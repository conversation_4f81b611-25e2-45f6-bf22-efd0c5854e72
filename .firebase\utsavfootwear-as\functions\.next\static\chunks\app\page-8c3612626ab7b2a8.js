(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1609:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6874,23)),Promise.resolve().then(i.bind(i,3584)),Promise.resolve().then(i.bind(i,8932))},3584:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});var a=i(5155),n=i(8274);function s(e){let{children:t,className:i="",delay:s=0,direction:r="up",duration:c=.6,disableScrollAnimation:o=!1}=e,u=()=>{switch(r){case"up":case"down":default:return{opacity:1,y:0};case"left":case"right":return{opacity:1,x:0}}};return(0,a.jsx)(n.P.div,{className:i,initial:(()=>{switch(r){case"up":default:return{opacity:0,y:50};case"down":return{opacity:0,y:-50};case"left":return{opacity:0,x:50};case"right":return{opacity:0,x:-50}}})(),animate:o?u():void 0,whileInView:o?void 0:u(),viewport:o?void 0:{once:!0,amount:.3},transition:{duration:c,delay:s,ease:"easeOut"},children:t})}},8932:(e,t,i)=>{"use strict";i.d(t,{default:()=>c});var a=i(5155),n=i(8274);let s={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},r={type:"tween",ease:"easeInOut",duration:.5};function c(e){let{children:t,className:i=""}=e;return(0,a.jsx)(n.P.div,{className:i,initial:"initial",animate:"in",exit:"out",variants:s,transition:r,children:t})}}},e=>{e.O(0,[274,874,441,964,358],()=>e(e.s=1609)),_N_E=e.O()}]);