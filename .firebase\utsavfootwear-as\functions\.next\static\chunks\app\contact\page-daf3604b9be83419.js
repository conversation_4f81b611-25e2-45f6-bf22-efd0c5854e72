(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1929:(e,s,r)=>{Promise.resolve().then(r.bind(r,8983))},3584:(e,s,r)=>{"use strict";r.d(s,{default:()=>l});var a=r(5155),t=r(8274);function l(e){let{children:s,className:r="",delay:l=0,direction:n="up",duration:o=.6,disableScrollAnimation:i=!1}=e,c=()=>{switch(n){case"up":case"down":default:return{opacity:1,y:0};case"left":case"right":return{opacity:1,x:0}}};return(0,a.jsx)(t.P.div,{className:r,initial:(()=>{switch(n){case"up":default:return{opacity:0,y:50};case"down":return{opacity:0,y:-50};case"left":return{opacity:0,x:50};case"right":return{opacity:0,x:-50}}})(),animate:i?c():void 0,whileInView:i?void 0:c(),viewport:i?void 0:{once:!0,amount:.3},transition:{duration:o,delay:l,ease:"easeOut"},children:s})}},8932:(e,s,r)=>{"use strict";r.d(s,{default:()=>o});var a=r(5155),t=r(8274);let l={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},n={type:"tween",ease:"easeInOut",duration:.5};function o(e){let{children:s,className:r=""}=e;return(0,a.jsx)(t.P.div,{className:r,initial:"initial",animate:"in",exit:"out",variants:l,transition:n,children:s})}},8983:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(5155),t=r(2115),l=r(8932),n=r(3584);function o(){let[e,s]=(0,t.useState)({name:"",email:"",phone:"",subject:"",message:""}),[r,o]=(0,t.useState)(!1),[i,c]=(0,t.useState)("idle"),d=e=>{let{name:r,value:a}=e.target;s(e=>({...e,[r]:a}))},u=async r=>{r.preventDefault(),o(!0),c("idle");try{if(!(await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to send email");c("success"),s({name:"",email:"",phone:"",subject:"",message:""})}catch(e){console.error("Error sending email:",e),c("error")}finally{o(!1)}};return(0,a.jsx)(l.default,{className:"min-h-screen py-12",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(n.default,{className:"text-center mb-12",disableScrollAnimation:!0,children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-blue-300 mb-6",children:"Contact Us"}),(0,a.jsx)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto",children:"Have questions about our products or services? We'd love to hear from you. Get in touch with us today!"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)(n.default,{className:"bg-blue-50 rounded-lg shadow-lg p-8 hover-lift",delay:.2,children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),"success"===i&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Thank you for your message! We'll get back to you soon."}),"error"===i&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:"Sorry, there was an error sending your message. Please try again or contact us directly."}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:d,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:d,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:d,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"Your phone number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,a.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:d,required:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",children:[(0,a.jsx)("option",{value:"",disabled:!0,className:"text-gray-400",children:"Select a subject"}),(0,a.jsx)("option",{value:"product-inquiry",children:"Product Inquiry"}),(0,a.jsx)("option",{value:"store-hours",children:"Store Hours & Location"}),(0,a.jsx)("option",{value:"customer-service",children:"Customer Service"}),(0,a.jsx)("option",{value:"feedback",children:"Feedback"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,a.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:d,required:!0,rows:5,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"Tell us how we can help you..."})]}),(0,a.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:r?"Sending...":"Send Message"})]})]}),(0,a.jsxs)(n.default,{className:"space-y-8",delay:.4,direction:"right",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Get in Touch"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83C\uDFEA"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Visit Our Store"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Store location details on our about page.",(0,a.jsx)("br",{}),"Check our About page for updates."]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Why Contact Us?"}),(0,a.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"✓"}),"Product recommendations and sizing help"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"✓"}),"Store location and hours information"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"✓"}),"Special orders and requests"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"✓"}),"Customer service and support"]})]})]})]})]})]})})}}},e=>{e.O(0,[274,441,964,358],()=>e(e.s=1929)),_N_E=e.O()}]);