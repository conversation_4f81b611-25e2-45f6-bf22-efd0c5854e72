{"name": "@emailjs/browser", "version": "4.4.1", "description": "Official EmailJS SDK for browsers", "private": false, "author": "EmailJS", "contributors": ["<PERSON> <<EMAIL>> (https://www.emailjs.com)"], "homepage": "https://www.emailjs.com", "license": "BSD-3-<PERSON><PERSON>", "main": "cjs/index.js", "types": "es/index.d.ts", "module": "es/index.js", "repository": {"type": "git", "url": "https://github.com/emailjs-com/emailjs-sdk.git"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=14.0.0"}, "scripts": {"build:clean": "rm -rf es && rm -rf cjs && rm -rf dist", "build:version": "./set-version.sh $npm_package_version", "build:ts": "tsc --declaration --project ./tsconfig.build.json && npm run build:version es", "build:cjs": "tsc --module commonjs --outDir cjs --project ./tsconfig.build.json && npm run build:version cjs", "build:lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:bundle": "rollup -c", "build": "npm run build:clean && npm run build:lint && npm run build:ts && npm run build:cjs && npm run build:bundle", "test": "jest --coverage --no-cache", "lint": "tsc --noEmit && eslint src"}, "keywords": ["js email", "es6 email", "ts email", "javascript email", "typescript email", "emailjs", "email templates", "send email", "send email from js", "emailjs-com"], "devDependencies": {"@babel/preset-env": "^7.24.8", "@babel/preset-typescript": "^7.24.7", "@happy-dom/jest-environment": "^14.12.3", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "core-js": "^3.37.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "prettier": "^3.3.2", "rollup": "^4.18.1", "typescript": "^5.5.3"}, "prettier": {"trailingComma": "all", "singleQuote": true, "printWidth": 100}}