exports.id=573,exports.ids=[573],exports.modules={24078:(a,b,c)=>{"use strict";c.d(b,{GoogleAnalytics:()=>f});var d=c(60687);c(43210),c(16189);let e="G-XXXXXXXXXX";function f(){return(0,d.jsxs)(d.<PERSON>,{children:[(0,d.jsx)("script",{async:!0,src:`https://www.googletagmanager.com/gtag/js?id=${e}`}),(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${e}', {
              page_title: document.title,
              page_location: window.location.href,
            });
          `}})]})}},29190:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(60687),e=c(85814),f=c.n(e),g=c(30474),h=c(43210),i=c(92576),j=c(88920);function k(){let[a,b]=(0,h.useState)(!1);return(0,d.jsxs)(i.P.nav,{className:"bg-yellow-200 shadow-lg",initial:{y:-100},animate:{y:0},transition:{duration:.5,ease:"easeOut"},children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsx)(i.P.div,{className:"flex items-center pl--10",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:(0,d.jsx)(f(),{href:"/",className:"flex-shrink-0",children:(0,d.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{duration:.2},children:(0,d.jsx)(g.default,{src:"/Ustav Logo-03.png",alt:"Utsav Footwear",width:190,height:80,className:"pl--4",priority:!0})})})}),(0,d.jsx)(i.P.div,{className:"hidden md:flex items-center space-x-8",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.3},children:[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/products",label:"Products"},{href:"/contact",label:"Contact"}].map((a,b)=>(0,d.jsx)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.4+.1*b},children:(0,d.jsx)(f(),{href:a.href,className:"text-green-700 hover:text-black px-3 py-2 rounded-md text-lg font-bold border-b-2 border-transparent hover:border-green-700 transition-all duration-300 transform hover:scale-105",children:a.label})},a.href))}),(0,d.jsx)(i.P.div,{className:"md:hidden flex items-center",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.3},children:(0,d.jsxs)(i.P.button,{onClick:()=>{b(!a)},className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500",whileHover:{scale:1.1},whileTap:{scale:.9},transition:{duration:.2},children:[(0,d.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,d.jsx)(i.P.div,{animate:{rotate:180*!!a},transition:{duration:.3},children:a?(0,d.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):(0,d.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})})]})}),(0,d.jsx)(j.N,{children:a&&(0,d.jsx)(i.P.div,{className:"md:hidden",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeInOut"},children:(0,d.jsx)(i.P.div,{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",initial:{y:-20},animate:{y:0},exit:{y:-20},transition:{duration:.3},children:[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/products",label:"Products"},{href:"/contact",label:"Contact"}].map((a,c)=>(0,d.jsx)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2,delay:.1*c},children:(0,d.jsx)(f(),{href:a.href,className:"text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-all duration-200 hover:bg-gray-100 transform hover:translate-x-2",onClick:()=>b(!1),children:a.label})},a.href))})})})]})}},30004:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Data\\\\utsavfootwears\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Navbar.tsx","default")},34085:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,75116)),Promise.resolve().then(c.bind(c,30004)),Promise.resolve().then(c.bind(c,36105)),Promise.resolve().then(c.bind(c,65780))},36105:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Data\\\\utsavfootwears\\\\src\\\\components\\\\ScrollToTop.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\ScrollToTop.tsx","default")},47237:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,24078)),Promise.resolve().then(c.bind(c,29190)),Promise.resolve().then(c.bind(c,59431)),Promise.resolve().then(c.bind(c,52034))},52034:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(23877);function f(){let a=process.env.NEXT_PUBLIC_BUSINESS_PHONE_NO,b=encodeURIComponent("Hello, I want to know more about your products!"),c=`https://wa.me/${a}?text=${b}`;return(0,d.jsx)("a",{href:c,target:"_blank",rel:"noopener noreferrer",className:"fixed bottom-6 right-6 z-50 flex h-14 w-14 items-center justify-center rounded-full bg-green-500 text-white shadow-lg transition-transform hover:scale-110 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2","aria-label":"Chat on WhatsApp",children:(0,d.jsx)(e.EcP,{className:"h-8 w-8"})})}},52456:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},52499:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413),e=c(4536),f=c.n(e);let g={title:"Page Not Found - Utsav Footwear",description:"The page you're looking for doesn't exist. Browse our collection of premium Kolhapuri chappals or return to our homepage.",robots:{index:!1,follow:!1}};function h(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-md w-full text-center px-4",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"text-9xl font-bold text-gray-300",children:"404"}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Page Not Found"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the page you're looking for. Perhaps you'd like to browse our collection of premium Kolhapuri chappals?"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(f(),{href:"/",className:"block w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-300",children:"Go to Homepage"}),(0,d.jsx)(f(),{href:"/products",className:"block w-full border-2 border-yellow-400 text-yellow-600 hover:bg-yellow-400 hover:text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-300",children:"Browse Products"}),(0,d.jsx)(f(),{href:"/contact",className:"block w-full text-gray-600 hover:text-gray-900 font-medium py-2 transition-colors duration-300",children:"Contact Us"})]}),(0,d.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["Need help? ",(0,d.jsx)(f(),{href:"/contact",className:"text-yellow-600 hover:text-yellow-700",children:"Get in touch"})," with our team."]})})]})})}},59431:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(88920),f=c(92576),g=c(43210);function h(){let[a,b]=(0,g.useState)(!1);return(0,d.jsx)(e.N,{children:a&&(0,d.jsx)(f.P.button,{className:"fixed bottom-6 left-6 z-40 bg-gray-800 hover:bg-gray-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-xl",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},whileHover:{scale:1.1},whileTap:{scale:.9},transition:{duration:.2},children:(0,d.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})})})}},61135:()=>{},65608:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},65780:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Data\\\\utsavfootwears\\\\src\\\\components\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\WhatsAppButton.tsx","default")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},75116:(a,b,c)=>{"use strict";c.d(b,{GoogleAnalytics:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Analytics.tsx","GoogleAnalytics");(0,d.registerClientReference)(function(){throw Error("Attempted to call useGoogleAnalytics() from the server but useGoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Analytics.tsx","useGoogleAnalytics"),(0,d.registerClientReference)(function(){throw Error("Attempted to call trackEvent() from the server but trackEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Analytics.tsx","trackEvent"),(0,d.registerClientReference)(function(){throw Error("Attempted to call trackPageView() from the server but trackPageView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Analytics.tsx","trackPageView"),(0,d.registerClientReference)(function(){throw Error("Attempted to call trackConversion() from the server but trackConversion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\components\\Analytics.tsx","trackConversion")},89451:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))},98042:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r,metadata:()=>q});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);var i=c(30004),j=c(4536),k=c.n(j),l=c(17328);function m(){return(0,d.jsx)("footer",{className:"bg-gray-800 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto py-5 px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Utsav Footwear"}),(0,d.jsx)("p",{className:"text-gray-300 mb-4",children:"Your trusted destination for kolhapuri chappal's. We also offers comfortable, stylish, and durable footwear for every occasion."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Follow us on social media"}),(0,d.jsx)("ul",{className:"space-y-2",children:(0,d.jsx)("li",{children:(0,d.jsxs)(k(),{href:"https://www.instagram.com/utsav.footwear?igsh=MXJtZjJvZzdodm01bg==",className:"flex items-center gap-2 text-gray-300 hover:text-white transition-colors",children:[(0,d.jsx)(l.ao$,{className:"text-pink-500"}),"Instagram"]})})})]})]}),(0,d.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-700",children:(0,d.jsx)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:(0,d.jsxs)("p",{className:"text-gray-300 text-sm",children:["\xa9 ",new Date().getFullYear()," Utsav Footwear. All rights reserved."]})})})]})})}var n=c(36105),o=c(75116),p=c(65780);let q={title:{default:"Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",template:"%s | Utsav Footwear"},description:"Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids. Top Kolhapuri chappal shop in Kolhapur.",keywords:"footwear, shoes, quality shoes, comfortable shoes, stylish shoes, Utsav Footwear, kolhapuri chappals, kolhapur, Kolhapuri sandals, Buy Kolhapuri chappals, Handmade leather sandals, Top Kolhapuri chappal in kolhapur, Kolhapuri chappal market in Kolhapur, kolhapuri chappal for women, kolhapuri chappal for men, kolhapuri chappal for boys, kolhapuri chappal for girls, kolhapuri chappal for kids, kolhapuri chappal in kolhapur, kolhapuri chappals prada, कोल्हापुरी चप्पल, kolhapuri chappal shop near me",authors:[{name:"Utsav Footwear"}],creator:"Utsav Footwear",publisher:"Utsav Footwear",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:"https://utsavfootwear.com",siteName:"Utsav Footwear",title:"Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",description:"Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",images:[{url:"/Ustav Logo-03.png",width:1200,height:630,alt:"Utsav Footwear - Premium Kolhapuri Chappals"}]},twitter:{card:"summary_large_image",title:"Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",description:"Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",images:["/Ustav Logo-03.png"]},verification:{google:"your-google-verification-code"},alternates:{canonical:"https://utsavfootwear.com"},category:"shopping"};function r({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,d.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),(0,d.jsx)("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),(0,d.jsx)("link",{rel:"preload",as:"image",href:"/kolhapuribg.png"}),(0,d.jsx)("meta",{name:"theme-color",content:"#fbbf24"}),(0,d.jsx)("meta",{name:"msapplication-TileColor",content:"#fbbf24"}),(0,d.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"}),(0,d.jsx)(o.GoogleAnalytics,{})]}),(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{className:"flex-grow",children:a}),(0,d.jsx)(m,{}),(0,d.jsx)(n.default,{}),(0,d.jsx)(p.default,{})]})})]})}}};