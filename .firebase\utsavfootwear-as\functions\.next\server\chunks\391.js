exports.id=391,exports.ids=[391],exports.modules={2163:a=>{"use strict";var b=function(){this.clear()};b.prototype.ALLOWED_DUPLICATES=["set-cookie","set-cookie2","warning","www-authenticate"],b.prototype.clear=function(){this._sent={},this._lines=[]},b.prototype.set=function(a,b){if(void 0!==b){a=this._strip(a),b=this._strip(b);var c=a.toLowerCase();(!this._sent.hasOwnProperty(c)||this.ALLOWED_DUPLICATES.indexOf(c)>=0)&&(this._sent[c]=!0,this._lines.push(a+": "+b+"\r\n"))}},b.prototype.toString=function(){return this._lines.join("")},b.prototype._strip=function(a){return a.toString().replace(/^ */,"").replace(/ *$/,"")},a.exports=b},4015:(a,b,c)=>{"use strict";var d=c(7984).<PERSON><PERSON><PERSON>,e=c(55511),f=c(28354),g=c(63630),h=c(37196),i=c(47413),j=c(33595),k=function(a,b,c){if(h.apply(this,arguments),this._extensions=new g,this._stage=0,this._masking=this._options.masking,this._protocols=this._options.protocols||[],this._requireMasking=this._options.requireMasking,this._pingCallbacks={},"string"==typeof this._protocols&&(this._protocols=this._protocols.split(/ *, */)),this._request){var d=this._request.headers["sec-websocket-protocol"],e=this._protocols;void 0!==d&&("string"==typeof d&&(d=d.split(/ *, */)),this.protocol=d.filter(function(a){return e.indexOf(a)>=0})[0]),this.version="hybi-"+k.VERSION}};f.inherits(k,h),k.VERSION="13",k.mask=function(a,b,c){if(!b||0===b.length)return a;c=c||0;for(var d=0,e=a.length-c;d<e;d++)a[c+d]=a[c+d]^b[d%4];return a},k.generateAccept=function(a){var b=e.createHash("sha1");return b.update(a+k.GUID),b.digest("base64")},k.GUID="258EAFA5-E914-47DA-95CA-C5AB0DC85B11";var l={FIN:128,MASK:128,RSV1:64,RSV2:32,RSV3:16,OPCODE:15,LENGTH:127,OPCODES:{continuation:0,text:1,binary:2,close:8,ping:9,pong:10},OPCODE_CODES:[0,1,2,8,9,10],MESSAGE_OPCODES:[0,1,2],OPENING_OPCODES:[1,2],ERRORS:{normal_closure:1e3,going_away:1001,protocol_error:1002,unacceptable:1003,encoding_error:1007,policy_violation:1008,too_large:1009,extension_error:1010,unexpected_condition:1011},ERROR_CODES:[1e3,1001,1002,1003,1007,1008,1009,1010,1011],DEFAULT_ERROR_CODE:1e3,MIN_RESERVED_ERROR:3e3,MAX_RESERVED_ERROR:4999,UTF8_MATCH:/^([\x00-\x7F]|[\xC2-\xDF][\x80-\xBF]|\xE0[\xA0-\xBF][\x80-\xBF]|[\xE1-\xEC\xEE\xEF][\x80-\xBF]{2}|\xED[\x80-\x9F][\x80-\xBF]|\xF0[\x90-\xBF][\x80-\xBF]{2}|[\xF1-\xF3][\x80-\xBF]{3}|\xF4[\x80-\x8F][\x80-\xBF]{2})*$/,addExtension:function(a){return this._extensions.add(a),!0},parse:function(a){this._reader.put(a);for(var b=!0;b;)switch(this._stage){case 0:(b=this._reader.read(1))&&this._parseOpcode(b[0]);break;case 1:(b=this._reader.read(1))&&this._parseLength(b[0]);break;case 2:(b=this._reader.read(this._frame.lengthBytes))&&this._parseExtendedLength(b);break;case 3:(b=this._reader.read(4))&&(this._stage=4,this._frame.maskingKey=b);break;case 4:(b=this._reader.read(this._frame.length))&&(this._stage=0,this._emitFrame(b));break;default:b=null}},text:function(a){return!(this.readyState>1)&&this.frame(a,"text")},binary:function(a){return!(this.readyState>1)&&this.frame(a,"binary")},ping:function(a,b){return!(this.readyState>1)&&(a=a||"",b&&(this._pingCallbacks[a]=b),this.frame(a,"ping"))},pong:function(a){return!(this.readyState>1)&&(a=a||"",this.frame(a,"pong"))},close:function(a,b){return(a=a||"",b=b||this.ERRORS.normal_closure,this.readyState<=0)?(this.readyState=3,this.emit("close",new h.CloseEvent(b,a)),!0):1===this.readyState&&(this.readyState=2,this._extensions.close(function(){this.frame(a,"close",b)},this),!0)},frame:function(a,b,c){if(this.readyState<=0)return this._queue([a,b,c]);if(this.readyState>2)return!1;a instanceof Array&&(a=d.from(a)),"number"==typeof a&&(a=a.toString());var f,g,h=new j,k="string"==typeof a;h.rsv1=h.rsv2=h.rsv3=!1,h.opcode=this.OPCODES[b||(k?"text":"binary")],f=k?d.from(a,"utf8"):a,c&&(g=f,(f=d.allocUnsafe(2+g.length)).writeUInt16BE(c,0),g.copy(f,2)),h.data=f;var l=function(a){var b=new i;b.final=!0,b.rsv1=a.rsv1,b.rsv2=a.rsv2,b.rsv3=a.rsv3,b.opcode=a.opcode,b.masked=!!this._masking,b.length=a.data.length,b.payload=a.data,b.masked&&(b.maskingKey=e.randomBytes(4)),this._sendFrame(b)};return this.MESSAGE_OPCODES.indexOf(h.opcode)>=0?this._extensions.processOutgoingMessage(h,function(a,b){if(a)return this._fail("extension_error",a.message);l.call(this,b)},this):l.call(this,h),!0},_sendFrame:function(a){var b=a.length,c=b<=125?2:b<=65535?4:10,e=c+4*!!a.masked,f=d.allocUnsafe(e+b),g=a.masked?this.MASK:0;f[0]=(a.final?this.FIN:0)|(a.rsv1?this.RSV1:0)|(a.rsv2?this.RSV2:0)|(a.rsv3?this.RSV3:0)|a.opcode,b<=125?f[1]=g|b:b<=65535?(f[1]=126|g,f.writeUInt16BE(b,2)):(f[1]=127|g,f.writeUInt32BE(Math.floor(b/0x100000000),2),f.writeUInt32BE(b%0x100000000,6)),a.payload.copy(f,e),a.masked&&(a.maskingKey.copy(f,c),k.mask(f,a.maskingKey,e)),this._write(f)},_handshakeResponse:function(){var a=this._request.headers["sec-websocket-key"],b=this._request.headers["sec-websocket-version"];if(b!==k.VERSION)throw Error("Unsupported WebSocket version: "+b);if("string"!=typeof a)throw Error("Missing handshake request header: Sec-WebSocket-Key");this._headers.set("Upgrade","websocket"),this._headers.set("Connection","Upgrade"),this._headers.set("Sec-WebSocket-Accept",k.generateAccept(a)),this.protocol&&this._headers.set("Sec-WebSocket-Protocol",this.protocol);var c=this._extensions.generateResponse(this._request.headers["sec-websocket-extensions"]);c&&this._headers.set("Sec-WebSocket-Extensions",c);var e=["HTTP/1.1 101 Switching Protocols",this._headers.toString(),""];return d.from(e.join("\r\n"),"utf8")},_shutdown:function(a,b,c){delete this._frame,delete this._message,this._stage=5;var d=1===this.readyState;this.readyState=2,this._extensions.close(function(){d&&this.frame(b,"close",a),this.readyState=3,c&&this.emit("error",Error(b)),this.emit("close",new h.CloseEvent(a,b))},this)},_fail:function(a,b){this.readyState>1||this._shutdown(this.ERRORS[a],b,!0)},_parseOpcode:function(a){var b=[this.RSV1,this.RSV2,this.RSV3].map(function(b){return(a&b)===b}),c=this._frame=new i;return(c.final=(a&this.FIN)===this.FIN,c.rsv1=b[0],c.rsv2=b[1],c.rsv3=b[2],c.opcode=a&this.OPCODE,this._stage=1,this._extensions.validFrameRsv(c))?0>this.OPCODE_CODES.indexOf(c.opcode)?this._fail("protocol_error","Unrecognized frame opcode: "+c.opcode):0>this.MESSAGE_OPCODES.indexOf(c.opcode)&&!c.final?this._fail("protocol_error","Received fragmented control frame: opcode = "+c.opcode):this._message&&this.OPENING_OPCODES.indexOf(c.opcode)>=0?this._fail("protocol_error","Received new data frame but previous continuous frame is unfinished"):void 0:this._fail("protocol_error","One or more reserved bits are on: reserved1 = "+ +!!c.rsv1+", reserved2 = "+ +!!c.rsv2+", reserved3 = "+ +!!c.rsv3)},_parseLength:function(a){var b=this._frame;if(b.masked=(a&this.MASK)===this.MASK,b.length=a&this.LENGTH,b.length>=0&&b.length<=125){if(this._stage=b.masked?3:4,!this._checkFrameLength())return}else this._stage=2,b.lengthBytes=126===b.length?2:8;if(this._requireMasking&&!b.masked)return this._fail("unacceptable","Received unmasked frame but masking is required")},_parseExtendedLength:function(a){var b=this._frame;if(b.length=this._readUInt(a),this._stage=b.masked?3:4,0>this.MESSAGE_OPCODES.indexOf(b.opcode)&&b.length>125)return this._fail("protocol_error","Received control frame having too long payload: "+b.length);if(!this._checkFrameLength())return},_checkFrameLength:function(){return!((this._message?this._message.length:0)+this._frame.length>this._maxLength)||(this._fail("too_large","WebSocket frame length too large"),!1)},_emitFrame:function(a){var b,c,d,e,f,g=this._frame,i=g.payload=k.mask(a,g.maskingKey),l=g.opcode;if(delete this._frame,l===this.OPCODES.continuation){if(!this._message)return this._fail("protocol_error","Received unexpected continuation frame");this._message.pushFrame(g)}if((l===this.OPCODES.text||l===this.OPCODES.binary)&&(this._message=new j,this._message.pushFrame(g)),g.final&&this.MESSAGE_OPCODES.indexOf(l)>=0)return this._emitMessage(this._message);l===this.OPCODES.close&&(c=i.length>=2?i.readUInt16BE(0):null,d=i.length>2?this._encode(i.slice(2)):null,0!==i.length&&!(null!==c&&c>=this.MIN_RESERVED_ERROR&&c<=this.MAX_RESERVED_ERROR)&&0>this.ERROR_CODES.indexOf(c)&&(c=this.ERRORS.protocol_error),(i.length>125||i.length>2&&!d)&&(c=this.ERRORS.protocol_error),this._shutdown(c||this.DEFAULT_ERROR_CODE,d||"")),l===this.OPCODES.ping&&(this.frame(i,"pong"),this.emit("ping",new h.PingEvent(i.toString()))),l===this.OPCODES.pong&&(f=(e=this._pingCallbacks)[b=this._encode(i)],delete e[b],f&&f(),this.emit("pong",new h.PongEvent(i.toString())))},_emitMessage:function(a){var a=this._message;a.read(),delete this._message,this._extensions.processIncomingMessage(a,function(a,b){if(a)return this._fail("extension_error",a.message);var c=b.data;if(b.opcode===this.OPCODES.text&&(c=this._encode(c)),null===c)return this._fail("encoding_error","Could not decode a text frame as UTF-8");this.emit("message",new h.MessageEvent(c))},this)},_encode:function(a){try{var b=a.toString("binary",0,a.length);if(!this.UTF8_MATCH.test(b))return null}catch(a){}return a.toString("utf8",0,a.length)},_readUInt:function(a){return 2===a.length?a.readUInt16BE(0):0x100000000*a.readUInt32BE(0)+a.readUInt32BE(4)}};for(var m in l)k.prototype[m]=l[m];a.exports=k},7723:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=function(){this._queue=[],this._queueSize=0,this._offset=0};e.prototype.put=function(a){a&&0!==a.length&&(d.isBuffer(a)||(a=d.from(a)),this._queue.push(a),this._queueSize+=a.length)},e.prototype.read=function(a){if(a>this._queueSize)return null;if(0===a)return d.alloc(0);this._queueSize-=a;var b,c,e=this._queue,f=a,g=e[0];if(g.length>=a)if(g.length===a)return e.shift();else return c=g.slice(0,a),e[0]=g.slice(a),c;for(var h=0,i=e.length;h<i&&!(f<e[h].length);h++)f-=e[h].length;return b=e.splice(0,h),f>0&&e.length>0&&(b.push(e[0].slice(0,f)),e[0]=e[0].slice(f)),d.concat(b,a)},e.prototype.eachByte=function(a,b){for(var c,d,e;this._queue.length>0;){for(d=(c=this._queue[0]).length;this._offset<d;)e=this._offset,this._offset+=1,a.call(b,c[e]);this._offset=0,this._queue.shift()}},a.exports=e},7984:(a,b,c)=>{var d=c(79428),e=d.Buffer;function f(a,b){for(var c in a)b[c]=a[c]}function g(a,b,c){return e(a,b,c)}e.from&&e.alloc&&e.allocUnsafe&&e.allocUnsafeSlow?a.exports=d:(f(d,b),b.Buffer=g),g.prototype=Object.create(e.prototype),f(e,g),g.from=function(a,b,c){if("number"==typeof a)throw TypeError("Argument must not be a number");return e(a,b,c)},g.alloc=function(a,b,c){if("number"!=typeof a)throw TypeError("Argument must be a number");var d=e(a);return void 0!==b?"string"==typeof c?d.fill(b,c):d.fill(b):d.fill(0),d},g.allocUnsafe=function(a){if("number"!=typeof a)throw TypeError("Argument must be a number");return e(a)},g.allocUnsafeSlow=function(a){if("number"!=typeof a)throw TypeError("Argument must be a number");return d.SlowBuffer(a)}},13634:(a,b,c)=>{"use strict";var d;c.d(b,{$b:()=>d,Vy:()=>j});let e=[];!function(a){a[a.DEBUG=0]="DEBUG",a[a.VERBOSE=1]="VERBOSE",a[a.INFO=2]="INFO",a[a.WARN=3]="WARN",a[a.ERROR=4]="ERROR",a[a.SILENT=5]="SILENT"}(d||(d={}));let f={debug:d.DEBUG,verbose:d.VERBOSE,info:d.INFO,warn:d.WARN,error:d.ERROR,silent:d.SILENT},g=d.INFO,h={[d.DEBUG]:"log",[d.VERBOSE]:"log",[d.INFO]:"info",[d.WARN]:"warn",[d.ERROR]:"error"},i=(a,b,...c)=>{if(b<a.logLevel)return;let d=new Date().toISOString(),e=h[b];if(e)console[e](`[${d}]  ${a.name}:`,...c);else throw Error(`Attempted to log a message with an invalid logType (value: ${b})`)};class j{constructor(a){this.name=a,this._logLevel=g,this._logHandler=i,this._userLogHandler=null,e.push(this)}get logLevel(){return this._logLevel}set logLevel(a){if(!(a in d))throw TypeError(`Invalid value "${a}" assigned to \`logLevel\``);this._logLevel=a}setLogLevel(a){this._logLevel="string"==typeof a?f[a]:a}get logHandler(){return this._logHandler}set logHandler(a){if("function"!=typeof a)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=a}get userLogHandler(){return this._userLogHandler}set userLogHandler(a){this._userLogHandler=a}debug(...a){this._userLogHandler&&this._userLogHandler(this,d.DEBUG,...a),this._logHandler(this,d.DEBUG,...a)}log(...a){this._userLogHandler&&this._userLogHandler(this,d.VERBOSE,...a),this._logHandler(this,d.VERBOSE,...a)}info(...a){this._userLogHandler&&this._userLogHandler(this,d.INFO,...a),this._logHandler(this,d.INFO,...a)}warn(...a){this._userLogHandler&&this._userLogHandler(this,d.WARN,...a),this._logHandler(this,d.WARN,...a)}error(...a){this._userLogHandler&&this._userLogHandler(this,d.ERROR,...a),this._logHandler(this,d.ERROR,...a)}}},15048:(a,b,c)=>{"use strict";let d,e;c.d(b,{MF:()=>J,j6:()=>F,xZ:()=>G,om:()=>E,Sx:()=>L,Wp:()=>K,KO:()=>M});var f=c(89495),g=c(13634),h=c(52122);let i=new WeakMap,j=new WeakMap,k=new WeakMap,l=new WeakMap,m=new WeakMap,n={get(a,b,c){if(a instanceof IDBTransaction){if("done"===b)return j.get(a);if("objectStoreNames"===b)return a.objectStoreNames||k.get(a);if("store"===b)return c.objectStoreNames[1]?void 0:c.objectStore(c.objectStoreNames[0])}return o(a[b])},set:(a,b,c)=>(a[b]=c,!0),has:(a,b)=>a instanceof IDBTransaction&&("done"===b||"store"===b)||b in a};function o(a){if(a instanceof IDBRequest){let b=new Promise((b,c)=>{let d=()=>{a.removeEventListener("success",e),a.removeEventListener("error",f)},e=()=>{b(o(a.result)),d()},f=()=>{c(a.error),d()};a.addEventListener("success",e),a.addEventListener("error",f)});return b.then(b=>{b instanceof IDBCursor&&i.set(b,a)}).catch(()=>{}),m.set(b,a),b}if(l.has(a))return l.get(a);let b=function(a){if("function"==typeof a)return a!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(e||(e=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(a)?function(...b){return a.apply(p(this),b),o(i.get(this))}:function(...b){return o(a.apply(p(this),b))}:function(b,...c){let d=a.call(p(this),b,...c);return k.set(d,b.sort?b.sort():[b]),o(d)};return a instanceof IDBTransaction&&function(a){if(j.has(a))return;let b=new Promise((b,c)=>{let d=()=>{a.removeEventListener("complete",e),a.removeEventListener("error",f),a.removeEventListener("abort",f)},e=()=>{b(),d()},f=()=>{c(a.error||new DOMException("AbortError","AbortError")),d()};a.addEventListener("complete",e),a.addEventListener("error",f),a.addEventListener("abort",f)});j.set(a,b)}(a),(d||(d=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some(b=>a instanceof b)?new Proxy(a,n):a}(a);return b!==a&&(l.set(a,b),m.set(b,a)),b}let p=a=>m.get(a),q=["get","getKey","getAll","getAllKeys","count"],r=["put","add","delete","clear"],s=new Map;function t(a,b){if(!(a instanceof IDBDatabase&&!(b in a)&&"string"==typeof b))return;if(s.get(b))return s.get(b);let c=b.replace(/FromIndex$/,""),d=b!==c,e=r.includes(c);if(!(c in(d?IDBIndex:IDBObjectStore).prototype)||!(e||q.includes(c)))return;let f=async function(a,...b){let f=this.transaction(a,e?"readwrite":"readonly"),g=f.store;return d&&(g=g.index(b.shift())),(await Promise.all([g[c](...b),e&&f.done]))[0]};return s.set(b,f),f}n=(a=>({...a,get:(b,c,d)=>t(b,c)||a.get(b,c,d),has:(b,c)=>!!t(b,c)||a.has(b,c)}))(n);class u{constructor(a){this.container=a}getPlatformInfoString(){return this.container.getProviders().map(a=>{if(!function(a){let b=a.getComponent();return b?.type==="VERSION"}(a))return null;{let b=a.getImmediate();return`${b.library}/${b.version}`}}).filter(a=>a).join(" ")}}let v="@firebase/app",w="0.14.0",x=new g.Vy("@firebase/app"),y="[DEFAULT]",z={[v]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},A=new Map,B=new Map,C=new Map;function D(a,b){try{a.container.addComponent(b)}catch(c){x.debug(`Component ${b.name} failed to register with FirebaseApp ${a.name}`,c)}}function E(a){let b=a.name;if(C.has(b))return x.debug(`There were multiple attempts to register component ${b}.`),!1;for(let c of(C.set(b,a),A.values()))D(c,a);for(let b of B.values())D(b,a);return!0}function F(a,b){let c=a.container.getProvider("heartbeat").getImmediate({optional:!0});return c&&c.triggerHeartbeat(),a.container.getProvider(b)}function G(a){return null!=a&&void 0!==a.settings}let H=new h.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class I{constructor(a,b,c){this._isDeleted=!1,this._options={...a},this._config={...b},this._name=b.name,this._automaticDataCollectionEnabled=b.automaticDataCollectionEnabled,this._container=c,this.container.addComponent(new f.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(a){this.checkDestroyed(),this._automaticDataCollectionEnabled=a}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(a){this._isDeleted=a}checkDestroyed(){if(this.isDeleted)throw H.create("app-deleted",{appName:this._name})}}let J="12.0.0";function K(a,b={}){let c=a;"object"!=typeof b&&(b={name:b});let d={name:y,automaticDataCollectionEnabled:!0,...b},e=d.name;if("string"!=typeof e||!e)throw H.create("bad-app-name",{appName:String(e)});if(c||(c=(0,h.T9)()),!c)throw H.create("no-options");let g=A.get(e);if(g)if((0,h.bD)(c,g.options)&&(0,h.bD)(d,g.config))return g;else throw H.create("duplicate-app",{appName:e});let i=new f.h1(e);for(let a of C.values())i.addComponent(a);let j=new I(c,d,i);return A.set(e,j),j}function L(a=y){let b=A.get(a);if(!b&&a===y&&(0,h.T9)())return K();if(!b)throw H.create("no-app",{appName:a});return b}function M(a,b,c){let d=z[a]??a;c&&(d+=`-${c}`);let e=d.match(/\s|\//),g=b.match(/\s|\//);if(e||g){let a=[`Unable to register library "${d}" with version "${b}":`];e&&a.push(`library name "${d}" contains illegal characters (whitespace or "/")`),e&&g&&a.push("and"),g&&a.push(`version name "${b}" contains illegal characters (whitespace or "/")`),x.warn(a.join(" "));return}E(new f.uA(`${d}-version`,()=>({library:d,version:b}),"VERSION"))}let N="firebase-heartbeat-store",O=null;function P(){return O||(O=(function(a,b,{blocked:c,upgrade:d,blocking:e,terminated:f}={}){let g=indexedDB.open(a,1),h=o(g);return d&&g.addEventListener("upgradeneeded",a=>{d(o(g.result),a.oldVersion,a.newVersion,o(g.transaction),a)}),c&&g.addEventListener("blocked",a=>c(a.oldVersion,a.newVersion,a)),h.then(a=>{f&&a.addEventListener("close",()=>f()),e&&a.addEventListener("versionchange",a=>e(a.oldVersion,a.newVersion,a))}).catch(()=>{}),h})("firebase-heartbeat-database",0,{upgrade:(a,b)=>{if(0===b)try{a.createObjectStore(N)}catch(a){console.warn(a)}}}).catch(a=>{throw H.create("idb-open",{originalErrorMessage:a.message})})),O}async function Q(a){try{let b=(await P()).transaction(N),c=await b.objectStore(N).get(S(a));return await b.done,c}catch(a){if(a instanceof h.g)x.warn(a.message);else{let b=H.create("idb-get",{originalErrorMessage:a?.message});x.warn(b.message)}}}async function R(a,b){try{let c=(await P()).transaction(N,"readwrite"),d=c.objectStore(N);await d.put(b,S(a)),await c.done}catch(a){if(a instanceof h.g)x.warn(a.message);else{let b=H.create("idb-set",{originalErrorMessage:a?.message});x.warn(b.message)}}}function S(a){return`${a.name}!${a.options.appId}`}class T{constructor(a){this.container=a,this._heartbeatsCache=null;let b=this.container.getProvider("app").getImmediate();this._storage=new V(b),this._heartbeatsCachePromise=this._storage.read().then(a=>(this._heartbeatsCache=a,a))}async triggerHeartbeat(){try{let a=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),b=U();if(this._heartbeatsCache?.heartbeats==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null)||this._heartbeatsCache.lastSentHeartbeatDate===b||this._heartbeatsCache.heartbeats.some(a=>a.date===b))return;if(this._heartbeatsCache.heartbeats.push({date:b,agent:a}),this._heartbeatsCache.heartbeats.length>30){let a=function(a){if(0===a.length)return -1;let b=0,c=a[0].date;for(let d=1;d<a.length;d++)a[d].date<c&&(c=a[d].date,b=d);return b}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(a,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(a){x.warn(a)}}async getHeartbeatsHeader(){try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null||0===this._heartbeatsCache.heartbeats.length)return"";let a=U(),{heartbeatsToSend:b,unsentEntries:c}=function(a,b=1024){let c=[],d=a.slice();for(let e of a){let a=c.find(a=>a.agent===e.agent);if(a){if(a.dates.push(e.date),W(c)>b){a.dates.pop();break}}else if(c.push({agent:e.agent,dates:[e.date]}),W(c)>b){c.pop();break}d=d.slice(1)}return{heartbeatsToSend:c,unsentEntries:d}}(this._heartbeatsCache.heartbeats),d=(0,h.Uj)(JSON.stringify({version:2,heartbeats:b}));return this._heartbeatsCache.lastSentHeartbeatDate=a,c.length>0?(this._heartbeatsCache.heartbeats=c,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),d}catch(a){return x.warn(a),""}}}function U(){return new Date().toISOString().substring(0,10)}class V{constructor(a){this.app=a,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,h.zW)()&&(0,h.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let a=await Q(this.app);return a?.heartbeats?a:{heartbeats:[]}}}async overwrite(a){if(await this._canUseIndexedDBPromise){let b=await this.read();return R(this.app,{lastSentHeartbeatDate:a.lastSentHeartbeatDate??b.lastSentHeartbeatDate,heartbeats:a.heartbeats})}}async add(a){if(await this._canUseIndexedDBPromise){let b=await this.read();return R(this.app,{lastSentHeartbeatDate:a.lastSentHeartbeatDate??b.lastSentHeartbeatDate,heartbeats:[...b.heartbeats,...a.heartbeats]})}}}function W(a){return(0,h.Uj)(JSON.stringify({version:2,heartbeats:a})).length}E(new f.uA("platform-logger",a=>new u(a),"PRIVATE")),E(new f.uA("heartbeat",a=>new T(a),"PRIVATE")),M(v,w,""),M(v,w,"esm2020"),M("fire-js","")},19621:(a,b,c)=>{"use strict";var d=c(93841),e=c(70376),f=function(a){this._cells=a.map(function(a){return new d(a)}),this._stopped={incoming:!1,outgoing:!1}};f.prototype.processIncomingMessage=function(a,b,c){this._stopped.incoming||this._loop("incoming",this._cells.length-1,-1,-1,a,b,c)},f.prototype.processOutgoingMessage=function(a,b,c){this._stopped.outgoing||this._loop("outgoing",0,this._cells.length,1,a,b,c)},f.prototype.close=function(a,b){this._stopped={incoming:!0,outgoing:!0};var c=this._cells.map(function(a){return a.close()});a&&e.all(c).then(function(){a.call(b)})},f.prototype._loop=function(a,b,c,d,e,f,g){for(var h=this._cells,i=h.length,j=this;i--;)h[i].pending(a);var k=function(b,e,i){if(b===c)return f.call(g,e,i);h[b][a](e,i,function(c,e){c&&(j._stopped[a]=!0),k(b+d,c,e)})};k(b,null,e)},a.exports=f},23518:(a,b)=>{function c(a){if(void 0!==a&&a!==c.REQUEST&&a!==c.RESPONSE)throw Error("type must be REQUEST or RESPONSE");void 0===a||this.initialize(a),this.maxHeaderSize=c.maxHeaderSize}b.e=c,c.prototype.initialize=function(a,b){if(a!==c.REQUEST&&a!==c.RESPONSE)throw Error("type must be REQUEST or RESPONSE");this.type=a,this.state=a+"_LINE",this.info={headers:[],upgrade:!1},this.trailers=[],this.line="",this.isChunked=!1,this.connection="",this.headerSize=0,this.body_bytes=null,this.isUserCall=!1,this.hadError=!1},c.encoding="ascii",c.maxHeaderSize=81920,c.REQUEST="REQUEST",c.RESPONSE="RESPONSE";var d=c.kOnHeaders=1,e=c.kOnHeadersComplete=2,f=c.kOnBody=3,g=c.kOnMessageComplete=4;c.prototype[d]=c.prototype[e]=c.prototype[f]=c.prototype[g]=function(){};var h=!0;Object.defineProperty(c,"kOnExecute",{get:function(){return h=!1,99}});var i=c.methods=["DELETE","GET","HEAD","POST","PUT","CONNECT","OPTIONS","TRACE","COPY","LOCK","MKCOL","MOVE","PROPFIND","PROPPATCH","SEARCH","UNLOCK","BIND","REBIND","UNBIND","ACL","REPORT","MKACTIVITY","CHECKOUT","MERGE","M-SEARCH","NOTIFY","SUBSCRIBE","UNSUBSCRIBE","PATCH","PURGE","MKCALENDAR","LINK","UNLINK","SOURCE"],j=i.indexOf("CONNECT");c.prototype.reinitialize=c,c.prototype.close=c.prototype.pause=c.prototype.resume=c.prototype.remove=c.prototype.free=function(){},c.prototype._compatMode0_11=!1,c.prototype.getAsyncId=function(){return 0};var k={REQUEST_LINE:!0,RESPONSE_LINE:!0,HEADER:!0};c.prototype.execute=function(a,b,d){if(!(this instanceof c))throw TypeError("not a HTTPParser");b=b||0,d="number"==typeof d?d:a.length,this.chunk=a,this.offset=b;var e=this.end=b+d;try{for(;this.offset<e&&!this[this.state](););}catch(a){if(this.isUserCall)throw a;return this.hadError=!0,a}return(this.chunk=null,d=this.offset-b,k[this.state]&&(this.headerSize+=d,this.headerSize>(this.maxHeaderSize||c.maxHeaderSize)))?Error("max header size exceeded"):d};var l={REQUEST_LINE:!0,RESPONSE_LINE:!0,BODY_RAW:!0};c.prototype.finish=function(){if(!this.hadError){if(!l[this.state])return Error("invalid state for EOF");"BODY_RAW"===this.state&&this.userCall()(this[g]())}},c.prototype.consume=c.prototype.unconsume=c.prototype.getCurrentBuffer=function(){},c.prototype.userCall=function(){this.isUserCall=!0;var a=this;return function(b){return a.isUserCall=!1,b}},c.prototype.nextRequest=function(){this.userCall()(this[g]()),this.reinitialize(this.type)},c.prototype.consumeLine=function(){for(var a=this.end,b=this.chunk,d=this.offset;d<a;d++)if(10===b[d]){var e=this.line+b.toString(c.encoding,this.offset,d);return"\r"===e.charAt(e.length-1)&&(e=e.substr(0,e.length-1)),this.line="",this.offset=d+1,e}this.line+=b.toString(c.encoding,this.offset,this.end),this.offset=this.end};var m=/^([^: \t]+):[ \t]*((?:.*[^ \t])|)/,n=/^[ \t]+(.*[^ \t])/;c.prototype.parseHeader=function(a,b){if(-1!==a.indexOf("\r"))throw q("HPE_LF_EXPECTED");var c=m.exec(a),d=c&&c[1];if(d)b.push(d),b.push(c[2]);else{var e=n.exec(a);e&&b.length&&(b[b.length-1]&&(b[b.length-1]+=" "),b[b.length-1]+=e[1])}};var o=/^([A-Z-]+) ([^ ]+) HTTP\/(\d)\.(\d)$/;c.prototype.REQUEST_LINE=function(){var a=this.consumeLine();if(a){var b=o.exec(a);if(null===b)throw q("HPE_INVALID_CONSTANT");if(this.info.method=this._compatMode0_11?b[1]:i.indexOf(b[1]),-1===this.info.method)throw Error("invalid request method");this.info.url=b[2],this.info.versionMajor=+b[3],this.info.versionMinor=+b[4],this.body_bytes=0,this.state="HEADER"}};var p=/^HTTP\/(\d)\.(\d) (\d{3}) ?(.*)$/;function q(a){var b=Error("Parse Error");return b.code=a,b}c.prototype.RESPONSE_LINE=function(){var a=this.consumeLine();if(a){var b=p.exec(a);if(null===b)throw q("HPE_INVALID_CONSTANT");this.info.versionMajor=+b[1],this.info.versionMinor=+b[2];var c=this.info.statusCode=+b[3];this.info.statusMessage=b[4],((c/100|0)==1||204===c||304===c)&&(this.body_bytes=0),this.state="HEADER"}},c.prototype.shouldKeepAlive=function(){if(this.info.versionMajor>0&&this.info.versionMinor>0){if(-1!==this.connection.indexOf("close"))return!1}else if(-1===this.connection.indexOf("keep-alive"))return!1;return null!==this.body_bytes||!!this.isChunked},c.prototype.HEADER=function(){var a=this.consumeLine();if(void 0!==a){var b=this.info;if(a)this.parseHeader(a,b.headers);else{for(var d,f,g=b.headers,i=!1,k=!1,l=0;l<g.length;l+=2)switch(g[l].toLowerCase()){case"transfer-encoding":this.isChunked="chunked"===g[l+1].toLowerCase();break;case"content-length":if(d=+g[l+1],i){if(d!==this.body_bytes)throw q("HPE_UNEXPECTED_CONTENT_LENGTH")}else i=!0,this.body_bytes=d;break;case"connection":this.connection+=g[l+1].toLowerCase();break;case"upgrade":k=!0}if(this.isChunked&&i&&(i=!1,this.body_bytes=null),k&&-1!=this.connection.indexOf("upgrade")?b.upgrade=this.type===c.REQUEST||101===b.statusCode:b.upgrade=b.method===j,this.isChunked&&b.upgrade&&(this.isChunked=!1),b.shouldKeepAlive=this.shouldKeepAlive(),2===(f=h?this.userCall()(this[e](b)):this.userCall()(this[e](b.versionMajor,b.versionMinor,b.headers,b.method,b.url,b.statusCode,b.statusMessage,b.upgrade,b.shouldKeepAlive))))return this.nextRequest(),!0;if(this.isChunked&&!f)this.state="BODY_CHUNKHEAD";else{if(f||0===this.body_bytes)return this.nextRequest(),b.upgrade;null===this.body_bytes?this.state="BODY_RAW":this.state="BODY_SIZED"}}}},c.prototype.BODY_CHUNKHEAD=function(){var a=this.consumeLine();void 0!==a&&(this.body_bytes=parseInt(a,16),this.body_bytes?this.state="BODY_CHUNK":this.state="BODY_CHUNKTRAILERS")},c.prototype.BODY_CHUNK=function(){var a=Math.min(this.end-this.offset,this.body_bytes);this.userCall()(this[f](this.chunk.slice(this.offset,this.offset+a),0,a)),this.offset+=a,this.body_bytes-=a,this.body_bytes||(this.state="BODY_CHUNKEMPTYLINE")},c.prototype.BODY_CHUNKEMPTYLINE=function(){var a=this.consumeLine();if(void 0!==a){if(""!==a)throw Error("Expected empty line");this.state="BODY_CHUNKHEAD"}},c.prototype.BODY_CHUNKTRAILERS=function(){var a=this.consumeLine();void 0!==a&&(a?this.parseHeader(a,this.trailers):(this.trailers.length&&this.userCall()(this[d](this.trailers,"")),this.nextRequest()))},c.prototype.BODY_RAW=function(){this.userCall()(this[f](this.chunk.slice(this.offset,this.end),0,this.end-this.offset)),this.offset=this.end},c.prototype.BODY_SIZED=function(){var a=Math.min(this.end-this.offset,this.body_bytes);this.userCall()(this[f](this.chunk.slice(this.offset,this.offset+a),0,a)),this.offset+=a,this.body_bytes-=a,this.body_bytes||this.nextRequest()},["Headers","HeadersComplete","Body","MessageComplete"].forEach(function(a){var b=c["kOn"+a];Object.defineProperty(c.prototype,"on"+a,{get:function(){return this[b]},set:function(a){return this._compatMode0_11=!0,j="CONNECT",this[b]=a}})})},33595:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=function(){this.rsv1=!1,this.rsv2=!1,this.rsv3=!1,this.opcode=null,this.length=0,this._chunks=[]},f={read:function(){return this.data=this.data||d.concat(this._chunks,this.length)},pushFrame:function(a){this.rsv1=this.rsv1||a.rsv1,this.rsv2=this.rsv2||a.rsv2,this.rsv3=this.rsv3||a.rsv3,null===this.opcode&&(this.opcode=a.opcode),this._chunks.push(a.payload),this.length+=a.length}};for(var g in f)e.prototype[g]=f[g];a.exports=e},34364:(a,b,c)=>{"use strict";var d=c(28354),e=c(91645),f=c(34631),g=c(79551),h=c(74366),i=c(92139);c(64492);var j={"http:":80,"https:":443,"ws:":80,"wss:":443},k=["https:","wss:"],l=function(a,b,c){c=c||{},this.url=a,this._driver=h.client(this.url,{maxLength:c.maxLength,protocols:b}),["open","error"].forEach(function(a){this._driver.on(a,function(){s.headers=s._driver.headers,s.statusCode=s._driver.statusCode})},this);var d=c.proxy||{},l=g.parse(d.origin||this.url),m=l.port||j[l.protocol],n=k.indexOf(l.protocol)>=0,o=function(){s._onConnect()},p=c.net||{},q=c.tls||{},r=d.origin?d.tls||{}:q,s=this;p.host=r.host=l.hostname,p.port=r.port=m,q.ca=q.ca||c.ca,r.servername=r.servername||l.hostname,this._stream=n?f.connect(r,o):e.connect(p,o),d.origin&&this._configureProxy(d,q),i.call(this,c)};d.inherits(l,i),l.prototype._onConnect=function(){(this._proxy||this._driver).start()},l.prototype._configureProxy=function(a,b){var c,d=g.parse(this.url),e=k.indexOf(d.protocol)>=0,h=this;if(this._proxy=this._driver.proxy(a.origin),a.headers)for(c in a.headers)this._proxy.setHeader(c,a.headers[c]);this._proxy.pipe(this._stream,{end:!1}),this._stream.pipe(this._proxy),this._proxy.on("connect",function(){if(e){var a={socket:h._stream,servername:d.hostname};for(c in b)a[c]=b[c];h._stream=f.connect(a),h._configureStream()}h._driver.io.pipe(h._stream),h._stream.pipe(h._driver.io),h._driver.start()}),this._proxy.on("error",function(a){h._driver.emit("error",a)})},a.exports=l},37030:(a,b,c)=>{"use strict";var d=c(64492);a.exports={onopen:null,onmessage:null,onerror:null,onclose:null,addEventListener:function(a,b,c){this.on(a,b)},removeEventListener:function(a,b,c){this.removeListener(a,b)},dispatchEvent:function(a){a.target=a.currentTarget=this,a.eventPhase=d.AT_TARGET,this["on"+a.type]&&this["on"+a.type](a),this.emit(a.type,a)}}},37196:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=c(94735).EventEmitter,f=c(28354),g=c(57427),h=c(2163),i=c(7723),j=function(a,b,c){e.call(this),j.validateOptions(c||{},["maxLength","masking","requireMasking","protocols"]),this._request=a,this._reader=new i,this._options=c||{},this._maxLength=this._options.maxLength||this.MAX_LENGTH,this._headers=new h,this.__queue=[],this.readyState=0,this.url=b,this.io=new g.IO(this),this.messages=new g.Messages(this),this._bindEventListeners()};f.inherits(j,e),j.isWebSocket=function(a){var b=a.headers.connection||"",c=a.headers.upgrade||"";return"GET"===a.method&&b.toLowerCase().split(/ *, */).indexOf("upgrade")>=0&&"websocket"===c.toLowerCase()},j.validateOptions=function(a,b){for(var c in a)if(0>b.indexOf(c))throw Error("Unrecognized option: "+c)};var k={MAX_LENGTH:0x3ffffff,STATES:["connecting","open","closing","closed"],_bindEventListeners:function(){var a=this;this.messages.on("error",function(){}),this.on("message",function(b){var c=a.messages;c.readable&&c.emit("data",b.data)}),this.on("error",function(b){var c=a.messages;c.readable&&c.emit("error",b)}),this.on("close",function(){var b=a.messages;b.readable&&(b.readable=b.writable=!1,b.emit("end"))})},getState:function(){return this.STATES[this.readyState]||null},addExtension:function(a){return!1},setHeader:function(a,b){return!(this.readyState>0)&&(this._headers.set(a,b),!0)},start:function(){var a;if(0!==this.readyState)return!1;if(!j.isWebSocket(this._request))return this._failHandshake(Error("Not a WebSocket request"));try{a=this._handshakeResponse()}catch(a){return this._failHandshake(a)}return this._write(a),-1!==this._stage&&this._open(),!0},_failHandshake:function(a){var b=new h;return b.set("Content-Type","text/plain"),b.set("Content-Length",d.byteLength(a.message,"utf8")),b=["HTTP/1.1 400 Bad Request",b.toString(),a.message],this._write(d.from(b.join("\r\n"),"utf8")),this._fail("protocol_error",a.message),!1},text:function(a){return this.frame(a)},binary:function(a){return!1},ping:function(){return!1},pong:function(){return!1},close:function(a,b){return 1===this.readyState&&(this.readyState=3,this.emit("close",new j.CloseEvent(null,null)),!0)},_open:function(){this.readyState=1,this.__queue.forEach(function(a){this.frame.apply(this,a)},this),this.__queue=[],this.emit("open",new j.OpenEvent)},_queue:function(a){return this.__queue.push(a),!0},_write:function(a){var b=this.io;b.readable&&b.emit("data",a)},_fail:function(a,b){this.readyState=2,this.emit("error",Error(b)),this.close()}};for(var l in k)j.prototype[l]=k[l];j.ConnectEvent=function(){},j.OpenEvent=function(){},j.CloseEvent=function(a,b){this.code=a,this.reason=b},j.MessageEvent=function(a){this.data=a},j.PingEvent=function(a){this.data=a},j.PongEvent=function(a){this.data=a},a.exports=j},43082:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=c(37196),f=c(28354),g=function(a,b,c){e.apply(this,arguments),this._stage=0,this.version="hixie-75",this._headers.set("Upgrade","WebSocket"),this._headers.set("Connection","Upgrade"),this._headers.set("WebSocket-Origin",this._request.headers.origin),this._headers.set("WebSocket-Location",this.url)};f.inherits(g,e);var h={close:function(){return 3!==this.readyState&&(this.readyState=3,this.emit("close",new e.CloseEvent(null,null)),!0)},parse:function(a){this.readyState>1||(this._reader.put(a),this._reader.eachByte(function(a){var b;switch(this._stage){case -1:this._body.push(a),this._sendHandshakeBody();break;case 0:this._parseLeadingByte(a);break;case 1:if(this._length=(127&a)+128*this._length,this._closing&&0===this._length)return this.close();(128&a)!=128&&(0===this._length?this._stage=0:(this._skipped=0,this._stage=2));break;case 2:if(255===a)this._stage=0,b=d.from(this._buffer).toString("utf8",0,this._buffer.length),this.emit("message",new e.MessageEvent(b));else if(this._length)this._skipped+=1,this._skipped===this._length&&(this._stage=0);else if(this._buffer.push(a),this._buffer.length>this._maxLength)return this.close()}},this))},frame:function(a){if(0===this.readyState)return this._queue([a]);if(this.readyState>1)return!1;"string"!=typeof a&&(a=a.toString());var b=d.byteLength(a),c=d.allocUnsafe(b+2);return c[0]=0,c.write(a,1),c[c.length-1]=255,this._write(c),!0},_handshakeResponse:function(){var a=["HTTP/1.1 101 Web Socket Protocol Handshake",this._headers.toString(),""];return d.from(a.join("\r\n"),"utf8")},_parseLeadingByte:function(a){(128&a)==128?(this._length=0,this._stage=1):(delete this._length,delete this._skipped,this._buffer=[],this._stage=2)}};for(var i in h)g.prototype[i]=h[i];a.exports=g},47413:a=>{"use strict";var b=function(){},c={final:!1,rsv1:!1,rsv2:!1,rsv3:!1,opcode:null,masked:!1,maskingKey:null,lengthBytes:1,length:0,payload:null};for(var d in c)b.prototype[d]=c[d];a.exports=b},52122:(a,b,c)=>{"use strict";c.d(b,{cY:()=>s,FA:()=>I,g:()=>H,gz:()=>X,vA:()=>e,Hk:()=>f,K3:()=>i,u:()=>m,KA:()=>k,Uj:()=>l,gR:()=>P,Fy:()=>v,tD:()=>Y,A4:()=>n,bD:()=>function a(b,c){if(b===c)return!0;let d=Object.keys(b),e=Object.keys(c);for(let f of d){if(!e.includes(f))return!1;let d=b[f],g=c[f];if(T(d)&&T(g)){if(!a(d,g))return!1}else if(d!==g)return!1}for(let a of e)if(!d.includes(a))return!1;return!0},dI:()=>_,hp:()=>W,T9:()=>r,Tj:()=>p,yU:()=>q,Ku:()=>ac,ZQ:()=>z,qc:()=>O,sr:()=>C,zJ:()=>t,c1:()=>B,Im:()=>R,zW:()=>F,jZ:()=>A,$g:()=>E,lV:()=>D,Cv:()=>N,$L:()=>K,kH:()=>S,gE:()=>u,Am:()=>U,I9:()=>V,yw:()=>Q,OE:()=>ab,kj:()=>aa,As:()=>L,P1:()=>y,eX:()=>G});let d={NODE_CLIENT:!1,NODE_ADMIN:!1,SDK_VERSION:"${JSCORE_VERSION}"},e=function(a,b){if(!a)throw f(b)},f=function(a){return Error("Firebase Database ("+d.SDK_VERSION+") INTERNAL ASSERT FAILED: "+a)},g=function(a){let b=[],c=0;for(let d=0;d<a.length;d++){let e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((64512&e)==55296&&d+1<a.length&&(64512&a.charCodeAt(d+1))==56320?(e=65536+((1023&e)<<10)+(1023&a.charCodeAt(++d)),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=63&e|128)}return b},h=function(a){let b=[],c=0,d=0;for(;c<a.length;){let e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){let f=a[c++];b[d++]=String.fromCharCode((31&e)<<6|63&f)}else if(e>239&&e<365){let f=a[c++],g=((7&e)<<18|(63&f)<<12|(63&a[c++])<<6|63&a[c++])-65536;b[d++]=String.fromCharCode(55296+(g>>10)),b[d++]=String.fromCharCode(56320+(1023&g))}else{let f=a[c++],g=a[c++];b[d++]=String.fromCharCode((15&e)<<12|(63&f)<<6|63&g)}}return b.join("")},i={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(a,b){if(!Array.isArray(a))throw Error("encodeByteArray takes an array as a parameter");this.init_();let c=b?this.byteToCharMapWebSafe_:this.byteToCharMap_,d=[];for(let b=0;b<a.length;b+=3){let e=a[b],f=b+1<a.length,g=f?a[b+1]:0,h=b+2<a.length,i=h?a[b+2]:0,j=e>>2,k=(3&e)<<4|g>>4,l=(15&g)<<2|i>>6,m=63&i;!h&&(m=64,f||(l=64)),d.push(c[j],c[k],c[l],c[m])}return d.join("")},encodeString(a,b){return this.HAS_NATIVE_SUPPORT&&!b?btoa(a):this.encodeByteArray(g(a),b)},decodeString(a,b){return this.HAS_NATIVE_SUPPORT&&!b?atob(a):h(this.decodeStringToByteArray(a,b))},decodeStringToByteArray(a,b){this.init_();let c=b?this.charToByteMapWebSafe_:this.charToByteMap_,d=[];for(let b=0;b<a.length;){let e=c[a.charAt(b++)],f=b<a.length?c[a.charAt(b)]:0,g=++b<a.length?c[a.charAt(b)]:64,h=++b<a.length?c[a.charAt(b)]:64;if(++b,null==e||null==f||null==g||null==h)throw new j;let i=e<<2|f>>4;if(d.push(i),64!==g){let a=f<<4&240|g>>2;if(d.push(a),64!==h){let a=g<<6&192|h;d.push(a)}}}return d},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let a=0;a<this.ENCODED_VALS.length;a++)this.byteToCharMap_[a]=this.ENCODED_VALS.charAt(a),this.charToByteMap_[this.byteToCharMap_[a]]=a,this.byteToCharMapWebSafe_[a]=this.ENCODED_VALS_WEBSAFE.charAt(a),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[a]]=a,a>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(a)]=a,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(a)]=a)}}};class j extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let k=function(a){let b=g(a);return i.encodeByteArray(b,!0)},l=function(a){return k(a).replace(/\./g,"")},m=function(a){try{return i.decodeString(a,!0)}catch(a){console.error("base64Decode failed: ",a)}return null};function n(a){return function a(b,c){if(!(c instanceof Object))return c;switch(c.constructor){case Date:return new Date(c.getTime());case Object:void 0===b&&(b={});break;case Array:b=[];break;default:return c}for(let d in c)c.hasOwnProperty(d)&&"__proto__"!==d&&(b[d]=a(b[d],c[d]));return b}(void 0,a)}let o=()=>{try{return function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__||(()=>{if("undefined"==typeof process||void 0===process.env)return;let a=process.env.__FIREBASE_DEFAULTS__;if(a)return JSON.parse(a)})()||(()=>{let a;if("undefined"==typeof document)return;try{a=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(a){return}let b=a&&m(a[1]);return b&&JSON.parse(b)})()}catch(a){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${a}`);return}},p=a=>o()?.emulatorHosts?.[a],q=a=>{let b=p(a);if(!b)return;let c=b.lastIndexOf(":");if(c<=0||c+1===b.length)throw Error(`Invalid host ${b} with no separate hostname and port!`);let d=parseInt(b.substring(c+1),10);return"["===b[0]?[b.substring(1,c-1),d]:[b.substring(0,c),d]},r=()=>o()?.config;class s{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((a,b)=>{this.resolve=a,this.reject=b})}wrapCallback(a){return(b,c)=>{b?this.reject(b):this.resolve(c),"function"==typeof a&&(this.promise.catch(()=>{}),1===a.length?a(b):a(b,c))}}}function t(a){try{return(a.startsWith("http://")||a.startsWith("https://")?new URL(a).hostname:a).endsWith(".cloudworkstations.dev")}catch{return!1}}async function u(a){return(await fetch(a,{credentials:"include"})).ok}function v(a,b){if(a.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let c=b||"demo-project",d=a.iat||0,e=a.sub||a.user_id;if(!e)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let f={iss:`https://securetoken.google.com/${c}`,aud:c,iat:d,exp:d+3600,auth_time:d,sub:e,user_id:e,firebase:{sign_in_provider:"custom",identities:{}},...a};return[l(JSON.stringify({alg:"none",type:"JWT"})),l(JSON.stringify(f)),""].join(".")}let w={},x=!1;function y(a,b){if("undefined"==typeof window||"undefined"==typeof document||!t(window.location.host)||w[a]===b||w[a]||x)return;function c(a){return`__firebase__banner__${a}`}w[a]=b;let d="__firebase__banner",e=function(){let a={prod:[],emulator:[]};for(let b of Object.keys(w))w[b]?a.emulator.push(b):a.prod.push(b);return a}().prod.length>0;function f(){let a,b,f=(a=document.getElementById(d),b=!1,a||((a=document.createElement("div")).setAttribute("id",d),b=!0),{created:b,element:a}),g=c("text"),h=document.getElementById(g)||document.createElement("span"),i=c("learnmore"),j=document.getElementById(i)||document.createElement("a"),k=c("preprendIcon"),l=document.getElementById(k)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(f.created){let a=f.element;a.style.display="flex",a.style.background="#7faaf0",a.style.position="fixed",a.style.bottom="5px",a.style.left="5px",a.style.padding=".5em",a.style.borderRadius="5px",a.style.alignItems="center",j.setAttribute("id",i),j.innerText="Learn more",j.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",j.setAttribute("target","__blank"),j.style.paddingLeft="5px",j.style.textDecoration="underline";let b=function(){let a=document.createElement("span");return a.style.cursor="pointer",a.style.marginLeft="16px",a.style.fontSize="24px",a.innerHTML=" &times;",a.onclick=()=>{x=!0;let a=document.getElementById(d);a&&a.remove()},a}();l.setAttribute("width","24"),l.setAttribute("id",k),l.setAttribute("height","24"),l.setAttribute("viewBox","0 0 24 24"),l.setAttribute("fill","none"),l.style.marginLeft="-6px",a.append(l,h,j,b),document.body.appendChild(a)}e?(h.innerText="Preview backend disconnected.",l.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(l.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,h.innerText="Preview backend running in this workspace."),h.setAttribute("id",g)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",f):f()}function z(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function A(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(z())}function B(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function C(){let a="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof a&&void 0!==a.id}function D(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function E(){return!0===d.NODE_CLIENT||!0===d.NODE_ADMIN}function F(){try{return"object"==typeof indexedDB}catch(a){return!1}}function G(){return new Promise((a,b)=>{try{let c=!0,d="validate-browser-context-for-indexeddb-analytics-module",e=self.indexedDB.open(d);e.onsuccess=()=>{e.result.close(),c||self.indexedDB.deleteDatabase(d),a(!0)},e.onupgradeneeded=()=>{c=!1},e.onerror=()=>{b(e.error?.message||"")}}catch(a){b(a)}})}class H extends Error{constructor(a,b,c){super(b),this.code=a,this.customData=c,this.name="FirebaseError",Object.setPrototypeOf(this,H.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,I.prototype.create)}}class I{constructor(a,b,c){this.service=a,this.serviceName=b,this.errors=c}create(a,...b){var c,d;let e=b[0]||{},f=`${this.service}/${a}`,g=this.errors[a],h=g?(c=g,d=e,c.replace(J,(a,b)=>{let c=d[b];return null!=c?String(c):`<${b}?>`})):"Error",i=`${this.serviceName}: ${h} (${f}).`;return new H(f,i,e)}}let J=/\{\$([^}]+)}/g;function K(a){return JSON.parse(a)}function L(a){return JSON.stringify(a)}let M=function(a){let b={},c={},d={},e="";try{let f=a.split(".");b=K(m(f[0])||""),c=K(m(f[1])||""),e=f[2],d=c.d||{},delete c.d}catch(a){}return{header:b,claims:c,data:d,signature:e}},N=function(a){let b=M(a).claims;return!!b&&"object"==typeof b&&b.hasOwnProperty("iat")},O=function(a){let b=M(a).claims;return"object"==typeof b&&!0===b.admin};function P(a,b){return Object.prototype.hasOwnProperty.call(a,b)}function Q(a,b){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:void 0}function R(a){for(let b in a)if(Object.prototype.hasOwnProperty.call(a,b))return!1;return!0}function S(a,b,c){let d={};for(let e in a)Object.prototype.hasOwnProperty.call(a,e)&&(d[e]=b.call(c,a[e],e,a));return d}function T(a){return null!==a&&"object"==typeof a}function U(a){let b=[];for(let[c,d]of Object.entries(a))Array.isArray(d)?d.forEach(a=>{b.push(encodeURIComponent(c)+"="+encodeURIComponent(a))}):b.push(encodeURIComponent(c)+"="+encodeURIComponent(d));return b.length?"&"+b.join("&"):""}function V(a){let b={};return a.replace(/^\?/,"").split("&").forEach(a=>{if(a){let[c,d]=a.split("=");b[decodeURIComponent(c)]=decodeURIComponent(d)}}),b}function W(a){let b=a.indexOf("?");if(!b)return"";let c=a.indexOf("#",b);return a.substring(b,c>0?c:void 0)}class X{constructor(){this.chain_=[],this.buf_=[],this.W_=[],this.pad_=[],this.inbuf_=0,this.total_=0,this.blockSize=64,this.pad_[0]=128;for(let a=1;a<this.blockSize;++a)this.pad_[a]=0;this.reset()}reset(){this.chain_[0]=0x67452301,this.chain_[1]=0xefcdab89,this.chain_[2]=0x98badcfe,this.chain_[3]=0x10325476,this.chain_[4]=0xc3d2e1f0,this.inbuf_=0,this.total_=0}compress_(a,b){let c,d;b||(b=0);let e=this.W_;if("string"==typeof a)for(let c=0;c<16;c++)e[c]=a.charCodeAt(b)<<24|a.charCodeAt(b+1)<<16|a.charCodeAt(b+2)<<8|a.charCodeAt(b+3),b+=4;else for(let c=0;c<16;c++)e[c]=a[b]<<24|a[b+1]<<16|a[b+2]<<8|a[b+3],b+=4;for(let a=16;a<80;a++){let b=e[a-3]^e[a-8]^e[a-14]^e[a-16];e[a]=b<<1|b>>>31}let f=this.chain_[0],g=this.chain_[1],h=this.chain_[2],i=this.chain_[3],j=this.chain_[4];for(let a=0;a<80;a++){a<40?a<20?(c=i^g&(h^i),d=0x5a827999):(c=g^h^i,d=0x6ed9eba1):a<60?(c=g&h|i&(g|h),d=0x8f1bbcdc):(c=g^h^i,d=0xca62c1d6);let b=(f<<5|f>>>27)+c+j+d+e[a]|0;j=i,i=h,h=g<<30|g>>>2,g=f,f=b}this.chain_[0]=this.chain_[0]+f|0,this.chain_[1]=this.chain_[1]+g|0,this.chain_[2]=this.chain_[2]+h|0,this.chain_[3]=this.chain_[3]+i|0,this.chain_[4]=this.chain_[4]+j|0}update(a,b){if(null==a)return;void 0===b&&(b=a.length);let c=b-this.blockSize,d=0,e=this.buf_,f=this.inbuf_;for(;d<b;){if(0===f)for(;d<=c;)this.compress_(a,d),d+=this.blockSize;if("string"==typeof a){for(;d<b;)if(e[f]=a.charCodeAt(d),++f,++d,f===this.blockSize){this.compress_(e),f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f===this.blockSize){this.compress_(e),f=0;break}}this.inbuf_=f,this.total_+=b}digest(){let a=[],b=8*this.total_;this.inbuf_<56?this.update(this.pad_,56-this.inbuf_):this.update(this.pad_,this.blockSize-(this.inbuf_-56));for(let a=this.blockSize-1;a>=56;a--)this.buf_[a]=255&b,b/=256;this.compress_(this.buf_);let c=0;for(let b=0;b<5;b++)for(let d=24;d>=0;d-=8)a[c]=this.chain_[b]>>d&255,++c;return a}}function Y(a,b){let c=new Z(a,b);return c.subscribe.bind(c)}class Z{constructor(a,b){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=b,this.task.then(()=>{a(this)}).catch(a=>{this.error(a)})}next(a){this.forEachObserver(b=>{b.next(a)})}error(a){this.forEachObserver(b=>{b.error(a)}),this.close(a)}complete(){this.forEachObserver(a=>{a.complete()}),this.close()}subscribe(a,b,c){let d;if(void 0===a&&void 0===b&&void 0===c)throw Error("Missing Observer.");void 0===(d=!function(a,b){if("object"!=typeof a||null===a)return!1;for(let c of b)if(c in a&&"function"==typeof a[c])return!0;return!1}(a,["next","error","complete"])?{next:a,error:b,complete:c}:a).next&&(d.next=$),void 0===d.error&&(d.error=$),void 0===d.complete&&(d.complete=$);let e=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?d.error(this.finalError):d.complete()}catch(a){}}),this.observers.push(d),e}unsubscribeOne(a){void 0!==this.observers&&void 0!==this.observers[a]&&(delete this.observers[a],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(a){if(!this.finalized)for(let b=0;b<this.observers.length;b++)this.sendOne(b,a)}sendOne(a,b){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[a])try{b(this.observers[a])}catch(a){"undefined"!=typeof console&&console.error&&console.error(a)}})}close(a){this.finalized||(this.finalized=!0,void 0!==a&&(this.finalError=a),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function $(){}function _(a,b){return`${a} failed: ${b} argument `}let aa=function(a){let b=[],c=0;for(let d=0;d<a.length;d++){let f=a.charCodeAt(d);if(f>=55296&&f<=56319){let b=f-55296;e(++d<a.length,"Surrogate pair missing trail surrogate."),f=65536+(b<<10)+(a.charCodeAt(d)-56320)}f<128?b[c++]=f:(f<2048?b[c++]=f>>6|192:(f<65536?b[c++]=f>>12|224:(b[c++]=f>>18|240,b[c++]=f>>12&63|128),b[c++]=f>>6&63|128),b[c++]=63&f|128)}return b},ab=function(a){let b=0;for(let c=0;c<a.length;c++){let d=a.charCodeAt(c);d<128?b++:d<2048?b+=2:d>=55296&&d<=56319?(b+=4,c++):b+=3}return b};function ac(a){return a&&a._delegate?a._delegate:a}d.NODE_CLIENT=!0},54482:(a,b,c)=>{"use strict";var d=c(28354),e=c(71392),f=c(37196),g=c(43082),h=c(83991),i=c(4015),j=function(a){f.call(this,null,null,a),this._http=new e("request")};d.inherits(j,f);var k={EVENTS:["open","message","error","close","ping","pong"],_bindEventListeners:function(){this.messages.on("error",function(){}),this.on("error",function(){})},parse:function(a){if(this._delegate)return this._delegate.parse(a);if(this._http.parse(a),this._http.isComplete()){this.method=this._http.method,this.url=this._http.url,this.headers=this._http.headers,this.body=this._http.body;var b=this;this._delegate=j.http(this,this._options),this._delegate.messages=this.messages,this._delegate.io=this.io,this._open(),this.EVENTS.forEach(function(a){this._delegate.on(a,function(c){b.emit(a,c)})},this),this.protocol=this._delegate.protocol,this.version=this._delegate.version,this.parse(this._http.body),this.emit("connect",new f.ConnectEvent)}},_open:function(){this.__queue.forEach(function(a){this._delegate[a[0]].apply(this._delegate,a[1])},this),this.__queue=[]}};for(var l in["addExtension","setHeader","start","frame","text","binary","ping","close"].forEach(function(a){k[a]=function(){return this._delegate?this._delegate[a].apply(this._delegate,arguments):(this.__queue.push([a,arguments]),!0)}}),k)j.prototype[l]=k[l];j.isSecureRequest=function(a){if(a.connection&&void 0!==a.connection.authorized||a.socket&&a.socket.secure)return!0;var b=a.headers;return!!b&&("on"===b.https||"on"===b["x-forwarded-ssl"]||"https"===b["x-forwarded-scheme"]||"https"===b["x-forwarded-proto"])},j.determineUrl=function(a){return(this.isSecureRequest(a)?"wss:":"ws:")+"//"+a.headers.host+a.url},j.http=function(a,b){void 0===(b=b||{}).requireMasking&&(b.requireMasking=!0);var c=a.headers,d=c["sec-websocket-version"],e=c["sec-websocket-key"],f=c["sec-websocket-key1"],j=c["sec-websocket-key2"],k=this.determineUrl(a);return d||e?new i(a,k,b):f||j?new h(a,k,b):new g(a,k,b)},a.exports=j},56858:(a,b,c)=>{"use strict";var d=c(27910).Stream,e=c(28354),f=c(74366),g=c(2163),h=c(92139),i=c(37030),j=c(64492),k=function(a,b,c){this.writable=!0,c=c||{},this._stream=b.socket,this._ping=c.ping||this.DEFAULT_PING,this._retry=c.retry||this.DEFAULT_RETRY;var d=f.isSecureRequest(a)?"https:":"http:";this.url=d+"//"+a.headers.host+a.url,this.lastEventId=a.headers["last-event-id"]||"",this.readyState=h.CONNECTING;var e=new g,i=this;if(c.headers)for(var j in c.headers)e.set(j,c.headers[j]);if(this._stream&&this._stream.writable){process.nextTick(function(){i._open()}),this._stream.setTimeout(0),this._stream.setNoDelay(!0);var k="HTTP/1.1 200 OK\r\nContent-Type: text/event-stream\r\nCache-Control: no-cache, no-store\r\nConnection: close\r\n"+e.toString()+"\r\nretry: "+Math.floor(1e3*this._retry)+"\r\n\r\n";this._write(k),this._stream.on("drain",function(){i.emit("drain")}),this._ping&&(this._pingTimer=setInterval(function(){i.ping()},1e3*this._ping)),["error","end"].forEach(function(a){i._stream.on(a,function(){i.close()})})}};e.inherits(k,d),k.isEventSource=function(a){return"GET"===a.method&&(a.headers.accept||"").split(/\s*,\s*/).indexOf("text/event-stream")>=0};var l={DEFAULT_PING:10,DEFAULT_RETRY:5,_write:function(a){if(!this.writable)return!1;try{return this._stream.write(a,"utf8")}catch(a){return!1}},_open:function(){if(this.readyState===h.CONNECTING){this.readyState=h.OPEN;var a=new j("open");a.initEvent("open",!1,!1),this.dispatchEvent(a)}},write:function(a){return this.send(a)},end:function(a){void 0!==a&&this.write(a),this.close()},send:function(a,b){if(this.readyState>h.OPEN)return!1;a=String(a).replace(/(\r\n|\r|\n)/g,"$1data: ");var c="";return(b=b||{}).event&&(c+="event: "+b.event+"\r\n"),b.id&&(c+="id: "+b.id+"\r\n"),c+="data: "+a+"\r\n\r\n",this._write(c)},ping:function(){return this._write(":\r\n\r\n")},close:function(){if(this.readyState>h.OPEN)return!1;this.readyState=h.CLOSED,this.writable=!1,this._pingTimer&&clearInterval(this._pingTimer),this._stream&&this._stream.end();var a=new j("close");return a.initEvent("close",!1,!1),this.dispatchEvent(a),!0}};for(var m in l)k.prototype[m]=l[m];for(var n in i)k.prototype[n]=i[n];a.exports=k},57427:(a,b,c)=>{"use strict";var d=c(27910).Stream,e=c(28354),f=function(a){this.readable=this.writable=!0,this._paused=!1,this._driver=a};e.inherits(f,d),f.prototype.pause=function(){this._paused=!0,this._driver.messages._paused=!0},f.prototype.resume=function(){this._paused=!1,this.emit("drain");var a=this._driver.messages;a._paused=!1,a.emit("drain")},f.prototype.write=function(a){return!!this.writable&&(this._driver.parse(a),!this._paused)},f.prototype.end=function(a){if(this.writable){void 0!==a&&this.write(a),this.writable=!1;var b=this._driver.messages;b.readable&&(b.readable=b.writable=!1,b.emit("end"))}},f.prototype.destroy=function(){this.end()};var g=function(a){this.readable=this.writable=!0,this._paused=!1,this._driver=a};e.inherits(g,d),g.prototype.pause=function(){this._driver.io._paused=!0},g.prototype.resume=function(){this._driver.io._paused=!1,this._driver.io.emit("drain")},g.prototype.write=function(a){return!!this.writable&&("string"==typeof a?this._driver.text(a):this._driver.binary(a),!this._paused)},g.prototype.end=function(a){void 0!==a&&this.write(a)},g.prototype.destroy=function(){},b.IO=f,b.Messages=g},61448:(a,b,c)=>{"use strict";c.d(b,{xI:()=>aW,hg:()=>aR,x9:()=>aQ,CI:()=>aS});var d=c(15048),e=c(52122),f=c(89495),g=c(13634);function h(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}let i=new e.FA("auth","Firebase",h()),j=new g.Vy("@firebase/auth");function k(a,...b){j.logLevel<=g.$b.ERROR&&j.error(`Auth (${d.MF}): ${a}`,...b)}function l(a,...b){throw p(a,...b)}function m(a,...b){return p(a,...b)}function n(a,b,c){let d={...h(),[b]:c};return new e.FA("auth","Firebase",d).create(b,{appName:a.name})}function o(a){return n(a,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function p(a,...b){if("string"!=typeof a){let c=b[0],d=[...b.slice(1)];return d[0]&&(d[0].appName=a.name),a._errorFactory.create(c,...d)}return i.create(a,...b)}function q(a,b,...c){if(!a)throw p(b,...c)}function r(a){let b="INTERNAL ASSERTION FAILED: "+a;throw k(b),Error(b)}function s(){return"undefined"!=typeof self&&self.location?.protocol||null}class t{constructor(a,b){this.shortDelay=a,this.longDelay=b,b>a||r("Short delay should be less than long delay!"),this.isMobile=(0,e.jZ)()||(0,e.lV)()}get(){return!("undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&("http:"===s()||"https:"===s()||(0,e.sr)()||"connection"in navigator))||navigator.onLine?this.isMobile?this.longDelay:this.shortDelay:Math.min(5e3,this.shortDelay)}}class u{static initialize(a,b,c){this.fetchImpl=a,b&&(this.headersImpl=b),c&&(this.responseImpl=c)}static fetch(){return this.fetchImpl?this.fetchImpl:"undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void r("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static headers(){return this.headersImpl?this.headersImpl:"undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void r("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static response(){return this.responseImpl?this.responseImpl:"undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void r("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}}let v={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},w=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],x=new t(3e4,6e4);function y(a,b){return a.tenantId&&!b.tenantId?{...b,tenantId:a.tenantId}:b}async function z(a,b,c,d,f={}){return A(a,f,async()=>{let f={},g={};d&&("GET"===b?g=d:f={body:JSON.stringify(d)});let h=(0,e.Am)({key:a.config.apiKey,...g}).slice(1),i=await a._getAdditionalHeaders();i["Content-Type"]="application/json",a.languageCode&&(i["X-Firebase-Locale"]=a.languageCode);let j={method:b,headers:i,...f};return(0,e.c1)()||(j.referrerPolicy="no-referrer"),a.emulatorConfig&&(0,e.zJ)(a.emulatorConfig.host)&&(j.credentials="include"),u.fetch()(await C(a,a.config.apiHost,c,h),j)})}async function A(a,b,c){a._canInitEmulator=!1;let d={...v,...b};try{let b=new D(a),e=await Promise.race([c(),b.promise]);b.clearNetworkTimeout();let f=await e.json();if("needConfirmation"in f)throw E(a,"account-exists-with-different-credential",f);if(e.ok&&!("errorMessage"in f))return f;{let[b,c]=(e.ok?f.errorMessage:f.error.message).split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===b)throw E(a,"credential-already-in-use",f);if("EMAIL_EXISTS"===b)throw E(a,"email-already-in-use",f);if("USER_DISABLED"===b)throw E(a,"user-disabled",f);let g=d[b]||b.toLowerCase().replace(/[_\s]+/g,"-");if(c)throw n(a,g,c);l(a,g)}}catch(b){if(b instanceof e.g)throw b;l(a,"network-request-failed",{message:String(b)})}}async function B(a,b,c,d,e={}){let f=await z(a,b,c,d,e);return"mfaPendingCredential"in f&&l(a,"multi-factor-auth-required",{_serverResponse:f}),f}async function C(a,b,c,d){let e=`${b}${c}?${d}`,f=a.config.emulator?function(a,b){var c;c=a.emulator,c||r("Emulator should always be set here");let{url:d}=a.emulator;return b?`${d}${b.startsWith("/")?b.slice(1):b}`:d}(a.config,e):`${a.config.apiScheme}://${e}`;return w.includes(c)&&(await a._persistenceManagerAvailable,"COOKIE"===a._getPersistenceType())?a._getPersistence()._getFinalTarget(f).toString():f}class D{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(a){this.auth=a,this.timer=null,this.promise=new Promise((a,b)=>{this.timer=setTimeout(()=>b(m(this.auth,"network-request-failed")),x.get())})}}function E(a,b,c){let d={appName:a.name};c.email&&(d.email=c.email),c.phoneNumber&&(d.phoneNumber=c.phoneNumber);let e=m(a,b,d);return e.customData._tokenResponse=c,e}function F(a){return void 0!==a&&void 0!==a.enterprise}class G{constructor(a){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===a.recaptchaKey)throw Error("recaptchaKey undefined");this.siteKey=a.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=a.recaptchaEnforcementState}getProviderEnforcementState(a){if(!this.recaptchaEnforcementState||0===this.recaptchaEnforcementState.length)return null;for(let b of this.recaptchaEnforcementState)if(b.provider&&b.provider===a)switch(b.enforcementState){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}return null}isProviderEnabled(a){return"ENFORCE"===this.getProviderEnforcementState(a)||"AUDIT"===this.getProviderEnforcementState(a)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function H(a,b){return z(a,"GET","/v2/recaptchaConfig",y(a,b))}async function I(a,b){return z(a,"POST","/v1/accounts:delete",b)}async function J(a,b){return z(a,"POST","/v1/accounts:lookup",b)}function K(a){if(a)try{let b=new Date(Number(a));if(!isNaN(b.getTime()))return b.toUTCString()}catch(a){}}async function L(a,b=!1){let c=(0,e.Ku)(a),d=await c.getIdToken(b),f=N(d);q(f&&f.exp&&f.auth_time&&f.iat,c.auth,"internal-error");let g="object"==typeof f.firebase?f.firebase:void 0,h=g?.sign_in_provider;return{claims:f,token:d,authTime:K(M(f.auth_time)),issuedAtTime:K(M(f.iat)),expirationTime:K(M(f.exp)),signInProvider:h||null,signInSecondFactor:g?.sign_in_second_factor||null}}function M(a){return 1e3*Number(a)}function N(a){let[b,c,d]=a.split(".");if(void 0===b||void 0===c||void 0===d)return k("JWT malformed, contained fewer than 3 sections"),null;try{let a=(0,e.u)(c);if(!a)return k("Failed to decode base64 JWT payload"),null;return JSON.parse(a)}catch(a){return k("Caught error parsing JWT payload as JSON",a?.toString()),null}}function O(a){let b=N(a);return q(b,"internal-error"),q(void 0!==b.exp,"internal-error"),q(void 0!==b.iat,"internal-error"),Number(b.exp)-Number(b.iat)}async function P(a,b,c=!1){if(c)return b;try{return await b}catch(b){throw b instanceof e.g&&function({code:a}){return"auth/user-disabled"===a||"auth/user-token-expired"===a}(b)&&a.auth.currentUser===a&&await a.auth.signOut(),b}}class Q{constructor(a){this.user=a,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId&&clearTimeout(this.timerId))}getInterval(a){if(!a)return this.errorBackoff=3e4,Math.max(0,(this.user.stsTokenManager.expirationTime??0)-Date.now()-3e5);{let a=this.errorBackoff;return this.errorBackoff=Math.min(2*this.errorBackoff,96e4),a}}schedule(a=!1){if(!this.isRunning)return;let b=this.getInterval(a);this.timerId=setTimeout(async()=>{await this.iteration()},b)}async iteration(){try{await this.user.getIdToken(!0)}catch(a){a?.code==="auth/network-request-failed"&&this.schedule(!0);return}this.schedule()}}class R{constructor(a,b){this.createdAt=a,this.lastLoginAt=b,this._initializeTime()}_initializeTime(){this.lastSignInTime=K(this.lastLoginAt),this.creationTime=K(this.createdAt)}_copy(a){this.createdAt=a.createdAt,this.lastLoginAt=a.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function S(a){var b,c;let d=a.auth,e=await a.getIdToken(),f=await P(a,J(d,{idToken:e}));q(f?.users.length,d,"internal-error");let g=f.users[0];a._notifyReloadListener(g);let h=g.providerUserInfo?.length?U(g.providerUserInfo):[],i=(b=a.providerData,c=h,[...b.filter(a=>!c.some(b=>b.providerId===a.providerId)),...c]),j=a.isAnonymous,k=!(a.email&&g.passwordHash)&&!i?.length;Object.assign(a,{uid:g.localId,displayName:g.displayName||null,photoURL:g.photoUrl||null,email:g.email||null,emailVerified:g.emailVerified||!1,phoneNumber:g.phoneNumber||null,tenantId:g.tenantId||null,providerData:i,metadata:new R(g.createdAt,g.lastLoginAt),isAnonymous:!!j&&k})}async function T(a){let b=(0,e.Ku)(a);await S(b),await b.auth._persistUserIfCurrent(b),b.auth._notifyListenersIfCurrent(b)}function U(a){return a.map(({providerId:a,...b})=>({providerId:a,uid:b.rawId||"",displayName:b.displayName||null,email:b.email||null,phoneNumber:b.phoneNumber||null,photoURL:b.photoUrl||null}))}async function V(a,b){let c=await A(a,{},async()=>{let c=(0,e.Am)({grant_type:"refresh_token",refresh_token:b}).slice(1),{tokenApiHost:d,apiKey:f}=a.config,g=await C(a,d,"/v1/token",`key=${f}`),h=await a._getAdditionalHeaders();h["Content-Type"]="application/x-www-form-urlencoded";let i={method:"POST",headers:h,body:c};return a.emulatorConfig&&(0,e.zJ)(a.emulatorConfig.host)&&(i.credentials="include"),u.fetch()(g,i)});return{accessToken:c.access_token,expiresIn:c.expires_in,refreshToken:c.refresh_token}}async function W(a,b){return z(a,"POST","/v2/accounts:revokeToken",y(a,b))}class X{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(a){q(a.idToken,"internal-error"),q(void 0!==a.idToken,"internal-error"),q(void 0!==a.refreshToken,"internal-error");let b="expiresIn"in a&&void 0!==a.expiresIn?Number(a.expiresIn):O(a.idToken);this.updateTokensAndExpiration(a.idToken,a.refreshToken,b)}updateFromIdToken(a){q(0!==a.length,"internal-error");let b=O(a);this.updateTokensAndExpiration(a,null,b)}async getToken(a,b=!1){return b||!this.accessToken||this.isExpired?(q(this.refreshToken,a,"user-token-expired"),this.refreshToken)?(await this.refresh(a,this.refreshToken),this.accessToken):null:this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(a,b){let{accessToken:c,refreshToken:d,expiresIn:e}=await V(a,b);this.updateTokensAndExpiration(c,d,Number(e))}updateTokensAndExpiration(a,b,c){this.refreshToken=b||null,this.accessToken=a||null,this.expirationTime=Date.now()+1e3*c}static fromJSON(a,b){let{refreshToken:c,accessToken:d,expirationTime:e}=b,f=new X;return c&&(q("string"==typeof c,"internal-error",{appName:a}),f.refreshToken=c),d&&(q("string"==typeof d,"internal-error",{appName:a}),f.accessToken=d),e&&(q("number"==typeof e,"internal-error",{appName:a}),f.expirationTime=e),f}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(a){this.accessToken=a.accessToken,this.refreshToken=a.refreshToken,this.expirationTime=a.expirationTime}_clone(){return Object.assign(new X,this.toJSON())}_performRefresh(){return r("not implemented")}}function Y(a,b){q("string"==typeof a||void 0===a,"internal-error",{appName:b})}class Z{constructor({uid:a,auth:b,stsTokenManager:c,...d}){this.providerId="firebase",this.proactiveRefresh=new Q(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=a,this.auth=b,this.stsTokenManager=c,this.accessToken=c.accessToken,this.displayName=d.displayName||null,this.email=d.email||null,this.emailVerified=d.emailVerified||!1,this.phoneNumber=d.phoneNumber||null,this.photoURL=d.photoURL||null,this.isAnonymous=d.isAnonymous||!1,this.tenantId=d.tenantId||null,this.providerData=d.providerData?[...d.providerData]:[],this.metadata=new R(d.createdAt||void 0,d.lastLoginAt||void 0)}async getIdToken(a){let b=await P(this,this.stsTokenManager.getToken(this.auth,a));return q(b,this.auth,"internal-error"),this.accessToken!==b&&(this.accessToken=b,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),b}getIdTokenResult(a){return L(this,a)}reload(){return T(this)}_assign(a){this!==a&&(q(this.uid===a.uid,this.auth,"internal-error"),this.displayName=a.displayName,this.photoURL=a.photoURL,this.email=a.email,this.emailVerified=a.emailVerified,this.phoneNumber=a.phoneNumber,this.isAnonymous=a.isAnonymous,this.tenantId=a.tenantId,this.providerData=a.providerData.map(a=>({...a})),this.metadata._copy(a.metadata),this.stsTokenManager._assign(a.stsTokenManager))}_clone(a){let b=new Z({...this,auth:a,stsTokenManager:this.stsTokenManager._clone()});return b.metadata._copy(this.metadata),b}_onReload(a){q(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=a,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(a){this.reloadListener?this.reloadListener(a):this.reloadUserInfo=a}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(a,b=!1){let c=!1;a.idToken&&a.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(a),c=!0),b&&await S(this),await this.auth._persistUserIfCurrent(this),c&&this.auth._notifyListenersIfCurrent(this)}async delete(){if((0,d.xZ)(this.auth.app))return Promise.reject(o(this.auth));let a=await this.getIdToken();return await P(this,I(this.auth,{idToken:a})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()}toJSON(){return{uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(a=>({...a})),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId,...this.metadata.toJSON(),apiKey:this.auth.config.apiKey,appName:this.auth.name}}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(a,b){let c=b.displayName??void 0,d=b.email??void 0,e=b.phoneNumber??void 0,f=b.photoURL??void 0,g=b.tenantId??void 0,h=b._redirectEventId??void 0,i=b.createdAt??void 0,j=b.lastLoginAt??void 0,{uid:k,emailVerified:l,isAnonymous:m,providerData:n,stsTokenManager:o}=b;q(k&&o,a,"internal-error");let p=X.fromJSON(this.name,o);q("string"==typeof k,a,"internal-error"),Y(c,a.name),Y(d,a.name),q("boolean"==typeof l,a,"internal-error"),q("boolean"==typeof m,a,"internal-error"),Y(e,a.name),Y(f,a.name),Y(g,a.name),Y(h,a.name),Y(i,a.name),Y(j,a.name);let r=new Z({uid:k,auth:a,email:d,emailVerified:l,displayName:c,isAnonymous:m,photoURL:f,phoneNumber:e,tenantId:g,stsTokenManager:p,createdAt:i,lastLoginAt:j});return n&&Array.isArray(n)&&(r.providerData=n.map(a=>({...a}))),h&&(r._redirectEventId=h),r}static async _fromIdTokenResponse(a,b,c=!1){let d=new X;d.updateFromServerResponse(b);let e=new Z({uid:b.localId,auth:a,stsTokenManager:d,isAnonymous:c});return await S(e),e}static async _fromGetAccountInfoResponse(a,b,c){let d=b.users[0];q(void 0!==d.localId,"internal-error");let e=void 0!==d.providerUserInfo?U(d.providerUserInfo):[],f=!(d.email&&d.passwordHash)&&!e?.length,g=new X;g.updateFromIdToken(c);let h=new Z({uid:d.localId,auth:a,stsTokenManager:g,isAnonymous:f});return Object.assign(h,{uid:d.localId,displayName:d.displayName||null,photoURL:d.photoUrl||null,email:d.email||null,emailVerified:d.emailVerified||!1,phoneNumber:d.phoneNumber||null,tenantId:d.tenantId||null,providerData:e,metadata:new R(d.createdAt,d.lastLoginAt),isAnonymous:!(d.email&&d.passwordHash)&&!e?.length}),h}}let $=new Map;function _(a){a instanceof Function||r("Expected a class definition");let b=$.get(a);return b?(b instanceof a||r("Instance stored in cache mismatched with class"),b):(b=new a,$.set(a,b),b)}class aa{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(a,b){this.storage[a]=b}async _get(a){let b=this.storage[a];return void 0===b?null:b}async _remove(a){delete this.storage[a]}_addListener(a,b){}_removeListener(a,b){}}function ab(a,b,c){return`firebase:${a}:${b}:${c}`}aa.type="NONE";class ac{constructor(a,b,c){this.persistence=a,this.auth=b,this.userKey=c;let{config:d,name:e}=this.auth;this.fullUserKey=ab(this.userKey,d.apiKey,e),this.fullPersistenceKey=ab("persistence",d.apiKey,e),this.boundEventHandler=b._onStorageEvent.bind(b),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(a){return this.persistence._set(this.fullUserKey,a.toJSON())}async getCurrentUser(){let a=await this.persistence._get(this.fullUserKey);if(!a)return null;if("string"==typeof a){let b=await J(this.auth,{idToken:a}).catch(()=>void 0);return b?Z._fromGetAccountInfoResponse(this.auth,b,a):null}return Z._fromJSON(this.auth,a)}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(a){if(this.persistence===a)return;let b=await this.getCurrentUser();if(await this.removeCurrentUser(),this.persistence=a,b)return this.setCurrentUser(b)}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(a,b,c="authUser"){if(!b.length)return new ac(_(aa),a,c);let d=(await Promise.all(b.map(async a=>{if(await a._isAvailable())return a}))).filter(a=>a),e=d[0]||_(aa),f=ab(c,a.config.apiKey,a.name),g=null;for(let c of b)try{let b=await c._get(f);if(b){let d;if("string"==typeof b){let c=await J(a,{idToken:b}).catch(()=>void 0);if(!c)break;d=await Z._fromGetAccountInfoResponse(a,c,b)}else d=Z._fromJSON(a,b);c!==e&&(g=d),e=c;break}}catch{}let h=d.filter(a=>a._shouldAllowMigration);return e._shouldAllowMigration&&h.length&&(e=h[0],g&&await e._set(f,g.toJSON()),await Promise.all(b.map(async a=>{if(a!==e)try{await a._remove(f)}catch{}}))),new ac(e,a,c)}}function ad(a){let b=a.toLowerCase();if(b.includes("opera/")||b.includes("opr/")||b.includes("opios/"))return"Opera";{if(function(a=(0,e.ZQ)()){return/iemobile/i.test(a)}(b))return"IEMobile";if(b.includes("msie")||b.includes("trident/"))return"IE";if(b.includes("edge/"))return"Edge";if(function(a=(0,e.ZQ)()){return/firefox\//i.test(a)}(b))return"Firefox";if(b.includes("silk/"))return"Silk";if(function(a=(0,e.ZQ)()){return/blackberry/i.test(a)}(b))return"Blackberry";if(function(a=(0,e.ZQ)()){return/webos/i.test(a)}(b))return"Webos";if(function(a=(0,e.ZQ)()){let b=a.toLowerCase();return b.includes("safari/")&&!b.includes("chrome/")&&!b.includes("crios/")&&!b.includes("android")}(b))return"Safari";if((b.includes("chrome/")||function(a=(0,e.ZQ)()){return/crios\//i.test(a)}(b))&&!b.includes("edge/"))return"Chrome";if(function(a=(0,e.ZQ)()){return/android/i.test(a)}(b))return"Android";let c=a.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);if(c?.length===2)return c[1]}return"Other"}function ae(a,b=[]){let c;switch(a){case"Browser":c=ad((0,e.ZQ)());break;case"Worker":c=`${ad((0,e.ZQ)())}-${a}`;break;default:c=a}let f=b.length?b.join(","):"FirebaseCore-web";return`${c}/JsCore/${d.MF}/${f}`}class af{constructor(a){this.auth=a,this.queue=[]}pushCallback(a,b){let c=b=>new Promise((c,d)=>{try{let d=a(b);c(d)}catch(a){d(a)}});c.onAbort=b,this.queue.push(c);let d=this.queue.length-1;return()=>{this.queue[d]=()=>Promise.resolve()}}async runMiddleware(a){if(this.auth.currentUser===a)return;let b=[];try{for(let c of this.queue)await c(a),c.onAbort&&b.push(c.onAbort)}catch(a){for(let a of(b.reverse(),b))try{a()}catch(a){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:a?.message})}}}async function ag(a,b={}){return z(a,"GET","/v2/passwordPolicy",y(a,b))}class ah{constructor(a){let b=a.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=b.minPasswordLength??6,b.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=b.maxPasswordLength),void 0!==b.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=b.containsLowercaseCharacter),void 0!==b.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=b.containsUppercaseCharacter),void 0!==b.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=b.containsNumericCharacter),void 0!==b.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=b.containsNonAlphanumericCharacter),this.enforcementState=a.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=a.allowedNonAlphanumericCharacters?.join("")??"",this.forceUpgradeOnSignin=a.forceUpgradeOnSignin??!1,this.schemaVersion=a.schemaVersion}validatePassword(a){let b={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(a,b),this.validatePasswordCharacterOptions(a,b),b.isValid&&(b.isValid=b.meetsMinPasswordLength??!0),b.isValid&&(b.isValid=b.meetsMaxPasswordLength??!0),b.isValid&&(b.isValid=b.containsLowercaseLetter??!0),b.isValid&&(b.isValid=b.containsUppercaseLetter??!0),b.isValid&&(b.isValid=b.containsNumericCharacter??!0),b.isValid&&(b.isValid=b.containsNonAlphanumericCharacter??!0),b}validatePasswordLengthOptions(a,b){let c=this.customStrengthOptions.minPasswordLength,d=this.customStrengthOptions.maxPasswordLength;c&&(b.meetsMinPasswordLength=a.length>=c),d&&(b.meetsMaxPasswordLength=a.length<=d)}validatePasswordCharacterOptions(a,b){let c;this.updatePasswordCharacterOptionsStatuses(b,!1,!1,!1,!1);for(let d=0;d<a.length;d++)c=a.charAt(d),this.updatePasswordCharacterOptionsStatuses(b,c>="a"&&c<="z",c>="A"&&c<="Z",c>="0"&&c<="9",this.allowedNonAlphanumericCharacters.includes(c))}updatePasswordCharacterOptionsStatuses(a,b,c,d,e){this.customStrengthOptions.containsLowercaseLetter&&(a.containsLowercaseLetter||(a.containsLowercaseLetter=b)),this.customStrengthOptions.containsUppercaseLetter&&(a.containsUppercaseLetter||(a.containsUppercaseLetter=c)),this.customStrengthOptions.containsNumericCharacter&&(a.containsNumericCharacter||(a.containsNumericCharacter=d)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(a.containsNonAlphanumericCharacter||(a.containsNonAlphanumericCharacter=e))}}class ai{constructor(a,b,c,d){this.app=a,this.heartbeatServiceProvider=b,this.appCheckServiceProvider=c,this.config=d,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new ak(this),this.idTokenSubscription=new ak(this),this.beforeStateQueue=new af(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=i,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=a.name,this.clientVersion=d.sdkClientVersion,this._persistenceManagerAvailable=new Promise(a=>this._resolvePersistenceManagerAvailable=a)}_initializeWithPersistence(a,b){return b&&(this._popupRedirectResolver=_(b)),this._initializationPromise=this.queue(async()=>{if(!this._deleted){if(this.persistenceManager=await ac.create(this,a),this._resolvePersistenceManagerAvailable?.(),!this._deleted){if(this._popupRedirectResolver?._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(a){}await this.initializeCurrentUser(b),this.lastNotifiedUid=this.currentUser?.uid||null,this._deleted||(this._isInitialized=!0)}}}),this._initializationPromise}async _onStorageEvent(){if(this._deleted)return;let a=await this.assertedPersistence.getCurrentUser();if(this.currentUser||a){if(this.currentUser&&a&&this.currentUser.uid===a.uid){this._currentUser._assign(a),await this.currentUser.getIdToken();return}await this._updateCurrentUser(a,!0)}}async initializeCurrentUserFromIdToken(a){try{let b=await J(this,{idToken:a}),c=await Z._fromGetAccountInfoResponse(this,b,a);await this.directlySetCurrentUser(c)}catch(a){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",a),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(a){if((0,d.xZ)(this.app)){let a=this.app.settings.authIdToken;return a?new Promise(b=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(a).then(b,b))}):this.directlySetCurrentUser(null)}let b=await this.assertedPersistence.getCurrentUser(),c=b,e=!1;if(a&&this.config.authDomain){await this.getOrInitRedirectPersistenceManager();let b=this.redirectUser?._redirectEventId,d=c?._redirectEventId,f=await this.tryRedirectSignIn(a);(!b||b===d)&&f?.user&&(c=f.user,e=!0)}if(!c)return this.directlySetCurrentUser(null);if(!c._redirectEventId){if(e)try{await this.beforeStateQueue.runMiddleware(c)}catch(a){c=b,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(a))}return c?this.reloadAndSetCurrentUserOrClear(c):this.directlySetCurrentUser(null)}return(q(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===c._redirectEventId)?this.directlySetCurrentUser(c):this.reloadAndSetCurrentUserOrClear(c)}async tryRedirectSignIn(a){let b=null;try{b=await this._popupRedirectResolver._completeRedirectFn(this,a,!0)}catch(a){await this._setRedirectUser(null)}return b}async reloadAndSetCurrentUserOrClear(a){try{await S(a)}catch(a){if(a?.code!=="auth/network-request-failed")return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(a)}useDeviceLanguage(){this.languageCode=function(){if("undefined"==typeof navigator)return null;let a=navigator;return a.languages&&a.languages[0]||a.language||null}()}async _delete(){this._deleted=!0}async updateCurrentUser(a){if((0,d.xZ)(this.app))return Promise.reject(o(this));let b=a?(0,e.Ku)(a):null;return b&&q(b.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(b&&b._clone(this))}async _updateCurrentUser(a,b=!1){if(!this._deleted)return a&&q(this.tenantId===a.tenantId,this,"tenant-id-mismatch"),b||await this.beforeStateQueue.runMiddleware(a),this.queue(async()=>{await this.directlySetCurrentUser(a),this.notifyAuthListeners()})}async signOut(){return(0,d.xZ)(this.app)?Promise.reject(o(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(a){return(0,d.xZ)(this.app)?Promise.reject(o(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(_(a))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(a){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();let b=this._getPasswordPolicyInternal();return b.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):b.validatePassword(a)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){let a=new ah(await ag(this));null===this.tenantId?this._projectPasswordPolicy=a:this._tenantPasswordPolicies[this.tenantId]=a}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(a){this._errorFactory=new e.FA("auth","Firebase",a())}onAuthStateChanged(a,b,c){return this.registerStateListener(this.authStateSubscription,a,b,c)}beforeAuthStateChanged(a,b){return this.beforeStateQueue.pushCallback(a,b)}onIdTokenChanged(a,b,c){return this.registerStateListener(this.idTokenSubscription,a,b,c)}authStateReady(){return new Promise((a,b)=>{if(this.currentUser)a();else{let c=this.onAuthStateChanged(()=>{c(),a()},b)}})}async revokeAccessToken(a){if(this.currentUser){let b={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:a,idToken:await this.currentUser.getIdToken()};null!=this.tenantId&&(b.tenantId=this.tenantId),await W(this,b)}}toJSON(){return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:this._currentUser?.toJSON()}}async _setRedirectUser(a,b){let c=await this.getOrInitRedirectPersistenceManager(b);return null===a?c.removeCurrentUser():c.setCurrentUser(a)}async getOrInitRedirectPersistenceManager(a){if(!this.redirectPersistenceManager){let b=a&&_(a)||this._popupRedirectResolver;q(b,this,"argument-error"),this.redirectPersistenceManager=await ac.create(this,[_(b._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()}return this.redirectPersistenceManager}async _redirectUserForId(a){return(this._isInitialized&&await this.queue(async()=>{}),this._currentUser?._redirectEventId===a)?this._currentUser:this.redirectUser?._redirectEventId===a?this.redirectUser:null}async _persistUserIfCurrent(a){if(a===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(a))}_notifyListenersIfCurrent(a){a===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){if(!this._isInitialized)return;this.idTokenSubscription.next(this.currentUser);let a=this.currentUser?.uid??null;this.lastNotifiedUid!==a&&(this.lastNotifiedUid=a,this.authStateSubscription.next(this.currentUser))}registerStateListener(a,b,c,d){if(this._deleted)return()=>{};let e="function"==typeof b?b:b.next.bind(b),f=!1,g=this._isInitialized?Promise.resolve():this._initializationPromise;if(q(g,this,"internal-error"),g.then(()=>{f||e(this.currentUser)}),"function"==typeof b){let e=a.addObserver(b,c,d);return()=>{f=!0,e()}}{let c=a.addObserver(b);return()=>{f=!0,c()}}}async directlySetCurrentUser(a){this.currentUser&&this.currentUser!==a&&this._currentUser._stopProactiveRefresh(),a&&this.isProactiveRefreshEnabled&&a._startProactiveRefresh(),this.currentUser=a,a?await this.assertedPersistence.setCurrentUser(a):await this.assertedPersistence.removeCurrentUser()}queue(a){return this.operations=this.operations.then(a,a),this.operations}get assertedPersistence(){return q(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(a){!a||this.frameworks.includes(a)||(this.frameworks.push(a),this.frameworks.sort(),this.clientVersion=ae(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){let a={"X-Client-Version":this.clientVersion};this.app.options.appId&&(a["X-Firebase-gmpid"]=this.app.options.appId);let b=await this.heartbeatServiceProvider.getImmediate({optional:!0})?.getHeartbeatsHeader();b&&(a["X-Firebase-Client"]=b);let c=await this._getAppCheckToken();return c&&(a["X-Firebase-AppCheck"]=c),a}async _getAppCheckToken(){if((0,d.xZ)(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let a=await this.appCheckServiceProvider.getImmediate({optional:!0})?.getToken();return a?.error&&function(a,...b){j.logLevel<=g.$b.WARN&&j.warn(`Auth (${d.MF}): ${a}`,...b)}(`Error while retrieving App Check token: ${a.error}`),a?.token}}function aj(a){return(0,e.Ku)(a)}class ak{constructor(a){this.auth=a,this.observer=null,this.addObserver=(0,e.tD)(a=>this.observer=a)}get next(){return q(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let al={async loadJS(){throw Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};class am{constructor(){this.enterprise=new an}ready(a){a()}execute(a,b){return Promise.resolve("token")}render(a,b){return""}}class an{ready(a){a()}execute(a,b){return Promise.resolve("token")}render(a,b){return""}}let ao="NO_RECAPTCHA";class ap{constructor(a){this.type="recaptcha-enterprise",this.auth=aj(a)}async verify(a="verify",b=!1){async function c(a){if(!b){if(null==a.tenantId&&null!=a._agentRecaptchaConfig)return a._agentRecaptchaConfig.siteKey;if(null!=a.tenantId&&void 0!==a._tenantRecaptchaConfigs[a.tenantId])return a._tenantRecaptchaConfigs[a.tenantId].siteKey}return new Promise(async(b,c)=>{H(a,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(d=>{if(void 0===d.recaptchaKey)c(Error("recaptcha Enterprise site key undefined"));else{let c=new G(d);return null==a.tenantId?a._agentRecaptchaConfig=c:a._tenantRecaptchaConfigs[a.tenantId]=c,b(c.siteKey)}}).catch(a=>{c(a)})})}function d(b,c,d){let e=window.grecaptcha;F(e)?e.enterprise.ready(()=>{e.enterprise.execute(b,{action:a}).then(a=>{c(a)}).catch(()=>{c(ao)})}):d(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?new am().execute("siteKey",{action:"verify"}):new Promise((a,e)=>{c(this.auth).then(c=>{if(!b&&F(window.grecaptcha))d(c,a,e);else{var f;if("undefined"==typeof window)return void e(Error("RecaptchaVerifier is only supported in browser"));let b=al.recaptchaEnterpriseScript;0!==b.length&&(b+=c),(f=b,al.loadJS(f)).then(()=>{d(c,a,e)}).catch(a=>{e(a)})}}).catch(a=>{e(a)})})}}async function aq(a,b,c,d=!1,e=!1){let f,g=new ap(a);if(e)f=ao;else try{f=await g.verify(c)}catch(a){f=await g.verify(c,!0)}let h={...b};if("mfaSmsEnrollment"===c||"mfaSmsSignIn"===c){if("phoneEnrollmentInfo"in h){let a=h.phoneEnrollmentInfo.phoneNumber,b=h.phoneEnrollmentInfo.recaptchaToken;Object.assign(h,{phoneEnrollmentInfo:{phoneNumber:a,recaptchaToken:b,captchaResponse:f,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})}else if("phoneSignInInfo"in h){let a=h.phoneSignInInfo.recaptchaToken;Object.assign(h,{phoneSignInInfo:{recaptchaToken:a,captchaResponse:f,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})}return h}return d?Object.assign(h,{captchaResp:f}):Object.assign(h,{captchaResponse:f}),Object.assign(h,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(h,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"}),h}async function ar(a,b,c,d,e){if("EMAIL_PASSWORD_PROVIDER"===e)if(!a._getRecaptchaConfig()?.isProviderEnabled("EMAIL_PASSWORD_PROVIDER"))return d(a,b).catch(async e=>{if("auth/missing-recaptcha-token"!==e.code)return Promise.reject(e);{console.log(`${c} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`);let e=await aq(a,b,c,"getOobCode"===c);return d(a,e)}});else{let e=await aq(a,b,c,"getOobCode"===c);return d(a,e)}if("PHONE_PROVIDER"!==e)return Promise.reject(e+" provider is not supported.");if(a._getRecaptchaConfig()?.isProviderEnabled("PHONE_PROVIDER")){let e=await aq(a,b,c);return d(a,e).catch(async e=>{if(a._getRecaptchaConfig()?.getProviderEnforcementState("PHONE_PROVIDER")==="AUDIT"&&("auth/missing-recaptcha-token"===e.code||"auth/invalid-app-credential"===e.code)){console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${c} flow.`);let e=await aq(a,b,c,!1,!0);return d(a,e)}return Promise.reject(e)})}{let e=await aq(a,b,c,!1,!0);return d(a,e)}}function as(a){let b=a.indexOf(":");return b<0?"":a.substr(0,b+1)}function at(a){if(!a)return null;let b=Number(a);return isNaN(b)?null:b}class au{constructor(a,b){this.providerId=a,this.signInMethod=b}toJSON(){return r("not implemented")}_getIdTokenResponse(a){return r("not implemented")}_linkToIdToken(a,b){return r("not implemented")}_getReauthenticationResolver(a){return r("not implemented")}}async function av(a,b){return z(a,"POST","/v1/accounts:signUp",b)}async function aw(a,b){return B(a,"POST","/v1/accounts:signInWithPassword",y(a,b))}async function ax(a,b){return B(a,"POST","/v1/accounts:signInWithEmailLink",y(a,b))}async function ay(a,b){return B(a,"POST","/v1/accounts:signInWithEmailLink",y(a,b))}class az extends au{constructor(a,b,c,d=null){super("password",c),this._email=a,this._password=b,this._tenantId=d}static _fromEmailAndPassword(a,b){return new az(a,b,"password")}static _fromEmailAndCode(a,b,c=null){return new az(a,b,"emailLink",c)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(a){let b="string"==typeof a?JSON.parse(a):a;if(b?.email&&b?.password){if("password"===b.signInMethod)return this._fromEmailAndPassword(b.email,b.password);else if("emailLink"===b.signInMethod)return this._fromEmailAndCode(b.email,b.password,b.tenantId)}return null}async _getIdTokenResponse(a){switch(this.signInMethod){case"password":return ar(a,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",aw,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return ax(a,{email:this._email,oobCode:this._password});default:l(a,"internal-error")}}async _linkToIdToken(a,b){switch(this.signInMethod){case"password":return ar(a,{idToken:b,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",av,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return ay(a,{idToken:b,email:this._email,oobCode:this._password});default:l(a,"internal-error")}}_getReauthenticationResolver(a){return this._getIdTokenResponse(a)}}async function aA(a,b){return B(a,"POST","/v1/accounts:signInWithIdp",y(a,b))}class aB extends au{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(a){let b=new aB(a.providerId,a.signInMethod);return a.idToken||a.accessToken?(a.idToken&&(b.idToken=a.idToken),a.accessToken&&(b.accessToken=a.accessToken),a.nonce&&!a.pendingToken&&(b.nonce=a.nonce),a.pendingToken&&(b.pendingToken=a.pendingToken)):a.oauthToken&&a.oauthTokenSecret?(b.accessToken=a.oauthToken,b.secret=a.oauthTokenSecret):l("argument-error"),b}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(a){let{providerId:b,signInMethod:c,...d}="string"==typeof a?JSON.parse(a):a;if(!b||!c)return null;let e=new aB(b,c);return e.idToken=d.idToken||void 0,e.accessToken=d.accessToken||void 0,e.secret=d.secret,e.nonce=d.nonce,e.pendingToken=d.pendingToken||null,e}_getIdTokenResponse(a){return aA(a,this.buildRequest())}_linkToIdToken(a,b){let c=this.buildRequest();return c.idToken=b,aA(a,c)}_getReauthenticationResolver(a){let b=this.buildRequest();return b.autoCreate=!1,aA(a,b)}buildRequest(){let a={requestUri:"http://localhost",returnSecureToken:!0};if(this.pendingToken)a.pendingToken=this.pendingToken;else{let b={};this.idToken&&(b.id_token=this.idToken),this.accessToken&&(b.access_token=this.accessToken),this.secret&&(b.oauth_token_secret=this.secret),b.providerId=this.providerId,this.nonce&&!this.pendingToken&&(b.nonce=this.nonce),a.postBody=(0,e.Am)(b)}return a}}class aC{constructor(a){let b=(0,e.I9)((0,e.hp)(a)),c=b.apiKey??null,d=b.oobCode??null,f=function(a){switch(a){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}(b.mode??null);q(c&&d&&f,"argument-error"),this.apiKey=c,this.operation=f,this.code=d,this.continueUrl=b.continueUrl??null,this.languageCode=b.lang??null,this.tenantId=b.tenantId??null}static parseLink(a){let b=function(a){let b=(0,e.I9)((0,e.hp)(a)).link,c=b?(0,e.I9)((0,e.hp)(b)).deep_link_id:null,d=(0,e.I9)((0,e.hp)(a)).deep_link_id;return(d?(0,e.I9)((0,e.hp)(d)).link:null)||d||c||b||a}(a);try{return new aC(b)}catch{return null}}}class aD{constructor(){this.providerId=aD.PROVIDER_ID}static credential(a,b){return az._fromEmailAndPassword(a,b)}static credentialWithLink(a,b){let c=aC.parseLink(b);return q(c,"argument-error"),az._fromEmailAndCode(a,c.code,c.tenantId)}}aD.PROVIDER_ID="password",aD.EMAIL_PASSWORD_SIGN_IN_METHOD="password",aD.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class aE{constructor(a){this.providerId=a,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(a){this.defaultLanguageCode=a}setCustomParameters(a){return this.customParameters=a,this}getCustomParameters(){return this.customParameters}}class aF extends aE{constructor(){super(...arguments),this.scopes=[]}addScope(a){return this.scopes.includes(a)||this.scopes.push(a),this}getScopes(){return[...this.scopes]}}class aG extends aF{constructor(){super("facebook.com")}static credential(a){return aB._fromParams({providerId:aG.PROVIDER_ID,signInMethod:aG.FACEBOOK_SIGN_IN_METHOD,accessToken:a})}static credentialFromResult(a){return aG.credentialFromTaggedObject(a)}static credentialFromError(a){return aG.credentialFromTaggedObject(a.customData||{})}static credentialFromTaggedObject({_tokenResponse:a}){if(!a||!("oauthAccessToken"in a)||!a.oauthAccessToken)return null;try{return aG.credential(a.oauthAccessToken)}catch{return null}}}aG.FACEBOOK_SIGN_IN_METHOD="facebook.com",aG.PROVIDER_ID="facebook.com";class aH extends aF{constructor(){super("google.com"),this.addScope("profile")}static credential(a,b){return aB._fromParams({providerId:aH.PROVIDER_ID,signInMethod:aH.GOOGLE_SIGN_IN_METHOD,idToken:a,accessToken:b})}static credentialFromResult(a){return aH.credentialFromTaggedObject(a)}static credentialFromError(a){return aH.credentialFromTaggedObject(a.customData||{})}static credentialFromTaggedObject({_tokenResponse:a}){if(!a)return null;let{oauthIdToken:b,oauthAccessToken:c}=a;if(!b&&!c)return null;try{return aH.credential(b,c)}catch{return null}}}aH.GOOGLE_SIGN_IN_METHOD="google.com",aH.PROVIDER_ID="google.com";class aI extends aF{constructor(){super("github.com")}static credential(a){return aB._fromParams({providerId:aI.PROVIDER_ID,signInMethod:aI.GITHUB_SIGN_IN_METHOD,accessToken:a})}static credentialFromResult(a){return aI.credentialFromTaggedObject(a)}static credentialFromError(a){return aI.credentialFromTaggedObject(a.customData||{})}static credentialFromTaggedObject({_tokenResponse:a}){if(!a||!("oauthAccessToken"in a)||!a.oauthAccessToken)return null;try{return aI.credential(a.oauthAccessToken)}catch{return null}}}aI.GITHUB_SIGN_IN_METHOD="github.com",aI.PROVIDER_ID="github.com";class aJ extends aF{constructor(){super("twitter.com")}static credential(a,b){return aB._fromParams({providerId:aJ.PROVIDER_ID,signInMethod:aJ.TWITTER_SIGN_IN_METHOD,oauthToken:a,oauthTokenSecret:b})}static credentialFromResult(a){return aJ.credentialFromTaggedObject(a)}static credentialFromError(a){return aJ.credentialFromTaggedObject(a.customData||{})}static credentialFromTaggedObject({_tokenResponse:a}){if(!a)return null;let{oauthAccessToken:b,oauthTokenSecret:c}=a;if(!b||!c)return null;try{return aJ.credential(b,c)}catch{return null}}}aJ.TWITTER_SIGN_IN_METHOD="twitter.com",aJ.PROVIDER_ID="twitter.com";class aK{constructor(a){this.user=a.user,this.providerId=a.providerId,this._tokenResponse=a._tokenResponse,this.operationType=a.operationType}static async _fromIdTokenResponse(a,b,c,d=!1){return new aK({user:await Z._fromIdTokenResponse(a,c,d),providerId:aL(c),_tokenResponse:c,operationType:b})}static async _forOperation(a,b,c){return await a._updateTokensIfNecessary(c,!0),new aK({user:a,providerId:aL(c),_tokenResponse:c,operationType:b})}}function aL(a){return a.providerId?a.providerId:"phoneNumber"in a?"phone":null}class aM extends e.g{constructor(a,b,c,d){super(b.code,b.message),this.operationType=c,this.user=d,Object.setPrototypeOf(this,aM.prototype),this.customData={appName:a.name,tenantId:a.tenantId??void 0,_serverResponse:b.customData._serverResponse,operationType:c}}static _fromErrorAndOperation(a,b,c,d){return new aM(a,b,c,d)}}async function aN(a,b,c=!1){if((0,d.xZ)(a.app))return Promise.reject(o(a));let e="signIn",f=await b._getIdTokenResponse(a).catch(b=>{if("auth/multi-factor-auth-required"===b.code)throw aM._fromErrorAndOperation(a,b,e,void 0);throw b}),g=await aK._fromIdTokenResponse(a,e,f);return c||await a._updateCurrentUser(g.user),g}async function aO(a,b){return aN(aj(a),b)}async function aP(a){let b=aj(a);b._getPasswordPolicyInternal()&&await b._updatePasswordPolicy()}function aQ(a,b,c){return(0,d.xZ)(a.app)?Promise.reject(o(a)):aO((0,e.Ku)(a),aD.credential(b,c)).catch(async b=>{throw"auth/password-does-not-meet-requirements"===b.code&&aP(a),b})}function aR(a,b,c,d){return(0,e.Ku)(a).onAuthStateChanged(b,c,d)}function aS(a){return(0,e.Ku)(a).signOut()}new WeakMap;var aT="@firebase/auth",aU="1.11.0";class aV{constructor(a){this.auth=a,this.internalListeners=new Map}getUid(){return this.assertAuthConfigured(),this.auth.currentUser?.uid||null}async getToken(a){return(this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser)?{accessToken:await this.auth.currentUser.getIdToken(a)}:null}addAuthTokenListener(a){if(this.assertAuthConfigured(),this.internalListeners.has(a))return;let b=this.auth.onIdTokenChanged(b=>{a(b?.stsTokenManager.accessToken||null)});this.internalListeners.set(a,b),this.updateProactiveRefresh()}removeAuthTokenListener(a){this.assertAuthConfigured();let b=this.internalListeners.get(a);b&&(this.internalListeners.delete(a),b(),this.updateProactiveRefresh())}assertAuthConfigured(){q(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){this.internalListeners.size>0?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}function aW(a=(0,d.Sx)()){let b=(0,d.j6)(a,"auth");if(b.isInitialized())return b.getImmediate();let c=function(a,b){let c=(0,d.j6)(a,"auth");if(c.isInitialized()){let a=c.getImmediate(),b=c.getOptions();if((0,e.bD)(b,(void 0)??{}))return a;l(a,"already-initialized")}return c.initialize({options:void 0})}(a),f=(0,e.Tj)("auth");return f&&function(a,b,c){let d=aj(a);q(/^https?:\/\//.test(b),d,"invalid-emulator-scheme");let f=as(b),{host:g,port:h}=function(a){let b=as(a),c=/(\/\/)?([^?#/]+)/.exec(a.substr(b.length));if(!c)return{host:"",port:null};let d=c[2].split("@").pop()||"",e=/^(\[[^\]]+\])(:|$)/.exec(d);if(e){let a=e[1];return{host:a,port:at(d.substr(a.length+1))}}{let[a,b]=d.split(":");return{host:a,port:at(b)}}}(b),i=null===h?"":`:${h}`,j={url:`${f}//${g}${i}/`},k=Object.freeze({host:g,port:h,protocol:f.replace(":",""),options:Object.freeze({disableWarnings:!1})});if(!d._canInitEmulator){q(d.config.emulator&&d.emulatorConfig,d,"emulator-config-failed"),q((0,e.bD)(j,d.config.emulator)&&(0,e.bD)(k,d.emulatorConfig),d,"emulator-config-failed");return}d.config.emulator=j,d.emulatorConfig=k,d.settings.appVerificationDisabledForTesting=!0,(0,e.zJ)(g)?((0,e.gE)(`${f}//${g}${i}`),(0,e.P1)("Auth",!0)):function(){function a(){let a=document.createElement("p"),b=a.style;a.innerText="Running in emulator mode. Do not use with production credentials.",b.position="fixed",b.width="100%",b.backgroundColor="#ffffff",b.border=".1em solid #000000",b.color="#b50000",b.bottom="0px",b.left="0px",b.margin="0px",b.zIndex="10000",b.textAlign="center",a.classList.add("firebase-emulator-warning"),document.body.appendChild(a)}"undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",a):a())}()}(c,`http://${f}`),c}u.initialize(fetch,Headers,Response),function(a){(0,d.om)(new f.uA("auth",(b,{options:c})=>{let d=b.getProvider("app").getImmediate(),e=b.getProvider("heartbeat"),f=b.getProvider("app-check-internal"),{apiKey:g,authDomain:h}=d.options;q(g&&!g.includes(":"),"invalid-api-key",{appName:d.name});let i=new ai(d,e,f,{apiKey:g,authDomain:h,clientPlatform:a,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:ae(a)}),j=c?.persistence||[],k=(Array.isArray(j)?j:[j]).map(_);return c?.errorMap&&i._updateErrorMap(c.errorMap),i._initializeWithPersistence(k,c?.popupRedirectResolver),i},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((a,b,c)=>{a.getProvider("auth-internal").initialize()})),(0,d.om)(new f.uA("auth-internal",a=>new aV(aj(a.getProvider("auth").getImmediate())),"PRIVATE").setInstantiationMode("EXPLICIT")),(0,d.KO)(aT,aU,function(a){switch(a){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}(a)),(0,d.KO)(aT,aU,"esm2020")}("Node"),m("operation-not-supported-in-this-environment"),ai.prototype.setPersistence=async()=>{};class aX{constructor(a){this.factorId=a}_process(a,b,c){switch(b.type){case"enroll":return this._finalizeEnroll(a,b.credential,c);case"signin":return this._finalizeSignIn(a,b.credential);default:return r("unexpected MultiFactorSessionType")}}}class aY{static assertionForEnrollment(a,b){return aZ._fromSecret(a,b)}static assertionForSignIn(a,b){return aZ._fromEnrollmentId(a,b)}static async generateSecret(a){var b;q(void 0!==a.user?.auth,"internal-error");let c=await z(b=a.user.auth,"POST","/v2/accounts/mfaEnrollment:start",y(b,{idToken:a.credential,totpEnrollmentInfo:{}}));return a$._fromStartTotpMfaEnrollmentResponse(c,a.user.auth)}}aY.FACTOR_ID="totp";class aZ extends aX{constructor(a,b,c){super("totp"),this.otp=a,this.enrollmentId=b,this.secret=c}static _fromSecret(a,b){return new aZ(b,void 0,a)}static _fromEnrollmentId(a,b){return new aZ(b,a)}async _finalizeEnroll(a,b,c){return q(void 0!==this.secret,a,"argument-error"),z(a,"POST","/v2/accounts/mfaEnrollment:finalize",y(a,{idToken:b,displayName:c,totpVerificationInfo:this.secret._makeTotpVerificationInfo(this.otp)}))}async _finalizeSignIn(a,b){q(void 0!==this.enrollmentId&&void 0!==this.otp,a,"argument-error");let c={verificationCode:this.otp};return z(a,"POST","/v2/accounts/mfaSignIn:finalize",y(a,{mfaPendingCredential:b,mfaEnrollmentId:this.enrollmentId,totpVerificationInfo:c}))}}class a${constructor(a,b,c,d,e,f,g){this.sessionInfo=f,this.auth=g,this.secretKey=a,this.hashingAlgorithm=b,this.codeLength=c,this.codeIntervalSeconds=d,this.enrollmentCompletionDeadline=e}static _fromStartTotpMfaEnrollmentResponse(a,b){return new a$(a.totpSessionInfo.sharedSecretKey,a.totpSessionInfo.hashingAlgorithm,a.totpSessionInfo.verificationCodeLength,a.totpSessionInfo.periodSec,new Date(a.totpSessionInfo.finalizeEnrollmentTime).toUTCString(),a.totpSessionInfo.sessionInfo,b)}_makeTotpVerificationInfo(a){return{sessionInfo:this.sessionInfo,verificationCode:a}}generateQrCodeUrl(a,b){let c=!1;return(a_(a)||a_(b))&&(c=!0),c&&(a_(a)&&(a=this.auth.currentUser?.email||"unknownuser"),a_(b)&&(b=this.auth.name)),`otpauth://totp/${b}:${a}?secret=${this.secretKey}&issuer=${b}&algorithm=${this.hashingAlgorithm}&digits=${this.codeLength}`}}function a_(a){return void 0===a||a?.length===0}},63206:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=c(55511),f=c(79551),g=c(28354),h=c(71392),i=c(37196),j=c(4015),k=c(70513),l=function(a,b){this.version="hybi-"+j.VERSION,j.call(this,null,a,b),this.readyState=-1,this._key=l.generateKey(),this._accept=j.generateAccept(this._key),this._http=new h("response");var c=f.parse(this.url),e=c.auth&&d.from(c.auth,"utf8").toString("base64");if(0>this.VALID_PROTOCOLS.indexOf(c.protocol))throw Error(this.url+" is not a valid WebSocket URL");this._pathname=(c.pathname||"/")+(c.search||""),this._headers.set("Host",c.host),this._headers.set("Upgrade","websocket"),this._headers.set("Connection","Upgrade"),this._headers.set("Sec-WebSocket-Key",this._key),this._headers.set("Sec-WebSocket-Version",j.VERSION),this._protocols.length>0&&this._headers.set("Sec-WebSocket-Protocol",this._protocols.join(", ")),e&&this._headers.set("Authorization","Basic "+e)};g.inherits(l,j),l.generateKey=function(){return e.randomBytes(16).toString("base64")};var m={VALID_PROTOCOLS:["ws:","wss:"],proxy:function(a,b){return new k(this,a,b)},start:function(){return -1===this.readyState&&(this._write(this._handshakeRequest()),this.readyState=0,!0)},parse:function(a){if(3!==this.readyState){if(this.readyState>0)return j.prototype.parse.call(this,a);this._http.parse(a),this._http.isComplete()&&(this._validateHandshake(),3!==this.readyState&&(this._open(),this.parse(this._http.body)))}},_handshakeRequest:function(){var a=this._extensions.generateOffer();a&&this._headers.set("Sec-WebSocket-Extensions",a);var b=["GET "+this._pathname+" HTTP/1.1",this._headers.toString(),""];return d.from(b.join("\r\n"),"utf8")},_failHandshake:function(a){a="Error during WebSocket handshake: "+a,this.readyState=3,this.emit("error",Error(a)),this.emit("close",new i.CloseEvent(this.ERRORS.protocol_error,a))},_validateHandshake:function(){if(this.statusCode=this._http.statusCode,this.headers=this._http.headers,this._http.error)return this._failHandshake(this._http.error.message);if(101!==this._http.statusCode)return this._failHandshake("Unexpected response code: "+this._http.statusCode);var a=this._http.headers,b=a.upgrade||"",c=a.connection||"",d=a["sec-websocket-accept"]||"",e=a["sec-websocket-protocol"]||"";if(""===b)return this._failHandshake("'Upgrade' header is missing");if("websocket"!==b.toLowerCase())return this._failHandshake("'Upgrade' header value is not 'WebSocket'");if(""===c)return this._failHandshake("'Connection' header is missing");if("upgrade"!==c.toLowerCase())return this._failHandshake("'Connection' header value is not 'Upgrade'");if(d!==this._accept)return this._failHandshake("Sec-WebSocket-Accept mismatch");if(this.protocol=null,""!==e)if(0>this._protocols.indexOf(e))return this._failHandshake("Sec-WebSocket-Protocol mismatch");else this.protocol=e;try{this._extensions.activate(this.headers["sec-websocket-extensions"])}catch(a){return this._failHandshake(a.message)}}};for(var n in m)l.prototype[n]=m[n];a.exports=l},63630:(a,b,c)=>{"use strict";var d=c(74279),e=c(19621),f=function(){this._rsv1=this._rsv2=this._rsv3=null,this._byName={},this._inOrder=[],this._sessions=[],this._index={}};f.MESSAGE_OPCODES=[1,2];var g={add:function(a){if("string"!=typeof a.name)throw TypeError("extension.name must be a string");if("permessage"!==a.type)throw TypeError('extension.type must be "permessage"');if("boolean"!=typeof a.rsv1)throw TypeError("extension.rsv1 must be true or false");if("boolean"!=typeof a.rsv2)throw TypeError("extension.rsv2 must be true or false");if("boolean"!=typeof a.rsv3)throw TypeError("extension.rsv3 must be true or false");if(this._byName.hasOwnProperty(a.name))throw TypeError('An extension with name "'+a.name+'" is already registered');this._byName[a.name]=a,this._inOrder.push(a)},generateOffer:function(){var a=[],b=[],c={};return this._inOrder.forEach(function(e){var f=e.createClientSession();if(f){var g=[e,f];a.push(g),c[e.name]=g;var h=f.generateOffer();(h=h?[].concat(h):[]).forEach(function(a){b.push(d.serializeParams(e.name,a))},this)}},this),this._sessions=a,this._index=c,b.length>0?b.join(", "):null},activate:function(a){var b=d.parseHeader(a),c=[];b.eachOffer(function(a,b){var e=this._index[a];if(!e)throw Error('Server sent an extension response for unknown extension "'+a+'"');var f=e[0],g=e[1],h=this._reserved(f);if(h)throw Error("Server sent two extension responses that use the RSV"+h[0]+' bit: "'+h[1]+'" and "'+f.name+'"');if(!0!==g.activate(b))throw Error("Server sent unacceptable extension parameters: "+d.serializeParams(a,b));this._reserve(f),c.push(e)},this),this._sessions=c,this._pipeline=new e(c)},generateResponse:function(a){var b=[],c=[],f=d.parseHeader(a);return this._inOrder.forEach(function(a){var e=f.byName(a.name);if(!(0===e.length||this._reserved(a))){var g=a.createServerSession(e);g&&(this._reserve(a),b.push([a,g]),c.push(d.serializeParams(a.name,g.generateResponse())))}},this),this._sessions=b,this._pipeline=new e(b),c.length>0?c.join(", "):null},validFrameRsv:function(a){var b,c={rsv1:!1,rsv2:!1,rsv3:!1};if(f.MESSAGE_OPCODES.indexOf(a.opcode)>=0)for(var d=0,e=this._sessions.length;d<e;d++)b=this._sessions[d][0],c.rsv1=c.rsv1||b.rsv1,c.rsv2=c.rsv2||b.rsv2,c.rsv3=c.rsv3||b.rsv3;return(c.rsv1||!a.rsv1)&&(c.rsv2||!a.rsv2)&&(c.rsv3||!a.rsv3)},processIncomingMessage:function(a,b,c){this._pipeline.processIncomingMessage(a,b,c)},processOutgoingMessage:function(a,b,c){this._pipeline.processOutgoingMessage(a,b,c)},close:function(a,b){if(!this._pipeline)return a.call(b);this._pipeline.close(a,b)},_reserve:function(a){this._rsv1=this._rsv1||a.rsv1&&a.name,this._rsv2=this._rsv2||a.rsv2&&a.name,this._rsv3=this._rsv3||a.rsv3&&a.name},_reserved:function(a){return this._rsv1&&a.rsv1?[1,this._rsv1]:this._rsv2&&a.rsv2?[2,this._rsv2]:!!this._rsv3&&!!a.rsv3&&[3,this._rsv3]}};for(var h in g)f.prototype[h]=g[h];a.exports=f},64492:a=>{"use strict";var b=function(a,b){for(var c in this.type=a,b)this[c]=b[c]};b.prototype.initEvent=function(a,b,c){this.type=a,this.bubbles=b,this.cancelable=c},b.prototype.stopPropagation=function(){},b.prototype.preventDefault=function(){},b.CAPTURING_PHASE=1,b.AT_TARGET=2,b.BUBBLING_PHASE=3,a.exports=b},67989:(a,b,c)=>{"use strict";c.d(b,{Wp:()=>d.Wp});var d=c(15048);(0,d.KO)("firebase","12.0.0","app")},70092:(a,b,c)=>{"use strict";var d=c(28354),e=c(74366),f=c(92139),g=function(a,b,c,d,g){g=g||{},this._stream=b,this._driver=e.http(a,{maxLength:g.maxLength,protocols:d});var h=this;if(this._stream&&this._stream.writable){if(!this._stream.readable)return this._stream.end();var i=function(){h._stream.removeListener("data",i)};this._stream.on("data",i),f.call(this,g),process.nextTick(function(){h._driver.start(),h._driver.io.write(c)})}};d.inherits(g,f),g.isWebSocket=function(a){return e.isWebSocket(a)},g.validateOptions=function(a,b){e.validateOptions(a,b)},g.WebSocket=g,g.Client=c(34364),g.EventSource=c(56858),a.exports=g},70146:(a,b,c)=>{"use strict";c.d(b,{c7:()=>w});var d,e,f=c(15048),g=c(52122),h=c(89495);let i="firebasestorage.googleapis.com";class j extends g.g{constructor(a,b,c=0){super(k(a),`Firebase Storage: ${b} (${k(a)})`),this.status_=c,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,j.prototype)}get status(){return this.status_}set status(a){this.status_=a}_codeEquals(a){return k(a)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(a){this.customData.serverResponse=a,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}function k(a){return"storage/"+a}function l(a){return new j(d.INVALID_ARGUMENT,a)}function m(){return new j(d.APP_DELETED,"The Firebase app was deleted.")}!function(a){a.UNKNOWN="unknown",a.OBJECT_NOT_FOUND="object-not-found",a.BUCKET_NOT_FOUND="bucket-not-found",a.PROJECT_NOT_FOUND="project-not-found",a.QUOTA_EXCEEDED="quota-exceeded",a.UNAUTHENTICATED="unauthenticated",a.UNAUTHORIZED="unauthorized",a.UNAUTHORIZED_APP="unauthorized-app",a.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",a.INVALID_CHECKSUM="invalid-checksum",a.CANCELED="canceled",a.INVALID_EVENT_NAME="invalid-event-name",a.INVALID_URL="invalid-url",a.INVALID_DEFAULT_BUCKET="invalid-default-bucket",a.NO_DEFAULT_BUCKET="no-default-bucket",a.CANNOT_SLICE_BLOB="cannot-slice-blob",a.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",a.NO_DOWNLOAD_URL="no-download-url",a.INVALID_ARGUMENT="invalid-argument",a.INVALID_ARGUMENT_COUNT="invalid-argument-count",a.APP_DELETED="app-deleted",a.INVALID_ROOT_OPERATION="invalid-root-operation",a.INVALID_FORMAT="invalid-format",a.INTERNAL_ERROR="internal-error",a.UNSUPPORTED_ENVIRONMENT="unsupported-environment"}(d||(d={}));class n{constructor(a,b){this.bucket=a,this.path_=b}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){let a=encodeURIComponent;return"/b/"+a(this.bucket)+"/o/"+a(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(a,b){let c;try{c=n.makeFromUrl(a,b)}catch(b){return new n(a,"")}if(""===c.path)return c;throw new j(d.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+a+"'.")}static makeFromUrl(a,b){let c=null,e="([A-Za-z0-9.\\-_]+)",f=RegExp("^gs://"+e+"(/(.*))?$","i");function g(a){a.path_=decodeURIComponent(a.path)}let h=b.replace(/[.]/g,"\\."),k=RegExp(`^https?://${h}/v[A-Za-z0-9_]+/b/${e}/o(/([^?#]*).*)?$`,"i"),l=b===i?"(?:storage.googleapis.com|storage.cloud.google.com)":b,m=[{regex:f,indices:{bucket:1,path:3},postModify:function(a){"/"===a.path.charAt(a.path.length-1)&&(a.path_=a.path_.slice(0,-1))}},{regex:k,indices:{bucket:1,path:3},postModify:g},{regex:RegExp(`^https?://${l}/${e}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:g}];for(let b=0;b<m.length;b++){let d=m[b],e=d.regex.exec(a);if(e){let a=e[d.indices.bucket],b=e[d.indices.path];b||(b=""),c=new n(a,b),d.postModify(c);break}}if(null==c)throw new j(d.INVALID_URL,"Invalid URL '"+a+"'.");return c}}class o{constructor(a){this.promise_=Promise.reject(a)}getPromise(){return this.promise_}cancel(a=!1){}}function p(a,b,c,d){if(d<b)throw l(`Invalid value for '${a}'. Expected ${b} or greater.`);if(d>c)throw l(`Invalid value for '${a}'. Expected ${c} or less.`)}!function(a){a[a.NO_ERROR=0]="NO_ERROR",a[a.NETWORK_ERROR=1]="NETWORK_ERROR",a[a.ABORT=2]="ABORT"}(e||(e={}));class q{constructor(a,b,c,d,e,f,g,h,i,j,k,l=!0,m=!1){this.url_=a,this.method_=b,this.headers_=c,this.body_=d,this.successCodes_=e,this.additionalRetryCodes_=f,this.callback_=g,this.errorCallback_=h,this.timeout_=i,this.progressCallback_=j,this.connectionFactory_=k,this.retry=l,this.isUsingEmulator=m,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((a,b)=>{this.resolve_=a,this.reject_=b,this.start_()})}start_(){let a=(a,b)=>{if(b)return void a(!1,new r(!1,null,!0));let c=this.connectionFactory_();this.pendingConnection_=c;let d=a=>{let b=a.loaded,c=a.lengthComputable?a.total:-1;null!==this.progressCallback_&&this.progressCallback_(b,c)};null!==this.progressCallback_&&c.addUploadProgressListener(d),c.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&c.removeUploadProgressListener(d),this.pendingConnection_=null;let b=c.getErrorCode()===e.NO_ERROR,f=c.getStatus();if(!b||function(a,b){let c=a>=500&&a<600,d=-1!==[408,429].indexOf(a),e=-1!==b.indexOf(a);return c||d||e}(f,this.additionalRetryCodes_)&&this.retry)return void a(!1,new r(!1,null,c.getErrorCode()===e.ABORT));a(!0,new r(-1!==this.successCodes_.indexOf(f),c))})},b=(a,b)=>{let c=this.resolve_,e=this.reject_,f=b.connection;if(b.wasSuccessCode)try{let a=this.callback_(f,f.getResponse());void 0!==a?c(a):c()}catch(a){e(a)}else if(null!==f){let a=new j(d.UNKNOWN,"An unknown error occurred, please check the error payload for server response.");a.serverResponse=f.getErrorText(),e(this.errorCallback_?this.errorCallback_(f,a):a)}else e(b.canceled?this.appDelete_?m():new j(d.CANCELED,"User canceled the upload/download."):new j(d.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again."))};this.canceled_?b(!1,new r(!1,null,!0)):this.backoffId_=function(a,b,c){let d=1,e=null,f=null,g=!1,h=0,i=!1;function j(...a){i||(i=!0,b.apply(null,a))}function k(b){e=setTimeout(()=>{e=null,a(m,2===h)},b)}function l(){f&&clearTimeout(f)}function m(a,...b){let c;if(i)return void l();if(a||2===h||g){l(),j.call(null,a,...b);return}d<64&&(d*=2),1===h?(h=2,c=0):c=(d+Math.random())*1e3,k(c)}let n=!1;function o(a){n||(n=!0,l(),!i&&(null!==e?(a||(h=2),clearTimeout(e),k(0)):a||(h=1)))}return k(0),f=setTimeout(()=>{g=!0,o(!0)},c),o}(a,b,this.timeout_)}getPromise(){return this.promise_}cancel(a){this.canceled_=!0,this.appDelete_=a||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class r{constructor(a,b,c){this.wasSuccessCode=a,this.connection=b,this.canceled=!!c}}class s{constructor(a,b){this._service=a,b instanceof n?this._location=b:this._location=n.makeFromUrl(b,a.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(a,b){return new s(a,b)}get root(){let a=new n(this._location.bucket,"");return this._newRef(this._service,a)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){var a=this._location.path;let b=a.lastIndexOf("/",a.length-2);return -1===b?a:a.slice(b+1)}get storage(){return this._service}get parent(){let a=function(a){if(0===a.length)return null;let b=a.lastIndexOf("/");return -1===b?"":a.slice(0,b)}(this._location.path);if(null===a)return null;let b=new n(this._location.bucket,a);return new s(this._service,b)}_throwIfRoot(a){if(""===this._location.path)throw new j(d.INVALID_ROOT_OPERATION,"The operation '"+a+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}}function t(a,b){let c=b?.storageBucket;return null==c?null:n.makeFromBucketSpec(c,a)}class u{constructor(a,b,c,d,e,f=!1){this.app=a,this._authProvider=b,this._appCheckProvider=c,this._url=d,this._firebaseVersion=e,this._isUsingEmulator=f,this._bucket=null,this._host=i,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,null!=d?this._bucket=n.makeFromBucketSpec(d,this._host):this._bucket=t(this._host,this.app.options)}get host(){return this._host}set host(a){this._host=a,null!=this._url?this._bucket=n.makeFromBucketSpec(this._url,a):this._bucket=t(a,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(a){p("time",0,1/0,a),this._maxUploadRetryTime=a}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(a){p("time",0,1/0,a),this._maxOperationRetryTime=a}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;let a=this._authProvider.getImmediate({optional:!0});if(a){let b=await a.getToken();if(null!==b)return b.accessToken}return null}async _getAppCheckToken(){if((0,f.xZ)(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let a=this._appCheckProvider.getImmediate({optional:!0});return a?(await a.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(a=>a.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(a){return new s(this,a)}_makeRequest(a,b,c,d,e=!0){if(this._deleted)return new o(m());{let f=function(a,b,c,d,e,f,g=!0,h=!1){let i=function(a){let b=encodeURIComponent,c="?";for(let d in a)a.hasOwnProperty(d)&&(c=c+(b(d)+"=")+b(a[d])+"&");return c.slice(0,-1)}(a.urlParams),j=a.url+i,k=Object.assign({},a.headers);return b&&(k["X-Firebase-GMPID"]=b),null!==c&&c.length>0&&(k.Authorization="Firebase "+c),k["X-Firebase-Storage-Version"]="webjs/"+(f??"AppManager"),null!==d&&(k["X-Firebase-AppCheck"]=d),new q(j,a.method,k,a.body,a.successCodes,a.additionalRetryCodes,a.handler,a.errorHandler,a.timeout,a.progressCallback,e,g,h)}(a,this._appId,c,d,b,this._firebaseVersion,e,this._isUsingEmulator);return this._requests.add(f),f.getPromise().then(()=>this._requests.delete(f),()=>this._requests.delete(f)),f}}async makeRequestWithTokens(a,b){let[c,d]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(a,b,c,d).getPromise()}}let v="storage";function w(a=(0,f.Sx)(),b){a=(0,g.Ku)(a);let c=(0,f.j6)(a,v).getImmediate({identifier:b}),d=(0,g.yU)("storage");return d&&function(a,b,c,d={}){!function(a,b,c,d={}){a.host=`${b}:${c}`;let e=(0,g.zJ)(b);e&&((0,g.gE)(`https://${a.host}/b`),(0,g.P1)("Storage",!0)),a._isUsingEmulator=!0,a._protocol=e?"https":"http";let{mockUserToken:f}=d;f&&(a._overrideAuthToken="string"==typeof f?f:(0,g.Fy)(f,a.app.options.projectId))}(a,b,c,d)}(c,...d),c}(0,f.om)(new h.uA(v,function(a,{instanceIdentifier:b}){let c=a.getProvider("app").getImmediate();return new u(c,a.getProvider("auth-internal"),a.getProvider("app-check-internal"),b,f.MF)},"PUBLIC").setMultipleInstances(!0)),(0,f.KO)("@firebase/storage","0.14.0")},70376:(a,b,c)=>{"use strict";var d=c(90380),e=function(){this._complete=!1,this._callbacks=new d(e.QUEUE_SIZE)};e.QUEUE_SIZE=4,e.all=function(a){var b=new e,c=a.length,d=c;for(0===c&&b.done();d--;)a[d].then(function(){0==(c-=1)&&b.done()});return b},e.prototype.then=function(a){this._complete?a():this._callbacks.push(a)},e.prototype.done=function(){this._complete=!0;for(var a,b=this._callbacks;a=b.shift();)a()},a.exports=e},70513:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=c(27910).Stream,f=c(79551),g=c(28354),h=c(37196),i=c(2163),j=c(71392),k={"ws:":80,"wss:":443},l=function(a,b,c){this._client=a,this._http=new j("response"),this._origin="object"==typeof a.url?a.url:f.parse(a.url),this._url="object"==typeof b?b:f.parse(b),this._options=c||{},this._state=0,this.readable=this.writable=!0,this._paused=!1,this._headers=new i,this._headers.set("Host",this._origin.host),this._headers.set("Connection","keep-alive"),this._headers.set("Proxy-Connection","keep-alive");var e=this._url.auth&&d.from(this._url.auth,"utf8").toString("base64");e&&this._headers.set("Proxy-Authorization","Basic "+e)};g.inherits(l,e);var m={setHeader:function(a,b){return 0===this._state&&(this._headers.set(a,b),!0)},start:function(){if(0!==this._state)return!1;this._state=1;var a=this._origin,b=a.port||k[a.protocol],c=["CONNECT "+a.hostname+":"+b+" HTTP/1.1",this._headers.toString(),""];return this.emit("data",d.from(c.join("\r\n"),"utf8")),!0},pause:function(){this._paused=!0},resume:function(){this._paused=!1,this.emit("drain")},write:function(a){if(!this.writable)return!1;if(this._http.parse(a),!this._http.isComplete())return!this._paused;if(this.statusCode=this._http.statusCode,this.headers=this._http.headers,200===this.statusCode)this.emit("connect",new h.ConnectEvent);else{var b="Can't establish a connection to the server at "+this._origin.href;this.emit("error",Error(b))}return this.end(),!this._paused},end:function(a){this.writable&&(void 0!==a&&this.write(a),this.readable=this.writable=!1,this.emit("close"),this.emit("end"))},destroy:function(){this.end()}};for(var n in m)l.prototype[n]=m[n];a.exports=l},71392:(a,b,c)=>{"use strict";var d=c(23518).e,e=c(7984).Buffer,f={request:d.REQUEST||"request",response:d.RESPONSE||"response"},g=function(a){this._type=a,this._parser=new d(f[a]),this._complete=!1,this.headers={};var b=null,c=this;this._parser.onHeaderField=function(a,c,d){b=a.toString("utf8",c,c+d).toLowerCase()},this._parser.onHeaderValue=function(a,d,e){var f=a.toString("utf8",d,d+e);c.headers.hasOwnProperty(b)?c.headers[b]+=", "+f:c.headers[b]=f},this._parser.onHeadersComplete=this._parser[d.kOnHeadersComplete]=function(a,b,d,e,f,h){var i=arguments[0];if("object"==typeof i&&(e=i.method,f=i.url,h=i.statusCode,d=i.headers),c.method="number"==typeof e?g.METHODS[e]:e,c.statusCode=h,c.url=f,d){for(var j,k,l=0,m=d.length;l<m;l+=2)j=d[l].toLowerCase(),k=d[l+1],c.headers.hasOwnProperty(j)?c.headers[j]+=", "+k:c.headers[j]=k;c._complete=!0}}};g.METHODS={0:"DELETE",1:"GET",2:"HEAD",3:"POST",4:"PUT",5:"CONNECT",6:"OPTIONS",7:"TRACE",8:"COPY",9:"LOCK",10:"MKCOL",11:"MOVE",12:"PROPFIND",13:"PROPPATCH",14:"SEARCH",15:"UNLOCK",16:"BIND",17:"REBIND",18:"UNBIND",19:"ACL",20:"REPORT",21:"MKACTIVITY",22:"CHECKOUT",23:"MERGE",24:"M-SEARCH",25:"NOTIFY",26:"SUBSCRIBE",27:"UNSUBSCRIBE",28:"PATCH",29:"PURGE",30:"MKCALENDAR",31:"LINK",32:"UNLINK"};var h=process.version?process.version.match(/[0-9]+/g).map(function(a){return parseInt(a,10)}):[];0===h[0]&&12===h[1]&&(g.METHODS[16]="REPORT",g.METHODS[17]="MKACTIVITY",g.METHODS[18]="CHECKOUT",g.METHODS[19]="MERGE",g.METHODS[20]="M-SEARCH",g.METHODS[21]="NOTIFY",g.METHODS[22]="SUBSCRIBE",g.METHODS[23]="UNSUBSCRIBE",g.METHODS[24]="PATCH",g.METHODS[25]="PURGE"),g.prototype.isComplete=function(){return this._complete},g.prototype.parse=function(a){var b=this._parser.execute(a,0,a.length);if("number"!=typeof b){this.error=b,this._complete=!0;return}this._complete&&(this.body=b<a.length?a.slice(b):e.alloc(0))},a.exports=g},74279:a=>{"use strict";var b=/([!#\$%&'\*\+\-\.\^_`\|~0-9A-Za-z]+)/,c=/([^!#\$%&'\*\+\-\.\^_`\|~0-9A-Za-z])/g,d=RegExp(b.source+"(?:=(?:"+b.source+"|"+/"((?:\\[\x00-\x7f]|[^\x00-\x08\x0a-\x1f\x7f"\\])*)"/.source+"))?"),e=RegExp(b.source+"(?: *; *"+d.source+")*","g"),f=RegExp("^"+e.source+"(?: *, *"+e.source+")*$"),g=/^-?(0|[1-9][0-9]*)(\.[0-9]+)?$/,h=Object.prototype.hasOwnProperty,i=function(){this._byName={},this._inOrder=[]};i.prototype.push=function(a,b){h.call(this._byName,a)||(this._byName[a]=[]),this._byName[a].push(b),this._inOrder.push({name:a,params:b})},i.prototype.eachOffer=function(a,b){for(var c=this._inOrder,d=0,e=c.length;d<e;d++)a.call(b,c[d].name,c[d].params)},i.prototype.byName=function(a){return this._byName[a]||[]},i.prototype.toArray=function(){return this._inOrder.slice()},a.exports={parseHeader:function(a){var b=new i;if(""===a||void 0===a)return b;if(!f.test(a))throw SyntaxError("Invalid Sec-WebSocket-Extensions header: "+a);return a.match(e).forEach(function(a){var c=a.match(RegExp(d.source,"g")),e=c.shift(),f={};c.forEach(function(a){var b,c=a.match(d),e=c[1];b=void 0!==c[2]?c[2]:void 0===c[3]||c[3].replace(/\\/g,""),g.test(b)&&(b=parseFloat(b)),h.call(f,e)?(f[e]=[].concat(f[e]),f[e].push(b)):f[e]=b},this),b.push(e,f)},this),b},serializeParams:function(a,b){var d=[],e=function(a,b){b instanceof Array?b.forEach(function(b){e(a,b)}):!0===b?d.push(a):"number"==typeof b?d.push(a+"="+b):c.test(b)?d.push(a+'="'+b.replace(/"/g,'\\"')+'"'):d.push(a+"="+b)};for(var f in b)e(f,b[f]);return[a].concat(d).join("; ")}}},74366:(a,b,c)=>{"use strict";var d=c(37196),e=c(63206),f=c(54482);a.exports={client:function(a,b){return void 0===(b=b||{}).masking&&(b.masking=!0),new e(a,b)},server:function(a){return void 0===(a=a||{}).requireMasking&&(a.requireMasking=!0),new f(a)},http:function(){return f.http.apply(f,arguments)},isSecureRequest:function(a){return f.isSecureRequest(a)},isWebSocket:function(a){return d.isWebSocket(a)},validateOptions:function(a,b){d.validateOptions(a,b)}}},83991:(a,b,c)=>{"use strict";var d=c(7984).Buffer,e=c(37196),f=c(43082),g=c(55511),h=c(28354),i=function(a){return parseInt((a.match(/[0-9]/g)||[]).join(""),10)},j=function(a){return(a.match(/ /g)||[]).length},k=function(a,b,c){f.apply(this,arguments),this._stage=-1,this._body=[],this.version="hixie-76",this._headers.clear(),this._headers.set("Upgrade","WebSocket"),this._headers.set("Connection","Upgrade"),this._headers.set("Sec-WebSocket-Origin",this._request.headers.origin),this._headers.set("Sec-WebSocket-Location",this.url)};h.inherits(k,f);var l={BODY_SIZE:8,start:function(){return!!f.prototype.start.call(this)&&(this._started=!0,this._sendHandshakeBody(),!0)},close:function(){return 3!==this.readyState&&(1===this.readyState&&this._write(d.from([255,0])),this.readyState=3,this.emit("close",new e.CloseEvent(null,null)),!0)},_handshakeResponse:function(){var a=this._request.headers,b=a["sec-websocket-key1"],c=a["sec-websocket-key2"];if(!b)throw Error("Missing required header: Sec-WebSocket-Key1");if(!c)throw Error("Missing required header: Sec-WebSocket-Key2");var e=i(b),f=j(b),g=i(c),h=j(c);if(e%f!=0||g%h!=0)throw Error("Client sent invalid Sec-WebSocket-Key headers");this._keyValues=[e/f,g/h];var a=["HTTP/1.1 101 WebSocket Protocol Handshake",this._headers.toString(),""];return d.from(a.join("\r\n"),"binary")},_handshakeSignature:function(){if(this._body.length<this.BODY_SIZE)return null;var a=g.createHash("md5"),b=d.allocUnsafe(8+this.BODY_SIZE);return b.writeUInt32BE(this._keyValues[0],0),b.writeUInt32BE(this._keyValues[1],4),d.from(this._body).copy(b,8,0,this.BODY_SIZE),a.update(b),d.from(a.digest("binary"),"binary")},_sendHandshakeBody:function(){if(this._started){var a=this._handshakeSignature();a&&(this._write(a),this._stage=0,this._open(),this._body.length>this.BODY_SIZE&&this.parse(this._body.slice(this.BODY_SIZE)))}},_parseLeadingByte:function(a){if(255!==a)return f.prototype._parseLeadingByte.call(this,a);this._closing=!0,this._length=0,this._stage=1}};for(var m in l)k.prototype[m]=l[m];a.exports=k},89495:(a,b,c)=>{"use strict";c.d(b,{h1:()=>h,uA:()=>e});var d=c(52122);class e{constructor(a,b,c){this.name=a,this.instanceFactory=b,this.type=c,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(a){return this.instantiationMode=a,this}setMultipleInstances(a){return this.multipleInstances=a,this}setServiceProps(a){return this.serviceProps=a,this}setInstanceCreatedCallback(a){return this.onInstanceCreated=a,this}}let f="[DEFAULT]";class g{constructor(a,b){this.name=a,this.container=b,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(a){let b=this.normalizeInstanceIdentifier(a);if(!this.instancesDeferred.has(b)){let a=new d.cY;if(this.instancesDeferred.set(b,a),this.isInitialized(b)||this.shouldAutoInitialize())try{let c=this.getOrInitializeService({instanceIdentifier:b});c&&a.resolve(c)}catch(a){}}return this.instancesDeferred.get(b).promise}getImmediate(a){let b=this.normalizeInstanceIdentifier(a?.identifier),c=a?.optional??!1;if(this.isInitialized(b)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:b})}catch(a){if(c)return null;throw a}if(c)return null;throw Error(`Service ${this.name} is not available`)}getComponent(){return this.component}setComponent(a){if(a.name!==this.name)throw Error(`Mismatching Component ${a.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=a,this.shouldAutoInitialize()){if("EAGER"===a.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:f})}catch(a){}for(let[a,b]of this.instancesDeferred.entries()){let c=this.normalizeInstanceIdentifier(a);try{let a=this.getOrInitializeService({instanceIdentifier:c});b.resolve(a)}catch(a){}}}}clearInstance(a=f){this.instancesDeferred.delete(a),this.instancesOptions.delete(a),this.instances.delete(a)}async delete(){let a=Array.from(this.instances.values());await Promise.all([...a.filter(a=>"INTERNAL"in a).map(a=>a.INTERNAL.delete()),...a.filter(a=>"_delete"in a).map(a=>a._delete())])}isComponentSet(){return null!=this.component}isInitialized(a=f){return this.instances.has(a)}getOptions(a=f){return this.instancesOptions.get(a)||{}}initialize(a={}){let{options:b={}}=a,c=this.normalizeInstanceIdentifier(a.instanceIdentifier);if(this.isInitialized(c))throw Error(`${this.name}(${c}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let d=this.getOrInitializeService({instanceIdentifier:c,options:b});for(let[a,b]of this.instancesDeferred.entries())c===this.normalizeInstanceIdentifier(a)&&b.resolve(d);return d}onInit(a,b){let c=this.normalizeInstanceIdentifier(b),d=this.onInitCallbacks.get(c)??new Set;d.add(a),this.onInitCallbacks.set(c,d);let e=this.instances.get(c);return e&&a(e,c),()=>{d.delete(a)}}invokeOnInitCallbacks(a,b){let c=this.onInitCallbacks.get(b);if(c)for(let d of c)try{d(a,b)}catch{}}getOrInitializeService({instanceIdentifier:a,options:b={}}){var c;let d=this.instances.get(a);if(!d&&this.component&&(d=this.component.instanceFactory(this.container,{instanceIdentifier:(c=a)===f?void 0:c,options:b}),this.instances.set(a,d),this.instancesOptions.set(a,b),this.invokeOnInitCallbacks(d,a),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,a,d)}catch{}return d||null}normalizeInstanceIdentifier(a=f){return this.component?this.component.multipleInstances?a:f:a}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class h{constructor(a){this.name=a,this.providers=new Map}addComponent(a){let b=this.getProvider(a.name);if(b.isComponentSet())throw Error(`Component ${a.name} has already been registered with ${this.name}`);b.setComponent(a)}addOrOverwriteComponent(a){this.getProvider(a.name).isComponentSet()&&this.providers.delete(a.name),this.addComponent(a)}getProvider(a){if(this.providers.has(a))return this.providers.get(a);let b=new g(a,this);return this.providers.set(a,b),b}getProviders(){return Array.from(this.providers.values())}}},90380:a=>{"use strict";var b=function(a){this._bufferSize=a,this.clear()};b.prototype.clear=function(){this._buffer=Array(this._bufferSize),this._ringOffset=0,this._ringSize=this._bufferSize,this._head=0,this._tail=0,this.length=0},b.prototype.push=function(a){var b=!1,c=!1;this._ringSize<this._bufferSize?b=0===this._tail:this._ringOffset===this._ringSize&&(b=!0,c=0===this._tail),b&&(this._tail=this._bufferSize,this._buffer=this._buffer.concat(Array(this._bufferSize)),this._bufferSize=this._buffer.length,c&&(this._ringSize=this._bufferSize)),this._buffer[this._tail]=a,this.length+=1,this._tail<this._ringSize&&(this._ringOffset+=1),this._tail=(this._tail+1)%this._bufferSize},b.prototype.peek=function(){if(0!==this.length)return this._buffer[this._head]},b.prototype.shift=function(){if(0!==this.length){var a=this._buffer[this._head];return this._buffer[this._head]=void 0,this.length-=1,this._ringOffset-=1,0===this._ringOffset&&this.length>0?(this._head=this._ringSize,this._ringOffset=this.length,this._ringSize=this._bufferSize):this._head=(this._head+1)%this._ringSize,a}},a.exports=b},90555:(a,b,c)=>{"use strict";let d,e,f,g,h,i,j,k,l,m;c.d(b,{Jt:()=>dG,C3:()=>dK,VC:()=>dC,KR:()=>dA,TF:()=>dD,hZ:()=>dE,yo:()=>dF});var n,o=c(70092),p=c(52122),q=c(13634),r=c(15048),s=c(89495);let t=/(console\.firebase|firebase-console-\w+\.corp|firebase\.corp)\.google\.com/,u="websocket",v="long_polling";class w{constructor(a){this.domStorage_=a,this.prefix_="firebase:"}set(a,b){null==b?this.domStorage_.removeItem(this.prefixedName_(a)):this.domStorage_.setItem(this.prefixedName_(a),(0,p.As)(b))}get(a){let b=this.domStorage_.getItem(this.prefixedName_(a));return null==b?null:(0,p.$L)(b)}remove(a){this.domStorage_.removeItem(this.prefixedName_(a))}prefixedName_(a){return this.prefix_+a}toString(){return this.domStorage_.toString()}}class x{constructor(){this.cache_={},this.isInMemoryStorage=!0}set(a,b){null==b?delete this.cache_[a]:this.cache_[a]=b}get(a){return(0,p.gR)(this.cache_,a)?this.cache_[a]:null}remove(a){delete this.cache_[a]}}let y=function(a){try{if("undefined"!=typeof window&&void 0!==window[a]){let b=window[a];return b.setItem("firebase:sentinel","cache"),b.removeItem("firebase:sentinel"),new w(b)}}catch(a){}return new x},z=y("localStorage"),A=y("sessionStorage"),B=new q.Vy("@firebase/database"),C=function(){let a=1;return function(){return a++}}(),D=function(a){let b=(0,p.kj)(a),c=new p.gz;c.update(b);let d=c.digest();return p.K3.encodeByteArray(d)},E=function(...a){let b="";for(let c=0;c<a.length;c++){let d=a[c];Array.isArray(d)||d&&"object"==typeof d&&"number"==typeof d.length?b+=E.apply(null,d):"object"==typeof d?b+=(0,p.As)(d):b+=d,b+=" "}return b},F=null,G=!0,H=function(a,b){(0,p.vA)(!b||!0===a||!1===a,"Can't turn on custom loggers persistently."),!0===a?(B.logLevel=q.$b.VERBOSE,F=B.log.bind(B),b&&A.set("logging_enabled",!0)):"function"==typeof a?F=a:(F=null,A.remove("logging_enabled"))},I=function(...a){if(!0===G&&(G=!1,null===F&&!0===A.get("logging_enabled")&&H(!0)),F){let b=E.apply(null,a);F(b)}},J=function(a){return function(...b){I(a,...b)}},K=function(...a){let b="FIREBASE INTERNAL ERROR: "+E(...a);B.error(b)},L=function(...a){let b=`FIREBASE FATAL ERROR: ${E(...a)}`;throw B.error(b),Error(b)},M=function(...a){let b="FIREBASE WARNING: "+E(...a);B.warn(b)},N=function(){"undefined"!=typeof window&&window.location&&window.location.protocol&&-1!==window.location.protocol.indexOf("https:")&&M("Insecure Firebase access from a secure page. Please use https in calls to new Firebase().")},O=function(a){return"number"==typeof a&&(a!=a||a===1/0||a===-1/0)},P=function(a){if((0,p.$g)()||"complete"===document.readyState)a();else{let b=!1,c=function(){if(!document.body)return void setTimeout(c,Math.floor(10));b||(b=!0,a())};document.addEventListener?(document.addEventListener("DOMContentLoaded",c,!1),window.addEventListener("load",c,!1)):document.attachEvent&&(document.attachEvent("onreadystatechange",()=>{"complete"===document.readyState&&c()}),window.attachEvent("onload",c))}},Q="[MIN_NAME]",R="[MAX_NAME]",S=function(a,b){if(a===b)return 0;{if(a===Q||b===R)return -1;if(b===Q||a===R)return 1;let c=$(a),d=$(b);if(null!==c)if(null!==d)return c-d==0?a.length-b.length:c-d;else return -1;return null!==d?1:a<b?-1:1}},T=function(a,b){return a===b?0:a<b?-1:1},U=function(a,b){if(b&&a in b)return b[a];throw Error("Missing required key ("+a+") in object: "+(0,p.As)(b))},V=function(a){if("object"!=typeof a||null===a)return(0,p.As)(a);let b=[];for(let c in a)b.push(c);b.sort();let c="{";for(let d=0;d<b.length;d++)0!==d&&(c+=","),c+=(0,p.As)(b[d]),c+=":",c+=V(a[b[d]]);return c+"}"},W=function(a,b){let c=a.length;if(c<=b)return[a];let d=[];for(let e=0;e<c;e+=b)e+b>c?d.push(a.substring(e,c)):d.push(a.substring(e,e+b));return d};function X(a,b){for(let c in a)a.hasOwnProperty(c)&&b(c,a[c])}let Y=function(a){let b,c,d,e,f;(0,p.vA)(!O(a),"Invalid JSON number");0===a?(c=0,d=0,b=+(1/a==-1/0)):(b=a<0,(a=Math.abs(a))>=22250738585072014e-324?(c=(e=Math.min(Math.floor(Math.log(a)/Math.LN2),1023))+1023,d=Math.round(a*Math.pow(2,52-e)-0x10000000000000)):(c=0,d=Math.round(a/5e-324)));let g=[];for(f=52;f;f-=1)g.push(d%2?1:0),d=Math.floor(d/2);for(f=11;f;f-=1)g.push(c%2?1:0),c=Math.floor(c/2);g.push(+!!b),g.reverse();let h=g.join(""),i="";for(f=0;f<64;f+=8){let a=parseInt(h.substr(f,8),2).toString(16);1===a.length&&(a="0"+a),i+=a}return i.toLowerCase()},Z=RegExp("^-?(0*)\\d{1,10}$"),$=function(a){if(Z.test(a)){let b=Number(a);if(b>=-0x80000000&&b<=0x7fffffff)return b}return null},_=function(a){try{a()}catch(a){setTimeout(()=>{throw M("Exception was thrown by user callback.",a.stack||""),a},Math.floor(0))}},aa=function(a,b){let c=setTimeout(a,b);return"number"==typeof c&&"undefined"!=typeof Deno&&Deno.unrefTimer?Deno.unrefTimer(c):"object"==typeof c&&c.unref&&c.unref(),c};class ab{constructor(a,b,c,d,e=!1,f="",g=!1,h=!1,i=null){this.secure=b,this.namespace=c,this.webSocketOnly=d,this.nodeAdmin=e,this.persistenceKey=f,this.includeNamespaceInQueryParams=g,this.isUsingEmulator=h,this.emulatorOptions=i,this._host=a.toLowerCase(),this._domain=this._host.substr(this._host.indexOf(".")+1),this.internalHost=z.get("host:"+a)||this._host}isCacheableHost(){return"s-"===this.internalHost.substr(0,2)}isCustomHost(){return"firebaseio.com"!==this._domain&&"firebaseio-demo.com"!==this._domain}get host(){return this._host}set host(a){a!==this.internalHost&&(this.internalHost=a,this.isCacheableHost()&&z.set("host:"+this._host,this.internalHost))}toString(){let a=this.toURLString();return this.persistenceKey&&(a+="<"+this.persistenceKey+">"),a}toURLString(){let a=this.secure?"https://":"http://",b=this.includeNamespaceInQueryParams?`?ns=${this.namespace}`:"";return`${a}${this.host}/${b}`}}function ac(a,b,c){let d;if((0,p.vA)("string"==typeof b,"typeof type must == string"),(0,p.vA)("object"==typeof c,"typeof params must == object"),b===u)d=(a.secure?"wss://":"ws://")+a.internalHost+"/.ws?";else if(b===v)d=(a.secure?"https://":"http://")+a.internalHost+"/.lp?";else throw Error("Unknown connection type: "+b);(a.host!==a.internalHost||a.isCustomHost()||a.includeNamespaceInQueryParams)&&(c.ns=a.namespace);let e=[];return X(c,(a,b)=>{e.push(a+"="+b)}),d+e.join("&")}class ad{constructor(){this.counters_={}}incrementCounter(a,b=1){(0,p.gR)(this.counters_,a)||(this.counters_[a]=0),this.counters_[a]+=b}get(){return(0,p.A4)(this.counters_)}}let ae={},af={};function ag(a){let b=a.toString();return ae[b]||(ae[b]=new ad),ae[b]}let ah="",ai=null;"undefined"!=typeof MozWebSocket?ai=MozWebSocket:"undefined"!=typeof WebSocket&&(ai=WebSocket);class aj{constructor(a,b,c,d,e,f,g){this.connId=a,this.applicationId=c,this.appCheckToken=d,this.authToken=e,this.keepaliveTimer=null,this.frames=null,this.totalFrames=0,this.bytesSent=0,this.bytesReceived=0,this.log_=J(this.connId),this.stats_=ag(b),this.connURL=aj.connectionURL_(b,f,g,d,c),this.nodeAdmin=b.nodeAdmin}static connectionURL_(a,b,c,d,e){let f={};return f.v="5",!(0,p.$g)()&&"undefined"!=typeof location&&location.hostname&&t.test(location.hostname)&&(f.r="f"),b&&(f.s=b),c&&(f.ls=c),d&&(f.ac=d),e&&(f.p=e),ac(a,u,f)}open(a,b){this.onDisconnect=b,this.onMessage=a,this.log_("Websocket connecting to "+this.connURL),this.everConnected_=!1,z.set("previous_websocket_failure",!0);try{let a;if((0,p.$g)()){let b=this.nodeAdmin?"AdminNode":"Node";a={headers:{"User-Agent":`Firebase/5/${ah}/${process.platform}/${b}`,"X-Firebase-GMPID":this.applicationId||""}},this.authToken&&(a.headers.Authorization=`Bearer ${this.authToken}`),this.appCheckToken&&(a.headers["X-Firebase-AppCheck"]=this.appCheckToken);let c=process.env,d=0===this.connURL.indexOf("wss://")?c.HTTPS_PROXY||c.https_proxy:c.HTTP_PROXY||c.http_proxy;d&&(a.proxy={origin:d})}this.mySock=new ai(this.connURL,[],a)}catch(b){this.log_("Error instantiating WebSocket.");let a=b.message||b.data;a&&this.log_(a),this.onClosed_();return}this.mySock.onopen=()=>{this.log_("Websocket connected."),this.everConnected_=!0},this.mySock.onclose=()=>{this.log_("Websocket connection was disconnected."),this.mySock=null,this.onClosed_()},this.mySock.onmessage=a=>{this.handleIncomingFrame(a)},this.mySock.onerror=a=>{this.log_("WebSocket error.  Closing connection.");let b=a.message||a.data;b&&this.log_(b),this.onClosed_()}}start(){}static forceDisallow(){aj.forceDisallow_=!0}static isAvailable(){let a=!1;if("undefined"!=typeof navigator&&navigator.userAgent){let b=navigator.userAgent.match(/Android ([0-9]{0,}\.[0-9]{0,})/);b&&b.length>1&&4.4>parseFloat(b[1])&&(a=!0)}return!a&&null!==ai&&!aj.forceDisallow_}static previouslyFailed(){return z.isInMemoryStorage||!0===z.get("previous_websocket_failure")}markConnectionHealthy(){z.remove("previous_websocket_failure")}appendFrame_(a){if(this.frames.push(a),this.frames.length===this.totalFrames){let a=this.frames.join("");this.frames=null;let b=(0,p.$L)(a);this.onMessage(b)}}handleNewFrameCount_(a){this.totalFrames=a,this.frames=[]}extractFrameCount_(a){if((0,p.vA)(null===this.frames,"We already have a frame buffer"),a.length<=6){let b=Number(a);if(!isNaN(b))return this.handleNewFrameCount_(b),null}return this.handleNewFrameCount_(1),a}handleIncomingFrame(a){if(null===this.mySock)return;let b=a.data;if(this.bytesReceived+=b.length,this.stats_.incrementCounter("bytes_received",b.length),this.resetKeepAlive(),null!==this.frames)this.appendFrame_(b);else{let a=this.extractFrameCount_(b);null!==a&&this.appendFrame_(a)}}send(a){this.resetKeepAlive();let b=(0,p.As)(a);this.bytesSent+=b.length,this.stats_.incrementCounter("bytes_sent",b.length);let c=W(b,16384);c.length>1&&this.sendString_(String(c.length));for(let a=0;a<c.length;a++)this.sendString_(c[a])}shutdown_(){this.isClosed_=!0,this.keepaliveTimer&&(clearInterval(this.keepaliveTimer),this.keepaliveTimer=null),this.mySock&&(this.mySock.close(),this.mySock=null)}onClosed_(){!this.isClosed_&&(this.log_("WebSocket is closing itself"),this.shutdown_(),this.onDisconnect&&(this.onDisconnect(this.everConnected_),this.onDisconnect=null))}close(){this.isClosed_||(this.log_("WebSocket is being closed"),this.shutdown_())}resetKeepAlive(){clearInterval(this.keepaliveTimer),this.keepaliveTimer=setInterval(()=>{this.mySock&&this.sendString_("0"),this.resetKeepAlive()},Math.floor(45e3))}sendString_(a){try{this.mySock.send(a)}catch(a){this.log_("Exception thrown from WebSocket.send():",a.message||a.data,"Closing connection."),setTimeout(this.onClosed_.bind(this),0)}}}aj.responsesRequiredToBeHealthy=2,aj.healthyTimeout=3e4;let ak="@firebase/database",al="1.1.0";class am{constructor(a,b){this.appCheckProvider=b,this.appName=a.name,(0,r.xZ)(a)&&a.settings.appCheckToken&&(this.serverAppAppCheckToken=a.settings.appCheckToken),this.appCheck=b?.getImmediate({optional:!0}),this.appCheck||b?.get().then(a=>this.appCheck=a)}getToken(a){if(this.serverAppAppCheckToken){if(a)throw Error("Attempted reuse of `FirebaseServerApp.appCheckToken` after previous usage failed.");return Promise.resolve({token:this.serverAppAppCheckToken})}return this.appCheck?this.appCheck.getToken(a):new Promise((b,c)=>{setTimeout(()=>{this.appCheck?this.getToken(a).then(b,c):b(null)},0)})}addTokenChangeListener(a){this.appCheckProvider?.get().then(b=>b.addTokenListener(a))}notifyForInvalidToken(){M(`Provided AppCheck credentials for the app named "${this.appName}" are invalid. This usually indicates your app was not initialized correctly.`)}}class an{constructor(a,b,c){this.appName_=a,this.firebaseOptions_=b,this.authProvider_=c,this.auth_=null,this.auth_=c.getImmediate({optional:!0}),this.auth_||c.onInit(a=>this.auth_=a)}getToken(a){return this.auth_?this.auth_.getToken(a).catch(a=>a&&"auth/token-not-initialized"===a.code?(I("Got auth/token-not-initialized error.  Treating as null token."),null):Promise.reject(a)):new Promise((b,c)=>{setTimeout(()=>{this.auth_?this.getToken(a).then(b,c):b(null)},0)})}addTokenChangeListener(a){this.auth_?this.auth_.addAuthTokenListener(a):this.authProvider_.get().then(b=>b.addAuthTokenListener(a))}removeTokenChangeListener(a){this.authProvider_.get().then(b=>b.removeAuthTokenListener(a))}notifyForInvalidToken(){let a='Provided authentication credentials for the app named "'+this.appName_+'" are invalid. This usually indicates your app was not initialized correctly. ';"credential"in this.firebaseOptions_?a+='Make sure the "credential" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':"serviceAccount"in this.firebaseOptions_?a+='Make sure the "serviceAccount" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':a+='Make sure the "apiKey" and "databaseURL" properties provided to initializeApp() match the values provided for your app at https://console.firebase.google.com/.',M(a)}}class ao{constructor(a){this.accessToken=a}getToken(a){return Promise.resolve({accessToken:this.accessToken})}addTokenChangeListener(a){a(this.accessToken)}removeTokenChangeListener(a){}notifyForInvalidToken(){}}ao.OWNER="owner";class ap{constructor(a){this.onMessage_=a,this.pendingResponses=[],this.currentResponseNum=0,this.closeAfterResponse=-1,this.onClose=null}closeAfter(a,b){this.closeAfterResponse=a,this.onClose=b,this.closeAfterResponse<this.currentResponseNum&&(this.onClose(),this.onClose=null)}handleResponse(a,b){for(this.pendingResponses[a]=b;this.pendingResponses[this.currentResponseNum];){let a=this.pendingResponses[this.currentResponseNum];delete this.pendingResponses[this.currentResponseNum];for(let b=0;b<a.length;++b)a[b]&&_(()=>{this.onMessage_(a[b])});if(this.currentResponseNum===this.closeAfterResponse){this.onClose&&(this.onClose(),this.onClose=null);break}this.currentResponseNum++}}}let aq="start";class ar{constructor(a,b,c,d,e,f,g){this.connId=a,this.repoInfo=b,this.applicationId=c,this.appCheckToken=d,this.authToken=e,this.transportSessionId=f,this.lastSessionId=g,this.bytesSent=0,this.bytesReceived=0,this.everConnected_=!1,this.log_=J(a),this.stats_=ag(b),this.urlFn=a=>(this.appCheckToken&&(a.ac=this.appCheckToken),ac(b,v,a))}open(a,b){this.curSegmentNum=0,this.onDisconnect_=b,this.myPacketOrderer=new ap(a),this.isClosed_=!1,this.connectTimeoutTimer_=setTimeout(()=>{this.log_("Timed out trying to connect."),this.onClosed_(),this.connectTimeoutTimer_=null},Math.floor(3e4)),P(()=>{if(this.isClosed_)return;this.scriptTagHolder=new as((...a)=>{let[b,c,d,e,f]=a;if(this.incrementIncomingBytes_(a),this.scriptTagHolder)if(this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null),this.everConnected_=!0,b===aq)this.id=c,this.password=d;else if("close"===b)c?(this.scriptTagHolder.sendNewPolls=!1,this.myPacketOrderer.closeAfter(c,()=>{this.onClosed_()})):this.onClosed_();else throw Error("Unrecognized command received: "+b)},(...a)=>{let[b,c]=a;this.incrementIncomingBytes_(a),this.myPacketOrderer.handleResponse(b,c)},()=>{this.onClosed_()},this.urlFn);let a={};a[aq]="t",a.ser=Math.floor(1e8*Math.random()),this.scriptTagHolder.uniqueCallbackIdentifier&&(a.cb=this.scriptTagHolder.uniqueCallbackIdentifier),a.v="5",this.transportSessionId&&(a.s=this.transportSessionId),this.lastSessionId&&(a.ls=this.lastSessionId),this.applicationId&&(a.p=this.applicationId),this.appCheckToken&&(a.ac=this.appCheckToken),"undefined"!=typeof location&&location.hostname&&t.test(location.hostname)&&(a.r="f");let b=this.urlFn(a);this.log_("Connecting via long-poll to "+b),this.scriptTagHolder.addTag(b,()=>{})})}start(){this.scriptTagHolder.startLongPoll(this.id,this.password),this.addDisconnectPingFrame(this.id,this.password)}static forceAllow(){ar.forceAllow_=!0}static forceDisallow(){ar.forceDisallow_=!0}static isAvailable(){return!(0,p.$g)()&&(!!ar.forceAllow_||!ar.forceDisallow_&&"undefined"!=typeof document&&null!=document.createElement&&!("object"==typeof window&&window.chrome&&window.chrome.extension&&!/^chrome/.test(window.location.href))&&("object"!=typeof Windows||"object"!=typeof Windows.UI))}markConnectionHealthy(){}shutdown_(){this.isClosed_=!0,this.scriptTagHolder&&(this.scriptTagHolder.close(),this.scriptTagHolder=null),this.myDisconnFrame&&(document.body.removeChild(this.myDisconnFrame),this.myDisconnFrame=null),this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null)}onClosed_(){!this.isClosed_&&(this.log_("Longpoll is closing itself"),this.shutdown_(),this.onDisconnect_&&(this.onDisconnect_(this.everConnected_),this.onDisconnect_=null))}close(){this.isClosed_||(this.log_("Longpoll is being closed."),this.shutdown_())}send(a){let b=(0,p.As)(a);this.bytesSent+=b.length,this.stats_.incrementCounter("bytes_sent",b.length);let c=W((0,p.KA)(b),1840);for(let a=0;a<c.length;a++)this.scriptTagHolder.enqueueSegment(this.curSegmentNum,c.length,c[a]),this.curSegmentNum++}addDisconnectPingFrame(a,b){if((0,p.$g)())return;this.myDisconnFrame=document.createElement("iframe");let c={};c.dframe="t",c.id=a,c.pw=b,this.myDisconnFrame.src=this.urlFn(c),this.myDisconnFrame.style.display="none",document.body.appendChild(this.myDisconnFrame)}incrementIncomingBytes_(a){let b=(0,p.As)(a).length;this.bytesReceived+=b,this.stats_.incrementCounter("bytes_received",b)}}class as{constructor(a,b,c,d){if(this.onDisconnect=c,this.urlFn=d,this.outstandingRequests=new Set,this.pendingSegs=[],this.currentSerial=Math.floor(1e8*Math.random()),this.sendNewPolls=!0,(0,p.$g)())this.commandCB=a,this.onMessageCB=b;else{this.uniqueCallbackIdentifier=C(),window["pLPCommand"+this.uniqueCallbackIdentifier]=a,window["pRTLPCB"+this.uniqueCallbackIdentifier]=b,this.myIFrame=as.createIFrame_();let c="";this.myIFrame.src&&"javascript:"===this.myIFrame.src.substr(0,11)&&(c='<script>document.domain="'+document.domain+'";<\/script>');let d="<html><body>"+c+"</body></html>";try{this.myIFrame.doc.open(),this.myIFrame.doc.write(d),this.myIFrame.doc.close()}catch(a){I("frame writing exception"),a.stack&&I(a.stack),I(a)}}}static createIFrame_(){let a=document.createElement("iframe");if(a.style.display="none",document.body){document.body.appendChild(a);try{a.contentWindow.document||I("No IE domain setting required")}catch(b){a.src="javascript:void((function(){document.open();document.domain='"+document.domain+"';document.close();})())"}}else throw"Document body has not initialized. Wait to initialize Firebase until after the document is ready.";return a.contentDocument?a.doc=a.contentDocument:a.contentWindow?a.doc=a.contentWindow.document:a.document&&(a.doc=a.document),a}close(){this.alive=!1,this.myIFrame&&(this.myIFrame.doc.body.textContent="",setTimeout(()=>{null!==this.myIFrame&&(document.body.removeChild(this.myIFrame),this.myIFrame=null)},Math.floor(0)));let a=this.onDisconnect;a&&(this.onDisconnect=null,a())}startLongPoll(a,b){for(this.myID=a,this.myPW=b,this.alive=!0;this.newRequest_(););}newRequest_(){if(!this.alive||!this.sendNewPolls||!(this.outstandingRequests.size<(this.pendingSegs.length>0?2:1)))return!1;{this.currentSerial++;let a={};a.id=this.myID,a.pw=this.myPW,a.ser=this.currentSerial;let b=this.urlFn(a),c="",d=0;for(;this.pendingSegs.length>0;)if(this.pendingSegs[0].d.length+30+c.length<=1870){let a=this.pendingSegs.shift();c=c+"&seg"+d+"="+a.seg+"&ts"+d+"="+a.ts+"&d"+d+"="+a.d,d++}else break;return b+=c,this.addLongPollTag_(b,this.currentSerial),!0}}enqueueSegment(a,b,c){this.pendingSegs.push({seg:a,ts:b,d:c}),this.alive&&this.newRequest_()}addLongPollTag_(a,b){this.outstandingRequests.add(b);let c=()=>{this.outstandingRequests.delete(b),this.newRequest_()},d=setTimeout(c,Math.floor(25e3));this.addTag(a,()=>{clearTimeout(d),c()})}addTag(a,b){(0,p.$g)()?this.doNodeLongPoll(a,b):setTimeout(()=>{try{if(!this.sendNewPolls)return;let c=this.myIFrame.doc.createElement("script");c.type="text/javascript",c.async=!0,c.src=a,c.onload=c.onreadystatechange=function(){let a=c.readyState;a&&"loaded"!==a&&"complete"!==a||(c.onload=c.onreadystatechange=null,c.parentNode&&c.parentNode.removeChild(c),b())},c.onerror=()=>{I("Long-poll script failed to load: "+a),this.sendNewPolls=!1,this.close()},this.myIFrame.doc.body.appendChild(c)}catch(a){}},Math.floor(1))}}class at{static get ALL_TRANSPORTS(){return[ar,aj]}static get IS_TRANSPORT_INITIALIZED(){return this.globalTransportInitialized_}constructor(a){this.initTransports_(a)}initTransports_(a){let b=aj&&aj.isAvailable(),c=b&&!aj.previouslyFailed();if(a.webSocketOnly&&(b||M("wss:// URL used, but browser isn't known to support websockets.  Trying anyway."),c=!0),c)this.transports_=[aj];else{let a=this.transports_=[];for(let b of at.ALL_TRANSPORTS)b&&b.isAvailable()&&a.push(b);at.globalTransportInitialized_=!0}}initialTransport(){if(this.transports_.length>0)return this.transports_[0];throw Error("No transports available")}upgradeTransport(){return this.transports_.length>1?this.transports_[1]:null}}at.globalTransportInitialized_=!1;class au{constructor(a,b,c,d,e,f,g,h,i,j){this.id=a,this.repoInfo_=b,this.applicationId_=c,this.appCheckToken_=d,this.authToken_=e,this.onMessage_=f,this.onReady_=g,this.onDisconnect_=h,this.onKill_=i,this.lastSessionId=j,this.connectionCount=0,this.pendingDataMessages=[],this.state_=0,this.log_=J("c:"+this.id+":"),this.transportManager_=new at(b),this.log_("Connection created"),this.start_()}start_(){let a=this.transportManager_.initialTransport();this.conn_=new a(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,null,this.lastSessionId),this.primaryResponsesRequired_=a.responsesRequiredToBeHealthy||0;let b=this.connReceiver_(this.conn_),c=this.disconnReceiver_(this.conn_);this.tx_=this.conn_,this.rx_=this.conn_,this.secondaryConn_=null,this.isHealthy_=!1,setTimeout(()=>{this.conn_&&this.conn_.open(b,c)},Math.floor(0));let d=a.healthyTimeout||0;d>0&&(this.healthyTimeout_=aa(()=>{this.healthyTimeout_=null,this.isHealthy_||(this.conn_&&this.conn_.bytesReceived>102400?(this.log_("Connection exceeded healthy timeout but has received "+this.conn_.bytesReceived+" bytes.  Marking connection healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()):this.conn_&&this.conn_.bytesSent>10240?this.log_("Connection exceeded healthy timeout but has sent "+this.conn_.bytesSent+" bytes.  Leaving connection alive."):(this.log_("Closing unhealthy connection after timeout."),this.close()))},Math.floor(d)))}nextTransportId_(){return"c:"+this.id+":"+this.connectionCount++}disconnReceiver_(a){return b=>{a===this.conn_?this.onConnectionLost_(b):a===this.secondaryConn_?(this.log_("Secondary connection lost."),this.onSecondaryConnectionLost_()):this.log_("closing an old connection")}}connReceiver_(a){return b=>{2!==this.state_&&(a===this.rx_?this.onPrimaryMessageReceived_(b):a===this.secondaryConn_?this.onSecondaryMessageReceived_(b):this.log_("message on old connection"))}}sendRequest(a){this.sendData_({t:"d",d:a})}tryCleanupConnection(){this.tx_===this.secondaryConn_&&this.rx_===this.secondaryConn_&&(this.log_("cleaning up and promoting a connection: "+this.secondaryConn_.connId),this.conn_=this.secondaryConn_,this.secondaryConn_=null)}onSecondaryControl_(a){if("t"in a){let b=a.t;"a"===b?this.upgradeIfSecondaryHealthy_():"r"===b?(this.log_("Got a reset on secondary, closing it"),this.secondaryConn_.close(),(this.tx_===this.secondaryConn_||this.rx_===this.secondaryConn_)&&this.close()):"o"===b&&(this.log_("got pong on secondary."),this.secondaryResponsesRequired_--,this.upgradeIfSecondaryHealthy_())}}onSecondaryMessageReceived_(a){let b=U("t",a),c=U("d",a);if("c"===b)this.onSecondaryControl_(c);else if("d"===b)this.pendingDataMessages.push(c);else throw Error("Unknown protocol layer: "+b)}upgradeIfSecondaryHealthy_(){this.secondaryResponsesRequired_<=0?(this.log_("Secondary connection is healthy."),this.isHealthy_=!0,this.secondaryConn_.markConnectionHealthy(),this.proceedWithUpgrade_()):(this.log_("sending ping on secondary."),this.secondaryConn_.send({t:"c",d:{t:"p",d:{}}}))}proceedWithUpgrade_(){this.secondaryConn_.start(),this.log_("sending client ack on secondary"),this.secondaryConn_.send({t:"c",d:{t:"a",d:{}}}),this.log_("Ending transmission on primary"),this.conn_.send({t:"c",d:{t:"n",d:{}}}),this.tx_=this.secondaryConn_,this.tryCleanupConnection()}onPrimaryMessageReceived_(a){let b=U("t",a),c=U("d",a);"c"===b?this.onControl_(c):"d"===b&&this.onDataMessage_(c)}onDataMessage_(a){this.onPrimaryResponse_(),this.onMessage_(a)}onPrimaryResponse_(){!this.isHealthy_&&(this.primaryResponsesRequired_--,this.primaryResponsesRequired_<=0&&(this.log_("Primary connection is healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()))}onControl_(a){let b=U("t",a);if("d"in a){let c=a.d;if("h"===b){let a={...c};this.repoInfo_.isUsingEmulator&&(a.h=this.repoInfo_.host),this.onHandshake_(a)}else if("n"===b){this.log_("recvd end transmission on primary"),this.rx_=this.secondaryConn_;for(let a=0;a<this.pendingDataMessages.length;++a)this.onDataMessage_(this.pendingDataMessages[a]);this.pendingDataMessages=[],this.tryCleanupConnection()}else"s"===b?this.onConnectionShutdown_(c):"r"===b?this.onReset_(c):"e"===b?K("Server Error: "+c):"o"===b?(this.log_("got pong on primary."),this.onPrimaryResponse_(),this.sendPingOnPrimaryIfNecessary_()):K("Unknown control packet command: "+b)}}onHandshake_(a){let b=a.ts,c=a.v,d=a.h;this.sessionId=a.s,this.repoInfo_.host=d,0===this.state_&&(this.conn_.start(),this.onConnectionEstablished_(this.conn_,b),"5"!==c&&M("Protocol version mismatch detected"),this.tryStartUpgrade_())}tryStartUpgrade_(){let a=this.transportManager_.upgradeTransport();a&&this.startUpgrade_(a)}startUpgrade_(a){this.secondaryConn_=new a(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,this.sessionId),this.secondaryResponsesRequired_=a.responsesRequiredToBeHealthy||0;let b=this.connReceiver_(this.secondaryConn_),c=this.disconnReceiver_(this.secondaryConn_);this.secondaryConn_.open(b,c),aa(()=>{this.secondaryConn_&&(this.log_("Timed out trying to upgrade."),this.secondaryConn_.close())},Math.floor(6e4))}onReset_(a){this.log_("Reset packet received.  New host: "+a),this.repoInfo_.host=a,1===this.state_?this.close():(this.closeConnections_(),this.start_())}onConnectionEstablished_(a,b){this.log_("Realtime connection established."),this.conn_=a,this.state_=1,this.onReady_&&(this.onReady_(b,this.sessionId),this.onReady_=null),0===this.primaryResponsesRequired_?(this.log_("Primary connection is healthy."),this.isHealthy_=!0):aa(()=>{this.sendPingOnPrimaryIfNecessary_()},Math.floor(5e3))}sendPingOnPrimaryIfNecessary_(){this.isHealthy_||1!==this.state_||(this.log_("sending ping on primary."),this.sendData_({t:"c",d:{t:"p",d:{}}}))}onSecondaryConnectionLost_(){let a=this.secondaryConn_;this.secondaryConn_=null,(this.tx_===a||this.rx_===a)&&this.close()}onConnectionLost_(a){this.conn_=null,a||0!==this.state_?1===this.state_&&this.log_("Realtime connection lost."):(this.log_("Realtime connection failed."),this.repoInfo_.isCacheableHost()&&(z.remove("host:"+this.repoInfo_.host),this.repoInfo_.internalHost=this.repoInfo_.host)),this.close()}onConnectionShutdown_(a){this.log_("Connection shutdown command received. Shutting down..."),this.onKill_&&(this.onKill_(a),this.onKill_=null),this.onDisconnect_=null,this.close()}sendData_(a){if(1!==this.state_)throw"Connection is not connected";this.tx_.send(a)}close(){2!==this.state_&&(this.log_("Closing realtime connection."),this.state_=2,this.closeConnections_(),this.onDisconnect_&&(this.onDisconnect_(),this.onDisconnect_=null))}closeConnections_(){this.log_("Shutting down all connections"),this.conn_&&(this.conn_.close(),this.conn_=null),this.secondaryConn_&&(this.secondaryConn_.close(),this.secondaryConn_=null),this.healthyTimeout_&&(clearTimeout(this.healthyTimeout_),this.healthyTimeout_=null)}}class av{put(a,b,c,d){}merge(a,b,c,d){}refreshAuthToken(a){}refreshAppCheckToken(a){}onDisconnectPut(a,b,c){}onDisconnectMerge(a,b,c){}onDisconnectCancel(a,b){}reportStats(a){}}class aw{constructor(a){this.allowedEvents_=a,this.listeners_={},(0,p.vA)(Array.isArray(a)&&a.length>0,"Requires a non-empty array")}trigger(a,...b){if(Array.isArray(this.listeners_[a])){let c=[...this.listeners_[a]];for(let a=0;a<c.length;a++)c[a].callback.apply(c[a].context,b)}}on(a,b,c){this.validateEventType_(a),this.listeners_[a]=this.listeners_[a]||[],this.listeners_[a].push({callback:b,context:c});let d=this.getInitialEvent(a);d&&b.apply(c,d)}off(a,b,c){this.validateEventType_(a);let d=this.listeners_[a]||[];for(let a=0;a<d.length;a++)if(d[a].callback===b&&(!c||c===d[a].context))return void d.splice(a,1)}validateEventType_(a){(0,p.vA)(this.allowedEvents_.find(b=>b===a),"Unknown event: "+a)}}class ax extends aw{static getInstance(){return new ax}constructor(){super(["online"]),this.online_=!0,"undefined"==typeof window||void 0===window.addEventListener||(0,p.jZ)()||(window.addEventListener("online",()=>{this.online_||(this.online_=!0,this.trigger("online",!0))},!1),window.addEventListener("offline",()=>{this.online_&&(this.online_=!1,this.trigger("online",!1))},!1))}getInitialEvent(a){return(0,p.vA)("online"===a,"Unknown event type: "+a),[this.online_]}currentlyOnline(){return this.online_}}class ay{constructor(a,b){if(void 0===b){this.pieces_=a.split("/");let b=0;for(let a=0;a<this.pieces_.length;a++)this.pieces_[a].length>0&&(this.pieces_[b]=this.pieces_[a],b++);this.pieces_.length=b,this.pieceNum_=0}else this.pieces_=a,this.pieceNum_=b}toString(){let a="";for(let b=this.pieceNum_;b<this.pieces_.length;b++)""!==this.pieces_[b]&&(a+="/"+this.pieces_[b]);return a||"/"}}function az(){return new ay("")}function aA(a){return a.pieceNum_>=a.pieces_.length?null:a.pieces_[a.pieceNum_]}function aB(a){return a.pieces_.length-a.pieceNum_}function aC(a){let b=a.pieceNum_;return b<a.pieces_.length&&b++,new ay(a.pieces_,b)}function aD(a){return a.pieceNum_<a.pieces_.length?a.pieces_[a.pieces_.length-1]:null}function aE(a,b=0){return a.pieces_.slice(a.pieceNum_+b)}function aF(a){if(a.pieceNum_>=a.pieces_.length)return null;let b=[];for(let c=a.pieceNum_;c<a.pieces_.length-1;c++)b.push(a.pieces_[c]);return new ay(b,0)}function aG(a,b){let c=[];for(let b=a.pieceNum_;b<a.pieces_.length;b++)c.push(a.pieces_[b]);if(b instanceof ay)for(let a=b.pieceNum_;a<b.pieces_.length;a++)c.push(b.pieces_[a]);else{let a=b.split("/");for(let b=0;b<a.length;b++)a[b].length>0&&c.push(a[b])}return new ay(c,0)}function aH(a){return a.pieceNum_>=a.pieces_.length}function aI(a,b){let c=aA(a),d=aA(b);if(null===c)return b;if(c===d)return aI(aC(a),aC(b));throw Error("INTERNAL ERROR: innerPath ("+b+") is not within outerPath ("+a+")")}function aJ(a,b){let c=aE(a,0),d=aE(b,0);for(let a=0;a<c.length&&a<d.length;a++){let b=S(c[a],d[a]);if(0!==b)return b}return c.length===d.length?0:c.length<d.length?-1:1}function aK(a,b){if(aB(a)!==aB(b))return!1;for(let c=a.pieceNum_,d=b.pieceNum_;c<=a.pieces_.length;c++,d++)if(a.pieces_[c]!==b.pieces_[d])return!1;return!0}function aL(a,b){let c=a.pieceNum_,d=b.pieceNum_;if(aB(a)>aB(b))return!1;for(;c<a.pieces_.length;){if(a.pieces_[c]!==b.pieces_[d])return!1;++c,++d}return!0}class aM{constructor(a,b){this.errorPrefix_=b,this.parts_=aE(a,0),this.byteLength_=Math.max(1,this.parts_.length);for(let a=0;a<this.parts_.length;a++)this.byteLength_+=(0,p.OE)(this.parts_[a]);aN(this)}}function aN(a){if(a.byteLength_>768)throw Error(a.errorPrefix_+"has a key path longer than 768 bytes ("+a.byteLength_+").");if(a.parts_.length>32)throw Error(a.errorPrefix_+"path specified exceeds the maximum depth that can be written (32) or object contains a cycle "+aO(a))}function aO(a){return 0===a.parts_.length?"":"in property '"+a.parts_.join(".")+"'"}class aP extends aw{static getInstance(){return new aP}constructor(){let a,b;super(["visible"]),"undefined"!=typeof document&&void 0!==document.addEventListener&&(void 0!==document.hidden?(b="visibilitychange",a="hidden"):void 0!==document.mozHidden?(b="mozvisibilitychange",a="mozHidden"):void 0!==document.msHidden?(b="msvisibilitychange",a="msHidden"):void 0!==document.webkitHidden&&(b="webkitvisibilitychange",a="webkitHidden")),this.visible_=!0,b&&document.addEventListener(b,()=>{let b=!document[a];b!==this.visible_&&(this.visible_=b,this.trigger("visible",b))},!1)}getInitialEvent(a){return(0,p.vA)("visible"===a,"Unknown event type: "+a),[this.visible_]}}class aQ extends av{constructor(a,b,c,d,e,f,g,h){if(super(),this.repoInfo_=a,this.applicationId_=b,this.onDataUpdate_=c,this.onConnectStatus_=d,this.onServerInfoUpdate_=e,this.authTokenProvider_=f,this.appCheckTokenProvider_=g,this.authOverride_=h,this.id=aQ.nextPersistentConnectionId_++,this.log_=J("p:"+this.id+":"),this.interruptReasons_={},this.listens=new Map,this.outstandingPuts_=[],this.outstandingGets_=[],this.outstandingPutCount_=0,this.outstandingGetCount_=0,this.onDisconnectRequestQueue_=[],this.connected_=!1,this.reconnectDelay_=1e3,this.maxReconnectDelay_=3e5,this.securityDebugCallback_=null,this.lastSessionId=null,this.establishConnectionTimer_=null,this.visible_=!1,this.requestCBHash_={},this.requestNumber_=0,this.realtime_=null,this.authToken_=null,this.appCheckToken_=null,this.forceTokenRefresh_=!1,this.invalidAuthTokenCount_=0,this.invalidAppCheckTokenCount_=0,this.firstConnection_=!0,this.lastConnectionAttemptTime_=null,this.lastConnectionEstablishedTime_=null,h&&!(0,p.$g)())throw Error("Auth override specified in options, but not supported on non Node.js platforms");aP.getInstance().on("visible",this.onVisible_,this),-1===a.host.indexOf("fblocal")&&ax.getInstance().on("online",this.onOnline_,this)}sendRequest(a,b,c){let d=++this.requestNumber_,e={r:d,a:a,b:b};this.log_((0,p.As)(e)),(0,p.vA)(this.connected_,"sendRequest call when we're not connected not allowed."),this.realtime_.sendRequest(e),c&&(this.requestCBHash_[d]=c)}get(a){this.initConnection_();let b=new p.cY,c={p:a._path.toString(),q:a._queryObject};this.outstandingGets_.push({action:"g",request:c,onComplete:a=>{let c=a.d;"ok"===a.s?b.resolve(c):b.reject(c)}}),this.outstandingGetCount_++;let d=this.outstandingGets_.length-1;return this.connected_&&this.sendGet_(d),b.promise}listen(a,b,c,d){this.initConnection_();let e=a._queryIdentifier,f=a._path.toString();this.log_("Listen called for "+f+" "+e),this.listens.has(f)||this.listens.set(f,new Map),(0,p.vA)(a._queryParams.isDefault()||!a._queryParams.loadsAllData(),"listen() called for non-default but complete query"),(0,p.vA)(!this.listens.get(f).has(e),"listen() called twice for same path/queryId.");let g={onComplete:d,hashFn:b,query:a,tag:c};this.listens.get(f).set(e,g),this.connected_&&this.sendListen_(g)}sendGet_(a){let b=this.outstandingGets_[a];this.sendRequest("g",b.request,c=>{delete this.outstandingGets_[a],this.outstandingGetCount_--,0===this.outstandingGetCount_&&(this.outstandingGets_=[]),b.onComplete&&b.onComplete(c)})}sendListen_(a){let b=a.query,c=b._path.toString(),d=b._queryIdentifier;this.log_("Listen on "+c+" for "+d);let e={p:c};a.tag&&(e.q=b._queryObject,e.t=a.tag),e.h=a.hashFn(),this.sendRequest("q",e,e=>{let f=e.d,g=e.s;aQ.warnOnListenWarnings_(f,b),(this.listens.get(c)&&this.listens.get(c).get(d))===a&&(this.log_("listen response",e),"ok"!==g&&this.removeListen_(c,d),a.onComplete&&a.onComplete(g,f))})}static warnOnListenWarnings_(a,b){if(a&&"object"==typeof a&&(0,p.gR)(a,"w")){let c=(0,p.yw)(a,"w");if(Array.isArray(c)&&~c.indexOf("no_index")){let a='".indexOn": "'+b._queryParams.getIndex().toString()+'"',c=b._path.toString();M(`Using an unspecified index. Your data will be downloaded and filtered on the client. Consider adding ${a} at ${c} to your security rules for better performance.`)}}}refreshAuthToken(a){this.authToken_=a,this.log_("Auth token refreshed"),this.authToken_?this.tryAuth():this.connected_&&this.sendRequest("unauth",{},()=>{}),this.reduceReconnectDelayIfAdminCredential_(a)}reduceReconnectDelayIfAdminCredential_(a){(a&&40===a.length||(0,p.qc)(a))&&(this.log_("Admin auth credential detected.  Reducing max reconnect time."),this.maxReconnectDelay_=3e4)}refreshAppCheckToken(a){this.appCheckToken_=a,this.log_("App check token refreshed"),this.appCheckToken_?this.tryAppCheck():this.connected_&&this.sendRequest("unappeck",{},()=>{})}tryAuth(){if(this.connected_&&this.authToken_){let a=this.authToken_,b=(0,p.Cv)(a)?"auth":"gauth",c={cred:a};null===this.authOverride_?c.noauth=!0:"object"==typeof this.authOverride_&&(c.authvar=this.authOverride_),this.sendRequest(b,c,b=>{let c=b.s,d=b.d||"error";this.authToken_===a&&("ok"===c?this.invalidAuthTokenCount_=0:this.onAuthRevoked_(c,d))})}}tryAppCheck(){this.connected_&&this.appCheckToken_&&this.sendRequest("appcheck",{token:this.appCheckToken_},a=>{let b=a.s,c=a.d||"error";"ok"===b?this.invalidAppCheckTokenCount_=0:this.onAppCheckRevoked_(b,c)})}unlisten(a,b){let c=a._path.toString(),d=a._queryIdentifier;this.log_("Unlisten called for "+c+" "+d),(0,p.vA)(a._queryParams.isDefault()||!a._queryParams.loadsAllData(),"unlisten() called for non-default but complete query"),this.removeListen_(c,d)&&this.connected_&&this.sendUnlisten_(c,d,a._queryObject,b)}sendUnlisten_(a,b,c,d){this.log_("Unlisten on "+a+" for "+b);let e={p:a};d&&(e.q=c,e.t=d),this.sendRequest("n",e)}onDisconnectPut(a,b,c){this.initConnection_(),this.connected_?this.sendOnDisconnect_("o",a,b,c):this.onDisconnectRequestQueue_.push({pathString:a,action:"o",data:b,onComplete:c})}onDisconnectMerge(a,b,c){this.initConnection_(),this.connected_?this.sendOnDisconnect_("om",a,b,c):this.onDisconnectRequestQueue_.push({pathString:a,action:"om",data:b,onComplete:c})}onDisconnectCancel(a,b){this.initConnection_(),this.connected_?this.sendOnDisconnect_("oc",a,null,b):this.onDisconnectRequestQueue_.push({pathString:a,action:"oc",data:null,onComplete:b})}sendOnDisconnect_(a,b,c,d){let e={p:b,d:c};this.log_("onDisconnect "+a,e),this.sendRequest(a,e,a=>{d&&setTimeout(()=>{d(a.s,a.d)},Math.floor(0))})}put(a,b,c,d){this.putInternal("p",a,b,c,d)}merge(a,b,c,d){this.putInternal("m",a,b,c,d)}putInternal(a,b,c,d,e){this.initConnection_();let f={p:b,d:c};void 0!==e&&(f.h=e),this.outstandingPuts_.push({action:a,request:f,onComplete:d}),this.outstandingPutCount_++;let g=this.outstandingPuts_.length-1;this.connected_?this.sendPut_(g):this.log_("Buffering put: "+b)}sendPut_(a){let b=this.outstandingPuts_[a].action,c=this.outstandingPuts_[a].request,d=this.outstandingPuts_[a].onComplete;this.outstandingPuts_[a].queued=this.connected_,this.sendRequest(b,c,c=>{this.log_(b+" response",c),delete this.outstandingPuts_[a],this.outstandingPutCount_--,0===this.outstandingPutCount_&&(this.outstandingPuts_=[]),d&&d(c.s,c.d)})}reportStats(a){if(this.connected_){let b={c:a};this.log_("reportStats",b),this.sendRequest("s",b,a=>{if("ok"!==a.s){let b=a.d;this.log_("reportStats","Error sending stats: "+b)}})}}onDataMessage_(a){if("r"in a){this.log_("from server: "+(0,p.As)(a));let b=a.r,c=this.requestCBHash_[b];c&&(delete this.requestCBHash_[b],c(a.b))}else if("error"in a)throw"A server-side error has occurred: "+a.error;else"a"in a&&this.onDataPush_(a.a,a.b)}onDataPush_(a,b){this.log_("handleServerMessage",a,b),"d"===a?this.onDataUpdate_(b.p,b.d,!1,b.t):"m"===a?this.onDataUpdate_(b.p,b.d,!0,b.t):"c"===a?this.onListenRevoked_(b.p,b.q):"ac"===a?this.onAuthRevoked_(b.s,b.d):"apc"===a?this.onAppCheckRevoked_(b.s,b.d):"sd"===a?this.onSecurityDebugPacket_(b):K("Unrecognized action received from server: "+(0,p.As)(a)+"\nAre you using the latest client?")}onReady_(a,b){this.log_("connection ready"),this.connected_=!0,this.lastConnectionEstablishedTime_=new Date().getTime(),this.handleTimestamp_(a),this.lastSessionId=b,this.firstConnection_&&this.sendConnectStats_(),this.restoreState_(),this.firstConnection_=!1,this.onConnectStatus_(!0)}scheduleConnect_(a){(0,p.vA)(!this.realtime_,"Scheduling a connect when we're already connected/ing?"),this.establishConnectionTimer_&&clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=setTimeout(()=>{this.establishConnectionTimer_=null,this.establishConnection_()},Math.floor(a))}initConnection_(){!this.realtime_&&this.firstConnection_&&this.scheduleConnect_(0)}onVisible_(a){a&&!this.visible_&&this.reconnectDelay_===this.maxReconnectDelay_&&(this.log_("Window became visible.  Reducing delay."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)),this.visible_=a}onOnline_(a){a?(this.log_("Browser went online."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)):(this.log_("Browser went offline.  Killing connection."),this.realtime_&&this.realtime_.close())}onRealtimeDisconnect_(){if(this.log_("data client disconnected"),this.connected_=!1,this.realtime_=null,this.cancelSentTransactions_(),this.requestCBHash_={},this.shouldReconnect_()){this.visible_?this.lastConnectionEstablishedTime_&&(new Date().getTime()-this.lastConnectionEstablishedTime_>3e4&&(this.reconnectDelay_=1e3),this.lastConnectionEstablishedTime_=null):(this.log_("Window isn't visible.  Delaying reconnect."),this.reconnectDelay_=this.maxReconnectDelay_,this.lastConnectionAttemptTime_=new Date().getTime());let a=Math.max(0,new Date().getTime()-this.lastConnectionAttemptTime_),b=Math.max(0,this.reconnectDelay_-a);b=Math.random()*b,this.log_("Trying to reconnect in "+b+"ms"),this.scheduleConnect_(b),this.reconnectDelay_=Math.min(this.maxReconnectDelay_,1.3*this.reconnectDelay_)}this.onConnectStatus_(!1)}async establishConnection_(){if(this.shouldReconnect_()){this.log_("Making a connection attempt"),this.lastConnectionAttemptTime_=new Date().getTime(),this.lastConnectionEstablishedTime_=null;let a=this.onDataMessage_.bind(this),b=this.onReady_.bind(this),c=this.onRealtimeDisconnect_.bind(this),d=this.id+":"+aQ.nextConnectionId_++,e=this.lastSessionId,f=!1,g=null,h=function(){g?g.close():(f=!0,c())};this.realtime_={close:h,sendRequest:function(a){(0,p.vA)(g,"sendRequest call when we're not connected not allowed."),g.sendRequest(a)}};let i=this.forceTokenRefresh_;this.forceTokenRefresh_=!1;try{let[h,j]=await Promise.all([this.authTokenProvider_.getToken(i),this.appCheckTokenProvider_.getToken(i)]);f?I("getToken() completed but was canceled"):(I("getToken() completed. Creating connection."),this.authToken_=h&&h.accessToken,this.appCheckToken_=j&&j.token,g=new au(d,this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,a,b,c,a=>{M(a+" ("+this.repoInfo_.toString()+")"),this.interrupt("server_kill")},e))}catch(a){this.log_("Failed to get token: "+a),f||(this.repoInfo_.nodeAdmin&&M(a),h())}}}interrupt(a){I("Interrupting connection for reason: "+a),this.interruptReasons_[a]=!0,this.realtime_?this.realtime_.close():(this.establishConnectionTimer_&&(clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=null),this.connected_&&this.onRealtimeDisconnect_())}resume(a){I("Resuming connection for reason: "+a),delete this.interruptReasons_[a],(0,p.Im)(this.interruptReasons_)&&(this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0))}handleTimestamp_(a){let b=a-new Date().getTime();this.onServerInfoUpdate_({serverTimeOffset:b})}cancelSentTransactions_(){for(let a=0;a<this.outstandingPuts_.length;a++){let b=this.outstandingPuts_[a];b&&"h"in b.request&&b.queued&&(b.onComplete&&b.onComplete("disconnect"),delete this.outstandingPuts_[a],this.outstandingPutCount_--)}0===this.outstandingPutCount_&&(this.outstandingPuts_=[])}onListenRevoked_(a,b){let c;c=b?b.map(a=>V(a)).join("$"):"default";let d=this.removeListen_(a,c);d&&d.onComplete&&d.onComplete("permission_denied")}removeListen_(a,b){let c,d=new ay(a).toString();if(this.listens.has(d)){let a=this.listens.get(d);c=a.get(b),a.delete(b),0===a.size&&this.listens.delete(d)}else c=void 0;return c}onAuthRevoked_(a,b){I("Auth token revoked: "+a+"/"+b),this.authToken_=null,this.forceTokenRefresh_=!0,this.realtime_.close(),("invalid_token"===a||"permission_denied"===a)&&(this.invalidAuthTokenCount_++,this.invalidAuthTokenCount_>=3&&(this.reconnectDelay_=3e4,this.authTokenProvider_.notifyForInvalidToken()))}onAppCheckRevoked_(a,b){I("App check token revoked: "+a+"/"+b),this.appCheckToken_=null,this.forceTokenRefresh_=!0,("invalid_token"===a||"permission_denied"===a)&&(this.invalidAppCheckTokenCount_++,this.invalidAppCheckTokenCount_>=3&&this.appCheckTokenProvider_.notifyForInvalidToken())}onSecurityDebugPacket_(a){this.securityDebugCallback_?this.securityDebugCallback_(a):"msg"in a&&console.log("FIREBASE: "+a.msg.replace("\n","\nFIREBASE: "))}restoreState_(){for(let a of(this.tryAuth(),this.tryAppCheck(),this.listens.values()))for(let b of a.values())this.sendListen_(b);for(let a=0;a<this.outstandingPuts_.length;a++)this.outstandingPuts_[a]&&this.sendPut_(a);for(;this.onDisconnectRequestQueue_.length;){let a=this.onDisconnectRequestQueue_.shift();this.sendOnDisconnect_(a.action,a.pathString,a.data,a.onComplete)}for(let a=0;a<this.outstandingGets_.length;a++)this.outstandingGets_[a]&&this.sendGet_(a)}sendConnectStats_(){let a={},b="js";(0,p.$g)()&&(b=this.repoInfo_.nodeAdmin?"admin_node":"node"),a["sdk."+b+"."+ah.replace(/\./g,"-")]=1,(0,p.jZ)()?a["framework.cordova"]=1:(0,p.lV)()&&(a["framework.reactnative"]=1),this.reportStats(a)}shouldReconnect_(){let a=ax.getInstance().currentlyOnline();return(0,p.Im)(this.interruptReasons_)&&a}}aQ.nextPersistentConnectionId_=0,aQ.nextConnectionId_=0;class aR{constructor(a,b){this.name=a,this.node=b}static Wrap(a,b){return new aR(a,b)}}class aS{getCompare(){return this.compare.bind(this)}indexedValueChanged(a,b){let c=new aR(Q,a),d=new aR(Q,b);return 0!==this.compare(c,d)}minPost(){return aR.MIN}}class aT extends aS{static get __EMPTY_NODE(){return d}static set __EMPTY_NODE(a){d=a}compare(a,b){return S(a.name,b.name)}isDefinedOn(a){throw(0,p.Hk)("KeyIndex.isDefinedOn not expected to be called.")}indexedValueChanged(a,b){return!1}minPost(){return aR.MIN}maxPost(){return new aR(R,d)}makePost(a,b){return(0,p.vA)("string"==typeof a,"KeyIndex indexValue must always be a string."),new aR(a,d)}toString(){return".key"}}let aU=new aT;class aV{constructor(a,b,c,d,e=null){this.isReverse_=d,this.resultGenerator_=e,this.nodeStack_=[];let f=1;for(;!a.isEmpty();)if(f=b?c(a.key,b):1,d&&(f*=-1),f<0)a=this.isReverse_?a.left:a.right;else if(0===f){this.nodeStack_.push(a);break}else this.nodeStack_.push(a),a=this.isReverse_?a.right:a.left}getNext(){let a;if(0===this.nodeStack_.length)return null;let b=this.nodeStack_.pop();if(a=this.resultGenerator_?this.resultGenerator_(b.key,b.value):{key:b.key,value:b.value},this.isReverse_)for(b=b.left;!b.isEmpty();)this.nodeStack_.push(b),b=b.right;else for(b=b.right;!b.isEmpty();)this.nodeStack_.push(b),b=b.left;return a}hasNext(){return this.nodeStack_.length>0}peek(){if(0===this.nodeStack_.length)return null;let a=this.nodeStack_[this.nodeStack_.length-1];return this.resultGenerator_?this.resultGenerator_(a.key,a.value):{key:a.key,value:a.value}}}class aW{constructor(a,b,c,d,e){this.key=a,this.value=b,this.color=null!=c?c:aW.RED,this.left=null!=d?d:aY.EMPTY_NODE,this.right=null!=e?e:aY.EMPTY_NODE}copy(a,b,c,d,e){return new aW(null!=a?a:this.key,null!=b?b:this.value,null!=c?c:this.color,null!=d?d:this.left,null!=e?e:this.right)}count(){return this.left.count()+1+this.right.count()}isEmpty(){return!1}inorderTraversal(a){return this.left.inorderTraversal(a)||!!a(this.key,this.value)||this.right.inorderTraversal(a)}reverseTraversal(a){return this.right.reverseTraversal(a)||a(this.key,this.value)||this.left.reverseTraversal(a)}min_(){return this.left.isEmpty()?this:this.left.min_()}minKey(){return this.min_().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(a,b,c){let d=this,e=c(a,d.key);return(d=e<0?d.copy(null,null,null,d.left.insert(a,b,c),null):0===e?d.copy(null,b,null,null,null):d.copy(null,null,null,null,d.right.insert(a,b,c))).fixUp_()}removeMin_(){if(this.left.isEmpty())return aY.EMPTY_NODE;let a=this;return a.left.isRed_()||a.left.left.isRed_()||(a=a.moveRedLeft_()),(a=a.copy(null,null,null,a.left.removeMin_(),null)).fixUp_()}remove(a,b){let c,d;if(c=this,0>b(a,c.key))c.left.isEmpty()||c.left.isRed_()||c.left.left.isRed_()||(c=c.moveRedLeft_()),c=c.copy(null,null,null,c.left.remove(a,b),null);else{if(c.left.isRed_()&&(c=c.rotateRight_()),c.right.isEmpty()||c.right.isRed_()||c.right.left.isRed_()||(c=c.moveRedRight_()),0===b(a,c.key))if(c.right.isEmpty())return aY.EMPTY_NODE;else d=c.right.min_(),c=c.copy(d.key,d.value,null,null,c.right.removeMin_());c=c.copy(null,null,null,null,c.right.remove(a,b))}return c.fixUp_()}isRed_(){return this.color}fixUp_(){let a=this;return a.right.isRed_()&&!a.left.isRed_()&&(a=a.rotateLeft_()),a.left.isRed_()&&a.left.left.isRed_()&&(a=a.rotateRight_()),a.left.isRed_()&&a.right.isRed_()&&(a=a.colorFlip_()),a}moveRedLeft_(){let a=this.colorFlip_();return a.right.left.isRed_()&&(a=(a=(a=a.copy(null,null,null,null,a.right.rotateRight_())).rotateLeft_()).colorFlip_()),a}moveRedRight_(){let a=this.colorFlip_();return a.left.left.isRed_()&&(a=(a=a.rotateRight_()).colorFlip_()),a}rotateLeft_(){let a=this.copy(null,null,aW.RED,null,this.right.left);return this.right.copy(null,null,this.color,a,null)}rotateRight_(){let a=this.copy(null,null,aW.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,a)}colorFlip_(){let a=this.left.copy(null,null,!this.left.color,null,null),b=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,a,b)}checkMaxDepth_(){return Math.pow(2,this.check_())<=this.count()+1}check_(){if(this.isRed_()&&this.left.isRed_())throw Error("Red node has red child("+this.key+","+this.value+")");if(this.right.isRed_())throw Error("Right child of ("+this.key+","+this.value+") is red");let a=this.left.check_();if(a===this.right.check_())return a+ +!this.isRed_();throw Error("Black depths differ")}}aW.RED=!0,aW.BLACK=!1;class aX{copy(a,b,c,d,e){return this}insert(a,b,c){return new aW(a,b,null)}remove(a,b){return this}count(){return 0}isEmpty(){return!0}inorderTraversal(a){return!1}reverseTraversal(a){return!1}minKey(){return null}maxKey(){return null}check_(){return 0}isRed_(){return!1}}class aY{constructor(a,b=aY.EMPTY_NODE){this.comparator_=a,this.root_=b}insert(a,b){return new aY(this.comparator_,this.root_.insert(a,b,this.comparator_).copy(null,null,aW.BLACK,null,null))}remove(a){return new aY(this.comparator_,this.root_.remove(a,this.comparator_).copy(null,null,aW.BLACK,null,null))}get(a){let b,c=this.root_;for(;!c.isEmpty();){if(0===(b=this.comparator_(a,c.key)))return c.value;b<0?c=c.left:b>0&&(c=c.right)}return null}getPredecessorKey(a){let b,c=this.root_,d=null;for(;!c.isEmpty();){if(0===(b=this.comparator_(a,c.key)))if(c.left.isEmpty())if(d)return d.key;else return null;else{for(c=c.left;!c.right.isEmpty();)c=c.right;return c.key}b<0?c=c.left:b>0&&(d=c,c=c.right)}throw Error("Attempted to find predecessor key for a nonexistent key.  What gives?")}isEmpty(){return this.root_.isEmpty()}count(){return this.root_.count()}minKey(){return this.root_.minKey()}maxKey(){return this.root_.maxKey()}inorderTraversal(a){return this.root_.inorderTraversal(a)}reverseTraversal(a){return this.root_.reverseTraversal(a)}getIterator(a){return new aV(this.root_,null,this.comparator_,!1,a)}getIteratorFrom(a,b){return new aV(this.root_,a,this.comparator_,!1,b)}getReverseIteratorFrom(a,b){return new aV(this.root_,a,this.comparator_,!0,b)}getReverseIterator(a){return new aV(this.root_,null,this.comparator_,!0,a)}}function aZ(a,b){return S(a.name,b.name)}function a$(a,b){return S(a,b)}aY.EMPTY_NODE=new aX;let a_=function(a){return"number"==typeof a?"number:"+Y(a):"string:"+a},a0=function(a){if(a.isLeafNode()){let b=a.val();(0,p.vA)("string"==typeof b||"number"==typeof b||"object"==typeof b&&(0,p.gR)(b,".sv"),"Priority must be a string or number.")}else(0,p.vA)(a===e||a.isEmpty(),"priority of unexpected type.");(0,p.vA)(a===e||a.getPriority().isEmpty(),"Priority nodes can't have a priority of their own.")};class a1{static set __childrenNodeConstructor(a){f=a}static get __childrenNodeConstructor(){return f}constructor(a,b=a1.__childrenNodeConstructor.EMPTY_NODE){this.value_=a,this.priorityNode_=b,this.lazyHash_=null,(0,p.vA)(void 0!==this.value_&&null!==this.value_,"LeafNode shouldn't be created with null/undefined value."),a0(this.priorityNode_)}isLeafNode(){return!0}getPriority(){return this.priorityNode_}updatePriority(a){return new a1(this.value_,a)}getImmediateChild(a){return".priority"===a?this.priorityNode_:a1.__childrenNodeConstructor.EMPTY_NODE}getChild(a){return aH(a)?this:".priority"===aA(a)?this.priorityNode_:a1.__childrenNodeConstructor.EMPTY_NODE}hasChild(){return!1}getPredecessorChildName(a,b){return null}updateImmediateChild(a,b){return".priority"===a?this.updatePriority(b):b.isEmpty()&&".priority"!==a?this:a1.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(a,b).updatePriority(this.priorityNode_)}updateChild(a,b){let c=aA(a);return null===c?b:b.isEmpty()&&".priority"!==c?this:((0,p.vA)(".priority"!==c||1===aB(a),".priority must be the last token in a path"),this.updateImmediateChild(c,a1.__childrenNodeConstructor.EMPTY_NODE.updateChild(aC(a),b)))}isEmpty(){return!1}numChildren(){return 0}forEachChild(a,b){return!1}val(a){return a&&!this.getPriority().isEmpty()?{".value":this.getValue(),".priority":this.getPriority().val()}:this.getValue()}hash(){if(null===this.lazyHash_){let a="";this.priorityNode_.isEmpty()||(a+="priority:"+a_(this.priorityNode_.val())+":");let b=typeof this.value_;a+=b+":","number"===b?a+=Y(this.value_):a+=this.value_,this.lazyHash_=D(a)}return this.lazyHash_}getValue(){return this.value_}compareTo(a){return a===a1.__childrenNodeConstructor.EMPTY_NODE?1:a instanceof a1.__childrenNodeConstructor?-1:((0,p.vA)(a.isLeafNode(),"Unknown node type"),this.compareToLeafNode_(a))}compareToLeafNode_(a){let b=typeof a.value_,c=typeof this.value_,d=a1.VALUE_TYPE_ORDER.indexOf(b),e=a1.VALUE_TYPE_ORDER.indexOf(c);return((0,p.vA)(d>=0,"Unknown leaf type: "+b),(0,p.vA)(e>=0,"Unknown leaf type: "+c),d!==e)?e-d:"object"===c?0:this.value_<a.value_?-1:1*(this.value_!==a.value_)}withIndex(){return this}isIndexed(){return!0}equals(a){return a===this||!!a.isLeafNode()&&this.value_===a.value_&&this.priorityNode_.equals(a.priorityNode_)}}a1.VALUE_TYPE_ORDER=["object","boolean","number","string"];class a2 extends aS{compare(a,b){let c=a.node.getPriority(),d=b.node.getPriority(),e=c.compareTo(d);return 0===e?S(a.name,b.name):e}isDefinedOn(a){return!a.getPriority().isEmpty()}indexedValueChanged(a,b){return!a.getPriority().equals(b.getPriority())}minPost(){return aR.MIN}maxPost(){return new aR(R,new a1("[PRIORITY-POST]",h))}makePost(a,b){return new aR(b,new a1("[PRIORITY-POST]",g(a)))}toString(){return".priority"}}let a3=new a2,a4=Math.log(2);class a5{constructor(a){this.count=parseInt(Math.log(a+1)/a4,10),this.current_=this.count-1;let b=parseInt(Array(this.count+1).join("1"),2);this.bits_=a+1&b}nextBitIsOne(){let a=!(this.bits_&1<<this.current_);return this.current_--,a}}let a6=function(a,b,c,d){a.sort(b);let e=function(b,d){let f,g=d-b;if(0===g)return null;if(1===g)return f=a[b],new aW(c?c(f):f,f.node,aW.BLACK,null,null);{let h=parseInt(g/2,10)+b,i=e(b,h),j=e(h+1,d);return f=a[h],new aW(c?c(f):f,f.node,aW.BLACK,i,j)}};return new aY(d||b,function(b){let d=null,f=null,g=a.length,h=function(b,d){let f=g-b,h=g;g-=b;let j=e(f+1,h),k=a[f];i(new aW(c?c(k):k,k.node,d,null,j))},i=function(a){d?d.left=a:f=a,d=a};for(let a=0;a<b.count;++a){let c=b.nextBitIsOne(),d=Math.pow(2,b.count-(a+1));c?h(d,aW.BLACK):(h(d,aW.BLACK),h(d,aW.RED))}return f}(new a5(a.length)))},a7={};class a8{static get Default(){return(0,p.vA)(a7&&a3,"ChildrenNode.ts has not been loaded"),i=i||new a8({".priority":a7},{".priority":a3})}constructor(a,b){this.indexes_=a,this.indexSet_=b}get(a){let b=(0,p.yw)(this.indexes_,a);if(!b)throw Error("No index defined for "+a);return b instanceof aY?b:null}hasIndex(a){return(0,p.gR)(this.indexSet_,a.toString())}addIndex(a,b){let c;(0,p.vA)(a!==aU,"KeyIndex always exists and isn't meant to be added to the IndexMap.");let d=[],e=!1,f=b.getIterator(aR.Wrap),g=f.getNext();for(;g;)e=e||a.isDefinedOn(g.node),d.push(g),g=f.getNext();c=e?a6(d,a.getCompare()):a7;let h=a.toString(),i={...this.indexSet_};i[h]=a;let j={...this.indexes_};return j[h]=c,new a8(j,i)}addToIndexes(a,b){return new a8((0,p.kH)(this.indexes_,(c,d)=>{let e=(0,p.yw)(this.indexSet_,d);if((0,p.vA)(e,"Missing index implementation for "+d),c===a7)if(!e.isDefinedOn(a.node))return a7;else{let c=[],d=b.getIterator(aR.Wrap),f=d.getNext();for(;f;)f.name!==a.name&&c.push(f),f=d.getNext();return c.push(a),a6(c,e.getCompare())}{let d=b.get(a.name),e=c;return d&&(e=e.remove(new aR(a.name,d))),e.insert(a,a.node)}}),this.indexSet_)}removeFromIndexes(a,b){return new a8((0,p.kH)(this.indexes_,c=>{if(c===a7)return c;{let d=b.get(a.name);return d?c.remove(new aR(a.name,d)):c}}),this.indexSet_)}}class a9{static get EMPTY_NODE(){return j||(j=new a9(new aY(a$),null,a8.Default))}constructor(a,b,c){this.children_=a,this.priorityNode_=b,this.indexMap_=c,this.lazyHash_=null,this.priorityNode_&&a0(this.priorityNode_),this.children_.isEmpty()&&(0,p.vA)(!this.priorityNode_||this.priorityNode_.isEmpty(),"An empty node cannot have a priority")}isLeafNode(){return!1}getPriority(){return this.priorityNode_||j}updatePriority(a){return this.children_.isEmpty()?this:new a9(this.children_,a,this.indexMap_)}getImmediateChild(a){if(".priority"===a)return this.getPriority();{let b=this.children_.get(a);return null===b?j:b}}getChild(a){let b=aA(a);return null===b?this:this.getImmediateChild(b).getChild(aC(a))}hasChild(a){return null!==this.children_.get(a)}updateImmediateChild(a,b){if((0,p.vA)(b,"We should always be passing snapshot nodes"),".priority"===a)return this.updatePriority(b);{let c,d,e=new aR(a,b);b.isEmpty()?(c=this.children_.remove(a),d=this.indexMap_.removeFromIndexes(e,this.children_)):(c=this.children_.insert(a,b),d=this.indexMap_.addToIndexes(e,this.children_));let f=c.isEmpty()?j:this.priorityNode_;return new a9(c,f,d)}}updateChild(a,b){let c=aA(a);if(null===c)return b;{(0,p.vA)(".priority"!==aA(a)||1===aB(a),".priority must be the last token in a path");let d=this.getImmediateChild(c).updateChild(aC(a),b);return this.updateImmediateChild(c,d)}}isEmpty(){return this.children_.isEmpty()}numChildren(){return this.children_.count()}val(a){if(this.isEmpty())return null;let b={},c=0,d=0,e=!0;if(this.forEachChild(a3,(f,g)=>{b[f]=g.val(a),c++,e&&a9.INTEGER_REGEXP_.test(f)?d=Math.max(d,Number(f)):e=!1}),a||!e||!(d<2*c))return a&&!this.getPriority().isEmpty()&&(b[".priority"]=this.getPriority().val()),b;{let a=[];for(let c in b)a[c]=b[c];return a}}hash(){if(null===this.lazyHash_){let a="";this.getPriority().isEmpty()||(a+="priority:"+a_(this.getPriority().val())+":"),this.forEachChild(a3,(b,c)=>{let d=c.hash();""!==d&&(a+=":"+b+":"+d)}),this.lazyHash_=""===a?"":D(a)}return this.lazyHash_}getPredecessorChildName(a,b,c){let d=this.resolveIndex_(c);if(!d)return this.children_.getPredecessorKey(a);{let c=d.getPredecessorKey(new aR(a,b));return c?c.name:null}}getFirstChildName(a){let b=this.resolveIndex_(a);if(!b)return this.children_.minKey();{let a=b.minKey();return a&&a.name}}getFirstChild(a){let b=this.getFirstChildName(a);return b?new aR(b,this.children_.get(b)):null}getLastChildName(a){let b=this.resolveIndex_(a);if(!b)return this.children_.maxKey();{let a=b.maxKey();return a&&a.name}}getLastChild(a){let b=this.getLastChildName(a);return b?new aR(b,this.children_.get(b)):null}forEachChild(a,b){let c=this.resolveIndex_(a);return c?c.inorderTraversal(a=>b(a.name,a.node)):this.children_.inorderTraversal(b)}getIterator(a){return this.getIteratorFrom(a.minPost(),a)}getIteratorFrom(a,b){let c=this.resolveIndex_(b);if(c)return c.getIteratorFrom(a,a=>a);{let c=this.children_.getIteratorFrom(a.name,aR.Wrap),d=c.peek();for(;null!=d&&0>b.compare(d,a);)c.getNext(),d=c.peek();return c}}getReverseIterator(a){return this.getReverseIteratorFrom(a.maxPost(),a)}getReverseIteratorFrom(a,b){let c=this.resolveIndex_(b);if(c)return c.getReverseIteratorFrom(a,a=>a);{let c=this.children_.getReverseIteratorFrom(a.name,aR.Wrap),d=c.peek();for(;null!=d&&b.compare(d,a)>0;)c.getNext(),d=c.peek();return c}}compareTo(a){if(this.isEmpty())if(a.isEmpty())return 0;else return -1;return a.isLeafNode()||a.isEmpty()?1:a===bb?-1:0}withIndex(a){if(a===aU||this.indexMap_.hasIndex(a))return this;{let b=this.indexMap_.addIndex(a,this.children_);return new a9(this.children_,this.priorityNode_,b)}}isIndexed(a){return a===aU||this.indexMap_.hasIndex(a)}equals(a){if(a===this)return!0;if(a.isLeafNode()||!this.getPriority().equals(a.getPriority()))return!1;if(this.children_.count()!==a.children_.count())return!1;{let b=this.getIterator(a3),c=a.getIterator(a3),d=b.getNext(),e=c.getNext();for(;d&&e;){if(d.name!==e.name||!d.node.equals(e.node))return!1;d=b.getNext(),e=c.getNext()}return null===d&&null===e}}resolveIndex_(a){return a===aU?null:this.indexMap_.get(a.toString())}}a9.INTEGER_REGEXP_=/^(0|[1-9]\d*)$/;class ba extends a9{constructor(){super(new aY(a$),a9.EMPTY_NODE,a8.Default)}compareTo(a){return+(a!==this)}equals(a){return a===this}getPriority(){return this}getImmediateChild(a){return a9.EMPTY_NODE}isEmpty(){return!1}}let bb=new ba;function bc(a,b=null){if(null===a)return a9.EMPTY_NODE;if("object"==typeof a&&".priority"in a&&(b=a[".priority"]),(0,p.vA)(null===b||"string"==typeof b||"number"==typeof b||"object"==typeof b&&".sv"in b,"Invalid priority type found: "+typeof b),"object"==typeof a&&".value"in a&&null!==a[".value"]&&(a=a[".value"]),"object"!=typeof a||".sv"in a)return new a1(a,bc(b));if(a instanceof Array){let c=a9.EMPTY_NODE;return X(a,(b,d)=>{if((0,p.gR)(a,b)&&"."!==b.substring(0,1)){let a=bc(d);(a.isLeafNode()||!a.isEmpty())&&(c=c.updateImmediateChild(b,a))}}),c.updatePriority(bc(b))}{let c=[],d=!1;if(X(a,(a,b)=>{if("."!==a.substring(0,1)){let e=bc(b);e.isEmpty()||(d=d||!e.getPriority().isEmpty(),c.push(new aR(a,e)))}}),0===c.length)return a9.EMPTY_NODE;let e=a6(c,aZ,a=>a.name,a$);if(!d)return new a9(e,bc(b),a8.Default);{let a=a6(c,a3.getCompare());return new a9(e,bc(b),new a8({".priority":a},{".priority":a3}))}}}Object.defineProperties(aR,{MIN:{value:new aR(Q,a9.EMPTY_NODE)},MAX:{value:new aR(R,bb)}}),aT.__EMPTY_NODE=a9.EMPTY_NODE,a1.__childrenNodeConstructor=a9,e=bb,h=bb,g=bc;class bd extends aS{constructor(a){super(),this.indexPath_=a,(0,p.vA)(!aH(a)&&".priority"!==aA(a),"Can't create PathIndex with empty path or .priority key")}extractChild(a){return a.getChild(this.indexPath_)}isDefinedOn(a){return!a.getChild(this.indexPath_).isEmpty()}compare(a,b){let c=this.extractChild(a.node),d=this.extractChild(b.node),e=c.compareTo(d);return 0===e?S(a.name,b.name):e}makePost(a,b){let c=bc(a);return new aR(b,a9.EMPTY_NODE.updateChild(this.indexPath_,c))}maxPost(){return new aR(R,a9.EMPTY_NODE.updateChild(this.indexPath_,bb))}toString(){return aE(this.indexPath_,0).join("/")}}class be extends aS{compare(a,b){let c=a.node.compareTo(b.node);return 0===c?S(a.name,b.name):c}isDefinedOn(a){return!0}indexedValueChanged(a,b){return!a.equals(b)}minPost(){return aR.MIN}maxPost(){return aR.MAX}makePost(a,b){return new aR(b,bc(a))}toString(){return".value"}}let bf=new be;function bg(a){return{type:"value",snapshotNode:a}}function bh(a,b){return{type:"child_added",snapshotNode:b,childName:a}}function bi(a,b){return{type:"child_removed",snapshotNode:b,childName:a}}function bj(a,b,c){return{type:"child_changed",snapshotNode:b,childName:a,oldSnap:c}}class bk{constructor(a){this.index_=a}updateChild(a,b,c,d,e,f){(0,p.vA)(a.isIndexed(this.index_),"A node must be indexed if only a child is updated");let g=a.getImmediateChild(b);return g.getChild(d).equals(c.getChild(d))&&g.isEmpty()===c.isEmpty()||(null!=f&&(c.isEmpty()?a.hasChild(b)?f.trackChildChange(bi(b,g)):(0,p.vA)(a.isLeafNode(),"A child remove without an old child only makes sense on a leaf node"):g.isEmpty()?f.trackChildChange(bh(b,c)):f.trackChildChange(bj(b,c,g))),a.isLeafNode()&&c.isEmpty())?a:a.updateImmediateChild(b,c).withIndex(this.index_)}updateFullNode(a,b,c){return null!=c&&(a.isLeafNode()||a.forEachChild(a3,(a,d)=>{b.hasChild(a)||c.trackChildChange(bi(a,d))}),b.isLeafNode()||b.forEachChild(a3,(b,d)=>{if(a.hasChild(b)){let e=a.getImmediateChild(b);e.equals(d)||c.trackChildChange(bj(b,d,e))}else c.trackChildChange(bh(b,d))})),b.withIndex(this.index_)}updatePriority(a,b){return a.isEmpty()?a9.EMPTY_NODE:a.updatePriority(b)}filtersNodes(){return!1}getIndexedFilter(){return this}getIndex(){return this.index_}}class bl{constructor(a){this.indexedFilter_=new bk(a.getIndex()),this.index_=a.getIndex(),this.startPost_=bl.getStartPost_(a),this.endPost_=bl.getEndPost_(a),this.startIsInclusive_=!a.startAfterSet_,this.endIsInclusive_=!a.endBeforeSet_}getStartPost(){return this.startPost_}getEndPost(){return this.endPost_}matches(a){let b=this.startIsInclusive_?0>=this.index_.compare(this.getStartPost(),a):0>this.index_.compare(this.getStartPost(),a),c=this.endIsInclusive_?0>=this.index_.compare(a,this.getEndPost()):0>this.index_.compare(a,this.getEndPost());return b&&c}updateChild(a,b,c,d,e,f){return this.matches(new aR(b,c))||(c=a9.EMPTY_NODE),this.indexedFilter_.updateChild(a,b,c,d,e,f)}updateFullNode(a,b,c){b.isLeafNode()&&(b=a9.EMPTY_NODE);let d=b.withIndex(this.index_);d=d.updatePriority(a9.EMPTY_NODE);let e=this;return b.forEachChild(a3,(a,b)=>{e.matches(new aR(a,b))||(d=d.updateImmediateChild(a,a9.EMPTY_NODE))}),this.indexedFilter_.updateFullNode(a,d,c)}updatePriority(a,b){return a}filtersNodes(){return!0}getIndexedFilter(){return this.indexedFilter_}getIndex(){return this.index_}static getStartPost_(a){if(!a.hasStart())return a.getIndex().minPost();{let b=a.getIndexStartName();return a.getIndex().makePost(a.getIndexStartValue(),b)}}static getEndPost_(a){if(!a.hasEnd())return a.getIndex().maxPost();{let b=a.getIndexEndName();return a.getIndex().makePost(a.getIndexEndValue(),b)}}}class bm{constructor(a){this.withinDirectionalStart=a=>this.reverse_?this.withinEndPost(a):this.withinStartPost(a),this.withinDirectionalEnd=a=>this.reverse_?this.withinStartPost(a):this.withinEndPost(a),this.withinStartPost=a=>{let b=this.index_.compare(this.rangedFilter_.getStartPost(),a);return this.startIsInclusive_?b<=0:b<0},this.withinEndPost=a=>{let b=this.index_.compare(a,this.rangedFilter_.getEndPost());return this.endIsInclusive_?b<=0:b<0},this.rangedFilter_=new bl(a),this.index_=a.getIndex(),this.limit_=a.getLimit(),this.reverse_=!a.isViewFromLeft(),this.startIsInclusive_=!a.startAfterSet_,this.endIsInclusive_=!a.endBeforeSet_}updateChild(a,b,c,d,e,f){return(this.rangedFilter_.matches(new aR(b,c))||(c=a9.EMPTY_NODE),a.getImmediateChild(b).equals(c))?a:a.numChildren()<this.limit_?this.rangedFilter_.getIndexedFilter().updateChild(a,b,c,d,e,f):this.fullLimitUpdateChild_(a,b,c,e,f)}updateFullNode(a,b,c){let d;if(b.isLeafNode()||b.isEmpty())d=a9.EMPTY_NODE.withIndex(this.index_);else if(2*this.limit_<b.numChildren()&&b.isIndexed(this.index_)){let a;d=a9.EMPTY_NODE.withIndex(this.index_),a=this.reverse_?b.getReverseIteratorFrom(this.rangedFilter_.getEndPost(),this.index_):b.getIteratorFrom(this.rangedFilter_.getStartPost(),this.index_);let c=0;for(;a.hasNext()&&c<this.limit_;){let b=a.getNext();if(this.withinDirectionalStart(b))if(this.withinDirectionalEnd(b))d=d.updateImmediateChild(b.name,b.node),c++;else break}}else{let a;d=(d=b.withIndex(this.index_)).updatePriority(a9.EMPTY_NODE),a=this.reverse_?d.getReverseIterator(this.index_):d.getIterator(this.index_);let c=0;for(;a.hasNext();){let b=a.getNext();c<this.limit_&&this.withinDirectionalStart(b)&&this.withinDirectionalEnd(b)?c++:d=d.updateImmediateChild(b.name,a9.EMPTY_NODE)}}return this.rangedFilter_.getIndexedFilter().updateFullNode(a,d,c)}updatePriority(a,b){return a}filtersNodes(){return!0}getIndexedFilter(){return this.rangedFilter_.getIndexedFilter()}getIndex(){return this.index_}fullLimitUpdateChild_(a,b,c,d,e){let f;if(this.reverse_){let a=this.index_.getCompare();f=(b,c)=>a(c,b)}else f=this.index_.getCompare();(0,p.vA)(a.numChildren()===this.limit_,"");let g=new aR(b,c),h=this.reverse_?a.getFirstChild(this.index_):a.getLastChild(this.index_),i=this.rangedFilter_.matches(g);if(a.hasChild(b)){let j=a.getImmediateChild(b),k=d.getChildAfterChild(this.index_,h,this.reverse_);for(;null!=k&&(k.name===b||a.hasChild(k.name));)k=d.getChildAfterChild(this.index_,k,this.reverse_);let l=null==k?1:f(k,g);if(i&&!c.isEmpty()&&l>=0)return null!=e&&e.trackChildChange(bj(b,c,j)),a.updateImmediateChild(b,c);{null!=e&&e.trackChildChange(bi(b,j));let c=a.updateImmediateChild(b,a9.EMPTY_NODE);return null!=k&&this.rangedFilter_.matches(k)?(null!=e&&e.trackChildChange(bh(k.name,k.node)),c.updateImmediateChild(k.name,k.node)):c}}return c.isEmpty()?a:i?f(h,g)>=0?(null!=e&&(e.trackChildChange(bi(h.name,h.node)),e.trackChildChange(bh(b,c))),a.updateImmediateChild(b,c).updateImmediateChild(h.name,a9.EMPTY_NODE)):a:a}}class bn{constructor(){this.limitSet_=!1,this.startSet_=!1,this.startNameSet_=!1,this.startAfterSet_=!1,this.endSet_=!1,this.endNameSet_=!1,this.endBeforeSet_=!1,this.limit_=0,this.viewFrom_="",this.indexStartValue_=null,this.indexStartName_="",this.indexEndValue_=null,this.indexEndName_="",this.index_=a3}hasStart(){return this.startSet_}isViewFromLeft(){return""===this.viewFrom_?this.startSet_:"l"===this.viewFrom_}getIndexStartValue(){return(0,p.vA)(this.startSet_,"Only valid if start has been set"),this.indexStartValue_}getIndexStartName(){return((0,p.vA)(this.startSet_,"Only valid if start has been set"),this.startNameSet_)?this.indexStartName_:Q}hasEnd(){return this.endSet_}getIndexEndValue(){return(0,p.vA)(this.endSet_,"Only valid if end has been set"),this.indexEndValue_}getIndexEndName(){return((0,p.vA)(this.endSet_,"Only valid if end has been set"),this.endNameSet_)?this.indexEndName_:R}hasLimit(){return this.limitSet_}hasAnchoredLimit(){return this.limitSet_&&""!==this.viewFrom_}getLimit(){return(0,p.vA)(this.limitSet_,"Only valid if limit has been set"),this.limit_}getIndex(){return this.index_}loadsAllData(){return!(this.startSet_||this.endSet_||this.limitSet_)}isDefault(){return this.loadsAllData()&&this.index_===a3}copy(){let a=new bn;return a.limitSet_=this.limitSet_,a.limit_=this.limit_,a.startSet_=this.startSet_,a.startAfterSet_=this.startAfterSet_,a.indexStartValue_=this.indexStartValue_,a.startNameSet_=this.startNameSet_,a.indexStartName_=this.indexStartName_,a.endSet_=this.endSet_,a.endBeforeSet_=this.endBeforeSet_,a.indexEndValue_=this.indexEndValue_,a.endNameSet_=this.endNameSet_,a.indexEndName_=this.indexEndName_,a.index_=this.index_,a.viewFrom_=this.viewFrom_,a}}function bo(a){let b,c={};if(a.isDefault())return c;if(a.index_===a3?b="$priority":a.index_===bf?b="$value":a.index_===aU?b="$key":((0,p.vA)(a.index_ instanceof bd,"Unrecognized index type!"),b=a.index_.toString()),c.orderBy=(0,p.As)(b),a.startSet_){let b=a.startAfterSet_?"startAfter":"startAt";c[b]=(0,p.As)(a.indexStartValue_),a.startNameSet_&&(c[b]+=","+(0,p.As)(a.indexStartName_))}if(a.endSet_){let b=a.endBeforeSet_?"endBefore":"endAt";c[b]=(0,p.As)(a.indexEndValue_),a.endNameSet_&&(c[b]+=","+(0,p.As)(a.indexEndName_))}return a.limitSet_&&(a.isViewFromLeft()?c.limitToFirst=a.limit_:c.limitToLast=a.limit_),c}function bp(a){let b={};if(a.startSet_&&(b.sp=a.indexStartValue_,a.startNameSet_&&(b.sn=a.indexStartName_),b.sin=!a.startAfterSet_),a.endSet_&&(b.ep=a.indexEndValue_,a.endNameSet_&&(b.en=a.indexEndName_),b.ein=!a.endBeforeSet_),a.limitSet_){b.l=a.limit_;let c=a.viewFrom_;""===c&&(c=a.isViewFromLeft()?"l":"r"),b.vf=c}return a.index_!==a3&&(b.i=a.index_.toString()),b}class bq extends av{reportStats(a){throw Error("Method not implemented.")}static getListenId_(a,b){return void 0!==b?"tag$"+b:((0,p.vA)(a._queryParams.isDefault(),"should have a tag if it's not a default query."),a._path.toString())}constructor(a,b,c,d){super(),this.repoInfo_=a,this.onDataUpdate_=b,this.authTokenProvider_=c,this.appCheckTokenProvider_=d,this.log_=J("p:rest:"),this.listens_={}}listen(a,b,c,d){let e=a._path.toString();this.log_("Listen called for "+e+" "+a._queryIdentifier);let f=bq.getListenId_(a,c),g={};this.listens_[f]=g;let h=bo(a._queryParams);this.restRequest_(e+".json",h,(a,b)=>{let h=b;(404===a&&(h=null,a=null),null===a&&this.onDataUpdate_(e,h,!1,c),(0,p.yw)(this.listens_,f)===g)&&d(a?401===a?"permission_denied":"rest_error:"+a:"ok",null)})}unlisten(a,b){let c=bq.getListenId_(a,b);delete this.listens_[c]}get(a){let b=bo(a._queryParams),c=a._path.toString(),d=new p.cY;return this.restRequest_(c+".json",b,(a,b)=>{let e=b;404===a&&(e=null,a=null),null===a?(this.onDataUpdate_(c,e,!1,null),d.resolve(e)):d.reject(Error(e))}),d.promise}refreshAuthToken(a){}restRequest_(a,b={},c){return b.format="export",Promise.all([this.authTokenProvider_.getToken(!1),this.appCheckTokenProvider_.getToken(!1)]).then(([d,e])=>{d&&d.accessToken&&(b.auth=d.accessToken),e&&e.token&&(b.ac=e.token);let f=(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host+a+"?ns="+this.repoInfo_.namespace+(0,p.Am)(b);this.log_("Sending REST request for "+f);let g=new XMLHttpRequest;g.onreadystatechange=()=>{if(c&&4===g.readyState){this.log_("REST Response for "+f+" received. status:",g.status,"response:",g.responseText);let a=null;if(g.status>=200&&g.status<300){try{a=(0,p.$L)(g.responseText)}catch(a){M("Failed to parse JSON response for "+f+": "+g.responseText)}c(null,a)}else 401!==g.status&&404!==g.status&&M("Got unsuccessful REST response for "+f+" Status: "+g.status),c(g.status);c=null}},g.open("GET",f,!0),g.send()})}}class br{constructor(){this.rootNode_=a9.EMPTY_NODE}getNode(a){return this.rootNode_.getChild(a)}updateSnapshot(a,b){this.rootNode_=this.rootNode_.updateChild(a,b)}}function bs(){return{value:null,children:new Map}}function bt(a,b,c){var d,e;null!==a.value?c(b,a.value):(d=a,e=(a,d)=>{bt(d,new ay(b.toString()+"/"+a),c)},d.children.forEach((a,b)=>{e(b,a)}))}class bu{constructor(a){this.collection_=a,this.last_=null}get(){let a=this.collection_.get(),b={...a};return this.last_&&X(this.last_,(a,c)=>{b[a]=b[a]-c}),this.last_=a,b}}class bv{constructor(a,b){this.server_=b,this.statsToReport_={},this.statsListener_=new bu(a);let c=1e4+2e4*Math.random();aa(this.reportStats_.bind(this),Math.floor(c))}reportStats_(){let a=this.statsListener_.get(),b={},c=!1;X(a,(a,d)=>{d>0&&(0,p.gR)(this.statsToReport_,a)&&(b[a]=d,c=!0)}),c&&this.server_.reportStats(b),aa(this.reportStats_.bind(this),Math.floor(2*Math.random()*3e5))}}function bw(){return{fromUser:!0,fromServer:!1,queryId:null,tagged:!1}}function bx(){return{fromUser:!1,fromServer:!0,queryId:null,tagged:!1}}function by(a){return{fromUser:!1,fromServer:!0,queryId:a,tagged:!0}}!function(a){a[a.OVERWRITE=0]="OVERWRITE",a[a.MERGE=1]="MERGE",a[a.ACK_USER_WRITE=2]="ACK_USER_WRITE",a[a.LISTEN_COMPLETE=3]="LISTEN_COMPLETE"}(n||(n={}));class bz{constructor(a,b,c){this.path=a,this.affectedTree=b,this.revert=c,this.type=n.ACK_USER_WRITE,this.source=bw()}operationForChild(a){if(!aH(this.path))return(0,p.vA)(aA(this.path)===a,"operationForChild called for unrelated child."),new bz(aC(this.path),this.affectedTree,this.revert);if(null!=this.affectedTree.value)return(0,p.vA)(this.affectedTree.children.isEmpty(),"affectedTree should not have overlapping affected paths."),this;{let b=this.affectedTree.subtree(new ay(a));return new bz(az(),b,this.revert)}}}class bA{constructor(a,b){this.source=a,this.path=b,this.type=n.LISTEN_COMPLETE}operationForChild(a){return aH(this.path)?new bA(this.source,az()):new bA(this.source,aC(this.path))}}class bB{constructor(a,b,c){this.source=a,this.path=b,this.snap=c,this.type=n.OVERWRITE}operationForChild(a){return aH(this.path)?new bB(this.source,az(),this.snap.getImmediateChild(a)):new bB(this.source,aC(this.path),this.snap)}}class bC{constructor(a,b,c){this.source=a,this.path=b,this.children=c,this.type=n.MERGE}operationForChild(a){if(!aH(this.path))return(0,p.vA)(aA(this.path)===a,"Can't get a merge for a child not on the path of the operation"),new bC(this.source,aC(this.path),this.children);{let b=this.children.subtree(new ay(a));return b.isEmpty()?null:b.value?new bB(this.source,az(),b.value):new bC(this.source,az(),b)}}toString(){return"Operation("+this.path+": "+this.source.toString()+" merge: "+this.children.toString()+")"}}class bD{constructor(a,b,c){this.node_=a,this.fullyInitialized_=b,this.filtered_=c}isFullyInitialized(){return this.fullyInitialized_}isFiltered(){return this.filtered_}isCompleteForPath(a){if(aH(a))return this.isFullyInitialized()&&!this.filtered_;let b=aA(a);return this.isCompleteForChild(b)}isCompleteForChild(a){return this.isFullyInitialized()&&!this.filtered_||this.node_.hasChild(a)}getNode(){return this.node_}}class bE{constructor(a){this.query_=a,this.index_=this.query_._queryParams.getIndex()}}function bF(a,b,c,d,e,f){let g=d.filter(a=>a.type===c);g.sort((b,c)=>(function(a,b,c){if(null==b.childName||null==c.childName)throw(0,p.Hk)("Should only compare child_ events.");let d=new aR(b.childName,b.snapshotNode),e=new aR(c.childName,c.snapshotNode);return a.index_.compare(d,e)})(a,b,c)),g.forEach(c=>{var d,g,h;let i=(d=a,g=c,h=f,"value"===g.type||"child_removed"===g.type||(g.prevName=h.getPredecessorChildName(g.childName,g.snapshotNode,d.index_)),g);e.forEach(d=>{d.respondsTo(c.type)&&b.push(d.createEvent(i,a.query_))})})}function bG(a,b){return{eventCache:a,serverCache:b}}function bH(a,b,c,d){return bG(new bD(b,c,d),a.serverCache)}function bI(a,b,c,d){return bG(a.eventCache,new bD(b,c,d))}function bJ(a){return a.eventCache.isFullyInitialized()?a.eventCache.getNode():null}function bK(a){return a.serverCache.isFullyInitialized()?a.serverCache.getNode():null}class bL{static fromObject(a){let b=new bL(null);return X(a,(a,c)=>{b=b.set(new ay(a),c)}),b}constructor(a,b=(!k&&(k=new aY(T)),k)){this.value=a,this.children=b}isEmpty(){return null===this.value&&this.children.isEmpty()}findRootMostMatchingPathAndValue(a,b){if(null!=this.value&&b(this.value))return{path:az(),value:this.value};{if(aH(a))return null;let c=aA(a),d=this.children.get(c);if(null===d)return null;{let e=d.findRootMostMatchingPathAndValue(aC(a),b);return null!=e?{path:aG(new ay(c),e.path),value:e.value}:null}}}findRootMostValueAndPath(a){return this.findRootMostMatchingPathAndValue(a,()=>!0)}subtree(a){if(aH(a))return this;{let b=aA(a),c=this.children.get(b);return null!==c?c.subtree(aC(a)):new bL(null)}}set(a,b){if(aH(a))return new bL(b,this.children);{let c=aA(a),d=(this.children.get(c)||new bL(null)).set(aC(a),b),e=this.children.insert(c,d);return new bL(this.value,e)}}remove(a){if(aH(a))if(this.children.isEmpty())return new bL(null);else return new bL(null,this.children);{let b=aA(a),c=this.children.get(b);if(!c)return this;{let d,e=c.remove(aC(a));return(d=e.isEmpty()?this.children.remove(b):this.children.insert(b,e),null===this.value&&d.isEmpty())?new bL(null):new bL(this.value,d)}}}get(a){if(aH(a))return this.value;{let b=aA(a),c=this.children.get(b);return c?c.get(aC(a)):null}}setTree(a,b){if(aH(a))return b;{let c,d=aA(a),e=(this.children.get(d)||new bL(null)).setTree(aC(a),b);return c=e.isEmpty()?this.children.remove(d):this.children.insert(d,e),new bL(this.value,c)}}fold(a){return this.fold_(az(),a)}fold_(a,b){let c={};return this.children.inorderTraversal((d,e)=>{c[d]=e.fold_(aG(a,d),b)}),b(a,this.value,c)}findOnPath(a,b){return this.findOnPath_(a,az(),b)}findOnPath_(a,b,c){let d=!!this.value&&c(b,this.value);if(d)return d;{if(aH(a))return null;let d=aA(a),e=this.children.get(d);return e?e.findOnPath_(aC(a),aG(b,d),c):null}}foreachOnPath(a,b){return this.foreachOnPath_(a,az(),b)}foreachOnPath_(a,b,c){if(aH(a))return this;{this.value&&c(b,this.value);let d=aA(a),e=this.children.get(d);return e?e.foreachOnPath_(aC(a),aG(b,d),c):new bL(null)}}foreach(a){this.foreach_(az(),a)}foreach_(a,b){this.children.inorderTraversal((c,d)=>{d.foreach_(aG(a,c),b)}),this.value&&b(a,this.value)}foreachChild(a){this.children.inorderTraversal((b,c)=>{c.value&&a(b,c.value)})}}class bM{constructor(a){this.writeTree_=a}static empty(){return new bM(new bL(null))}}function bN(a,b,c){if(aH(b))return new bM(new bL(c));{let d=a.writeTree_.findRootMostValueAndPath(b);if(null!=d){let e=d.path,f=d.value,g=aI(e,b);return f=f.updateChild(g,c),new bM(a.writeTree_.set(e,f))}{let d=new bL(c);return new bM(a.writeTree_.setTree(b,d))}}}function bO(a,b,c){let d=a;return X(c,(a,c)=>{d=bN(d,aG(b,a),c)}),d}function bP(a,b){return aH(b)?bM.empty():new bM(a.writeTree_.setTree(b,new bL(null)))}function bQ(a,b){return null!=bR(a,b)}function bR(a,b){let c=a.writeTree_.findRootMostValueAndPath(b);return null!=c?a.writeTree_.get(c.path).getChild(aI(c.path,b)):null}function bS(a){let b=[],c=a.writeTree_.value;return null!=c?c.isLeafNode()||c.forEachChild(a3,(a,c)=>{b.push(new aR(a,c))}):a.writeTree_.children.inorderTraversal((a,c)=>{null!=c.value&&b.push(new aR(a,c.value))}),b}function bT(a,b){if(aH(b))return a;{let c=bR(a,b);return new bM(null!=c?new bL(c):a.writeTree_.subtree(b))}}function bU(a){return a.writeTree_.isEmpty()}function bV(a,b){return function a(b,c,d){if(null!=c.value)return d.updateChild(b,c.value);{let e=null;return c.children.inorderTraversal((c,f)=>{".priority"===c?((0,p.vA)(null!==f.value,"Priority writes must always be leaf nodes"),e=f.value):d=a(aG(b,c),f,d)}),d.getChild(b).isEmpty()||null===e||(d=d.updateChild(aG(b,".priority"),e)),d}}(az(),a.writeTree_,b)}function bW(a){return a.visible}function bX(a,b,c){let d=bM.empty();for(let e=0;e<a.length;++e){let f=a[e];if(b(f)){let a,b=f.path;if(f.snap)aL(c,b)?d=bN(d,a=aI(c,b),f.snap):aL(b,c)&&(a=aI(b,c),d=bN(d,az(),f.snap.getChild(a)));else if(f.children){if(aL(c,b))d=bO(d,a=aI(c,b),f.children);else if(aL(b,c))if(aH(a=aI(b,c)))d=bO(d,az(),f.children);else{let b=(0,p.yw)(f.children,aA(a));if(b){let c=b.getChild(aC(a));d=bN(d,az(),c)}}}else throw(0,p.Hk)("WriteRecord should have .snap or .children")}}return d}function bY(a,b,c,d,e){if(d||e){let f=bT(a.visibleWrites,b);return!e&&bU(f)?c:e||null!=c||bQ(f,az())?bV(bX(a.allWrites,function(a){return(a.visible||e)&&(!d||!~d.indexOf(a.writeId))&&(aL(a.path,b)||aL(b,a.path))},b),c||a9.EMPTY_NODE):null}{let d=bR(a.visibleWrites,b);if(null!=d)return d;{let d=bT(a.visibleWrites,b);return bU(d)?c:null!=c||bQ(d,az())?bV(d,c||a9.EMPTY_NODE):null}}}function bZ(a,b,c,d){return bY(a.writeTree,a.treePath,b,c,d)}function b$(a,b){return function(a,b,c){let d=a9.EMPTY_NODE,e=bR(a.visibleWrites,b);if(e)return e.isLeafNode()||e.forEachChild(a3,(a,b)=>{d=d.updateImmediateChild(a,b)}),d;if(!c)return bS(bT(a.visibleWrites,b)).forEach(a=>{d=d.updateImmediateChild(a.name,a.node)}),d;{let e=bT(a.visibleWrites,b);return c.forEachChild(a3,(a,b)=>{let c=bV(bT(e,new ay(a)),b);d=d.updateImmediateChild(a,c)}),bS(e).forEach(a=>{d=d.updateImmediateChild(a.name,a.node)}),d}}(a.writeTree,a.treePath,b)}function b_(a,b,c,d){return function(a,b,c,d,e){(0,p.vA)(d||e,"Either existingEventSnap or existingServerSnap must exist");let f=aG(b,c);if(bQ(a.visibleWrites,f))return null;{let b=bT(a.visibleWrites,f);return bU(b)?e.getChild(c):bV(b,e.getChild(c))}}(a.writeTree,a.treePath,b,c,d)}function b0(a,b){var c,d;return c=a.writeTree,d=aG(a.treePath,b),bR(c.visibleWrites,d)}function b1(a,b,c){return function(a,b,c,d){let e=aG(b,c),f=bR(a.visibleWrites,e);return null!=f?f:d.isCompleteForChild(c)?bV(bT(a.visibleWrites,e),d.getNode().getImmediateChild(c)):null}(a.writeTree,a.treePath,b,c)}function b2(a,b){return b3(aG(a.treePath,b),a.writeTree)}function b3(a,b){return{treePath:a,writeTree:b}}class b4{constructor(){this.changeMap=new Map}trackChildChange(a){let b=a.type,c=a.childName;(0,p.vA)("child_added"===b||"child_changed"===b||"child_removed"===b,"Only child changes supported for tracking"),(0,p.vA)(".priority"!==c,"Only non-priority child changes can be tracked.");let d=this.changeMap.get(c);if(d){let e=d.type;if("child_added"===b&&"child_removed"===e)this.changeMap.set(c,bj(c,a.snapshotNode,d.snapshotNode));else if("child_removed"===b&&"child_added"===e)this.changeMap.delete(c);else if("child_removed"===b&&"child_changed"===e)this.changeMap.set(c,bi(c,d.oldSnap));else if("child_changed"===b&&"child_added"===e)this.changeMap.set(c,bh(c,a.snapshotNode));else if("child_changed"===b&&"child_changed"===e)this.changeMap.set(c,bj(c,a.snapshotNode,d.oldSnap));else throw(0,p.Hk)("Illegal combination of changes: "+a+" occurred after "+d)}else this.changeMap.set(c,a)}getChanges(){return Array.from(this.changeMap.values())}}class b5{getCompleteChild(a){return null}getChildAfterChild(a,b,c){return null}}let b6=new b5;class b7{constructor(a,b,c=null){this.writes_=a,this.viewCache_=b,this.optCompleteServerCache_=c}getCompleteChild(a){let b=this.viewCache_.eventCache;if(b.isCompleteForChild(a))return b.getNode().getImmediateChild(a);{let b=null!=this.optCompleteServerCache_?new bD(this.optCompleteServerCache_,!0,!1):this.viewCache_.serverCache;return b1(this.writes_,a,b)}}getChildAfterChild(a,b,c){var d;let e=null!=this.optCompleteServerCache_?this.optCompleteServerCache_:bK(this.viewCache_),f=(d=this.writes_,function(a,b,c,d,e,f,g){let h,i=bT(a.visibleWrites,b),j=bR(i,az());if(null!=j)h=j;else{if(null==c)return[];h=bV(i,c)}if((h=h.withIndex(g)).isEmpty()||h.isLeafNode())return[];{let a=[],b=g.getCompare(),c=f?h.getReverseIteratorFrom(d,g):h.getIteratorFrom(d,g),e=c.getNext();for(;e&&a.length<1;)0!==b(e,d)&&a.push(e),e=c.getNext();return a}}(d.writeTree,d.treePath,e,b,1,c,a));return 0===f.length?null:f[0]}}function b8(a,b,c,d,e,f){let g=b.eventCache;if(null!=b0(d,c))return b;{let h,i;if(aH(c))if((0,p.vA)(b.serverCache.isFullyInitialized(),"If change path is empty, we must have complete server data"),b.serverCache.isFiltered()){let c=bK(b),e=b$(d,c instanceof a9?c:a9.EMPTY_NODE);h=a.filter.updateFullNode(b.eventCache.getNode(),e,f)}else{let c=bZ(d,bK(b));h=a.filter.updateFullNode(b.eventCache.getNode(),c,f)}else{let j=aA(c);if(".priority"===j){(0,p.vA)(1===aB(c),"Can't have a priority with additional path components");let e=g.getNode(),f=b_(d,c,e,i=b.serverCache.getNode());h=null!=f?a.filter.updatePriority(e,f):g.getNode()}else{let k,l=aC(c);if(g.isCompleteForChild(j)){i=b.serverCache.getNode();let a=b_(d,c,g.getNode(),i);k=null!=a?g.getNode().getImmediateChild(j).updateChild(l,a):g.getNode().getImmediateChild(j)}else k=b1(d,j,b.serverCache);h=null!=k?a.filter.updateChild(g.getNode(),j,k,l,e,f):g.getNode()}}return bH(b,h,g.isFullyInitialized()||aH(c),a.filter.filtersNodes())}}function b9(a,b,c,d,e,f,g,h){let i,j=b.serverCache,k=g?a.filter:a.filter.getIndexedFilter();if(aH(c))i=k.updateFullNode(j.getNode(),d,null);else if(k.filtersNodes()&&!j.isFiltered()){let a=j.getNode().updateChild(c,d);i=k.updateFullNode(j.getNode(),a,null)}else{let a=aA(c);if(!j.isCompleteForPath(c)&&aB(c)>1)return b;let e=aC(c),f=j.getNode().getImmediateChild(a).updateChild(e,d);i=".priority"===a?k.updatePriority(j.getNode(),f):k.updateChild(j.getNode(),a,f,e,b6,null)}let l=bI(b,i,j.isFullyInitialized()||aH(c),k.filtersNodes()),m=new b7(e,l,f);return b8(a,l,c,e,m,h)}function ca(a,b,c,d,e,f,g){let h,i,j=b.eventCache,k=new b7(e,b,f);if(aH(c))i=a.filter.updateFullNode(b.eventCache.getNode(),d,g),h=bH(b,i,!0,a.filter.filtersNodes());else{let e=aA(c);if(".priority"===e)i=a.filter.updatePriority(b.eventCache.getNode(),d),h=bH(b,i,j.isFullyInitialized(),j.isFiltered());else{let f,i=aC(c),l=j.getNode().getImmediateChild(e);if(aH(i))f=d;else{let a=k.getCompleteChild(e);f=null!=a?".priority"===aD(i)&&a.getChild(aF(i)).isEmpty()?a:a.updateChild(i,d):a9.EMPTY_NODE}h=l.equals(f)?b:bH(b,a.filter.updateChild(j.getNode(),e,f,i,k,g),j.isFullyInitialized(),a.filter.filtersNodes())}}return h}function cb(a,b){return a.eventCache.isCompleteForChild(b)}function cc(a,b,c){return c.foreach((a,c)=>{b=b.updateChild(a,c)}),b}function cd(a,b,c,d,e,f,g,h){let i;if(b.serverCache.getNode().isEmpty()&&!b.serverCache.isFullyInitialized())return b;let j=b;i=aH(c)?d:new bL(null).setTree(c,d);let k=b.serverCache.getNode();return i.children.inorderTraversal((c,d)=>{if(k.hasChild(c)){let i=cc(a,b.serverCache.getNode().getImmediateChild(c),d);j=b9(a,j,new ay(c),i,e,f,g,h)}}),i.children.inorderTraversal((c,d)=>{let i=!b.serverCache.isCompleteForChild(c)&&null===d.value;if(!k.hasChild(c)&&!i){let i=cc(a,b.serverCache.getNode().getImmediateChild(c),d);j=b9(a,j,new ay(c),i,e,f,g,h)}}),j}class ce{constructor(a,b){this.query_=a,this.eventRegistrations_=[];let c=this.query_._queryParams,d=new bk(c.getIndex()),e=function(a){return a.loadsAllData()?new bk(a.getIndex()):a.hasLimit()?new bm(a):new bl(a)}(c);this.processor_={filter:e};let f=b.serverCache,g=b.eventCache,h=d.updateFullNode(a9.EMPTY_NODE,f.getNode(),null),i=e.updateFullNode(a9.EMPTY_NODE,g.getNode(),null),j=new bD(h,f.isFullyInitialized(),d.filtersNodes()),k=new bD(i,g.isFullyInitialized(),e.filtersNodes());this.viewCache_=bG(k,j),this.eventGenerator_=new bE(this.query_)}get query(){return this.query_}}function cf(a){return 0===a.eventRegistrations_.length}function cg(a,b,c){let d=[];if(c){(0,p.vA)(null==b,"A cancel should cancel all event registrations.");let e=a.query._path;a.eventRegistrations_.forEach(a=>{let b=a.createCancelEvent(c,e);b&&d.push(b)})}if(b){let c=[];for(let d=0;d<a.eventRegistrations_.length;++d){let e=a.eventRegistrations_[d];if(e.matches(b)){if(b.hasAnyCallback()){c=c.concat(a.eventRegistrations_.slice(d+1));break}}else c.push(e)}a.eventRegistrations_=c}else a.eventRegistrations_=[];return d}function ch(a,b,c,d){var e,f;b.type===n.MERGE&&null!==b.source.queryId&&((0,p.vA)(bK(a.viewCache_),"We should always have a full cache before handling merges"),(0,p.vA)(bJ(a.viewCache_),"Missing event cache, even though we have a server cache"));let g=a.viewCache_,h=function(a,b,c,d,e){let f,g,h=new b4;if(c.type===n.OVERWRITE)c.source.fromUser?f=ca(a,b,c.path,c.snap,d,e,h):((0,p.vA)(c.source.fromServer,"Unknown source."),g=c.source.tagged||b.serverCache.isFiltered()&&!aH(c.path),f=b9(a,b,c.path,c.snap,d,e,g,h));else if(c.type===n.MERGE){var i,j,k,l,m,o,q;let n;c.source.fromUser?(i=a,j=b,k=c.path,l=c.children,m=d,o=e,q=h,n=j,l.foreach((a,b)=>{let c=aG(k,a);cb(j,aA(c))&&(n=ca(i,n,c,b,m,o,q))}),l.foreach((a,b)=>{let c=aG(k,a);cb(j,aA(c))||(n=ca(i,n,c,b,m,o,q))}),f=n):((0,p.vA)(c.source.fromServer,"Unknown source."),g=c.source.tagged||b.serverCache.isFiltered(),f=cd(a,b,c.path,c.children,d,e,g,h))}else if(c.type===n.ACK_USER_WRITE)f=c.revert?function(a,b,c,d,e,f){let g;if(null!=b0(d,c))return b;{let h,i=new b7(d,b,e),j=b.eventCache.getNode();if(aH(c)||".priority"===aA(c)){let c;if(b.serverCache.isFullyInitialized())c=bZ(d,bK(b));else{let a=b.serverCache.getNode();(0,p.vA)(a instanceof a9,"serverChildren would be complete if leaf node"),c=b$(d,a)}h=a.filter.updateFullNode(j,c,f)}else{let e=aA(c),k=b1(d,e,b.serverCache);null==k&&b.serverCache.isCompleteForChild(e)&&(k=j.getImmediateChild(e)),(h=null!=k?a.filter.updateChild(j,e,k,aC(c),i,f):b.eventCache.getNode().hasChild(e)?a.filter.updateChild(j,e,a9.EMPTY_NODE,aC(c),i,f):j).isEmpty()&&b.serverCache.isFullyInitialized()&&(g=bZ(d,bK(b))).isLeafNode()&&(h=a.filter.updateFullNode(h,g,f))}return g=b.serverCache.isFullyInitialized()||null!=b0(d,az()),bH(b,h,g,a.filter.filtersNodes())}}(a,b,c.path,d,e,h):function(a,b,c,d,e,f,g){if(null!=b0(e,c))return b;let h=b.serverCache.isFiltered(),i=b.serverCache;if(null!=d.value)if(aH(c)&&i.isFullyInitialized()||i.isCompleteForPath(c))return b9(a,b,c,i.getNode().getChild(c),e,f,h,g);else{if(!aH(c))return b;let d=new bL(null);return i.getNode().forEachChild(aU,(a,b)=>{d=d.set(new ay(a),b)}),cd(a,b,c,d,e,f,h,g)}{let j=new bL(null);return d.foreach((a,b)=>{let d=aG(c,a);i.isCompleteForPath(d)&&(j=j.set(a,i.getNode().getChild(d)))}),cd(a,b,c,j,e,f,h,g)}}(a,b,c.path,c.affectedTree,d,e,h);else if(c.type===n.LISTEN_COMPLETE)f=function(a,b,c,d,e){let f=b.serverCache;return b8(a,bI(b,f.getNode(),f.isFullyInitialized()||aH(c),f.isFiltered()),c,d,b6,e)}(a,b,c.path,d,h);else throw(0,p.Hk)("Unknown operation type: "+c.type);let r=h.getChanges();return function(a,b,c){let d=b.eventCache;if(d.isFullyInitialized()){let e=d.getNode().isLeafNode()||d.getNode().isEmpty(),f=bJ(a);!(c.length>0)&&a.eventCache.isFullyInitialized()&&(!e||d.getNode().equals(f))&&d.getNode().getPriority().equals(f.getPriority())||c.push(bg(bJ(b)))}}(b,f,r),{viewCache:f,changes:r}}(a.processor_,g,b,c,d);return e=a.processor_,f=h.viewCache,(0,p.vA)(f.eventCache.getNode().isIndexed(e.filter.getIndex()),"Event snap not indexed"),(0,p.vA)(f.serverCache.getNode().isIndexed(e.filter.getIndex()),"Server snap not indexed"),(0,p.vA)(h.viewCache.serverCache.isFullyInitialized()||!g.serverCache.isFullyInitialized(),"Once a server snap is complete, it should never go back"),a.viewCache_=h.viewCache,ci(a,h.changes,h.viewCache.eventCache.getNode(),null)}function ci(a,b,c,d){let e=d?[d]:a.eventRegistrations_;var f=a.eventGenerator_;let g=[],h=[];return b.forEach(a=>{var b;"child_changed"===a.type&&f.index_.indexedValueChanged(a.oldSnap,a.snapshotNode)&&h.push((b=a.childName,{type:"child_moved",snapshotNode:a.snapshotNode,childName:b}))}),bF(f,g,"child_removed",b,e,c),bF(f,g,"child_added",b,e,c),bF(f,g,"child_moved",h,e,c),bF(f,g,"child_changed",b,e,c),bF(f,g,"value",b,e,c),g}class cj{constructor(){this.views=new Map}}function ck(a,b,c,d){let e=b.source.queryId;if(null!==e){let f=a.views.get(e);return(0,p.vA)(null!=f,"SyncTree gave us an op for an invalid query."),ch(f,b,c,d)}{let e=[];for(let f of a.views.values())e=e.concat(ch(f,b,c,d));return e}}function cl(a,b,c,d,e){let f=b._queryIdentifier,g=a.views.get(f);if(!g){let a=bZ(c,e?d:null),f=!1;return a?f=!0:(a=d instanceof a9?b$(c,d):a9.EMPTY_NODE,f=!1),new ce(b,bG(new bD(a,f,!1),new bD(d,e,!1)))}return g}function cm(a){let b=[];for(let c of a.views.values())c.query._queryParams.loadsAllData()||b.push(c);return b}function cn(a,b){let c=null;for(let d of a.views.values())c=c||function(a,b){let c=bK(a.viewCache_);return c&&(a.query._queryParams.loadsAllData()||!aH(b)&&!c.getImmediateChild(aA(b)).isEmpty())?c.getChild(b):null}(d,b);return c}function co(a,b){if(b._queryParams.loadsAllData())return cq(a);{let c=b._queryIdentifier;return a.views.get(c)}}function cp(a){return null!=cq(a)}function cq(a){for(let b of a.views.values())if(b.query._queryParams.loadsAllData())return b;return null}let cr=1;class cs{constructor(a){this.listenProvider_=a,this.syncPointTree_=new bL(null),this.pendingWriteTree_={visibleWrites:bM.empty(),allWrites:[],lastWriteId:-1},this.tagToQueryMap=new Map,this.queryToTagMap=new Map}}function ct(a,b,c,d,e){var f,g;return(f=a.pendingWriteTree_,g=e,(0,p.vA)(d>f.lastWriteId,"Stacking an older write on top of newer ones"),void 0===g&&(g=!0),f.allWrites.push({path:b,snap:c,writeId:d,visible:g}),g&&(f.visibleWrites=bN(f.visibleWrites,b,c)),f.lastWriteId=d,e)?cz(a,new bB(bw(),b,c)):[]}function cu(a,b,c=!1){let d=function(a,b){for(let c=0;c<a.allWrites.length;c++){let d=a.allWrites[c];if(d.writeId===b)return d}return null}(a.pendingWriteTree_,b);if(!function(a,b){var c;let d=a.allWrites.findIndex(a=>a.writeId===b);(0,p.vA)(d>=0,"removeWrite called with nonexistent writeId.");let e=a.allWrites[d];a.allWrites.splice(d,1);let f=e.visible,g=!1,h=a.allWrites.length-1;for(;f&&h>=0;){let b=a.allWrites[h];b.visible&&(h>=d&&function(a,b){if(a.snap)return aL(a.path,b);for(let c in a.children)if(a.children.hasOwnProperty(c)&&aL(aG(a.path,c),b))return!0;return!1}(b,e.path)?f=!1:aL(e.path,b.path)&&(g=!0)),h--}return!!f&&(g?((c=a).visibleWrites=bX(c.allWrites,bW,az()),c.allWrites.length>0?c.lastWriteId=c.allWrites[c.allWrites.length-1].writeId:c.lastWriteId=-1):e.snap?a.visibleWrites=bP(a.visibleWrites,e.path):X(e.children,b=>{a.visibleWrites=bP(a.visibleWrites,aG(e.path,b))}),!0)}(a.pendingWriteTree_,b))return[];{let b=new bL(null);return null!=d.snap?b=b.set(az(),!0):X(d.children,a=>{b=b.set(new ay(a),!0)}),cz(a,new bz(d.path,b,c))}}function cv(a,b,c){return cz(a,new bB(bx(),b,c))}function cw(a,b,c,d,e=!1){let f=b._path,g=a.syncPointTree_.get(f),h=[];if(g&&("default"===b._queryIdentifier||null!=co(g,b))){let k=function(a,b,c,d){let e=b._queryIdentifier,f=[],g=[],h=cp(a);if("default"===e)for(let[b,e]of a.views.entries())g=g.concat(cg(e,c,d)),cf(e)&&(a.views.delete(b),e.query._queryParams.loadsAllData()||f.push(e.query));else{let b=a.views.get(e);b&&(g=g.concat(cg(b,c,d)),cf(b)&&(a.views.delete(e),b.query._queryParams.loadsAllData()||f.push(b.query)))}return h&&!cp(a)&&f.push(new((0,p.vA)(l,"Reference.ts has not been loaded"),l)(b._repo,b._path)),{removed:f,events:g}}(g,b,c,d);0===g.views.size&&(a.syncPointTree_=a.syncPointTree_.remove(f));let m=k.removed;if(h=k.events,!e){let c=-1!==m.findIndex(a=>a._queryParams.loadsAllData()),e=a.syncPointTree_.findOnPath(f,(a,b)=>cp(b));if(c&&!e){let b=a.syncPointTree_.subtree(f);if(!b.isEmpty()){let c=b.fold((a,b,c)=>{if(b&&cp(b))return[cq(b)];{let a=[];return b&&(a=cm(b)),X(c,(b,c)=>{a=a.concat(c)}),a}});for(let b=0;b<c.length;++b){let d=c[b],e=d.query,f=cA(a,d);a.listenProvider_.startListening(cG(e),cB(a,e),f.hashFn,f.onComplete)}}}e||!(m.length>0)||d||(c?a.listenProvider_.stopListening(cG(b),null):m.forEach(b=>{let c=a.queryToTagMap.get(cC(b));a.listenProvider_.stopListening(cG(b),c)}))}var i=a,j=m;for(let a=0;a<j.length;++a){let b=j[a];if(!b._queryParams.loadsAllData()){let a=cC(b),c=i.queryToTagMap.get(a);i.queryToTagMap.delete(a),i.tagToQueryMap.delete(c)}}}return h}function cx(a,b,c,d){let e=cD(a,d);if(null==e)return[];{let d=cE(e),f=d.path,g=d.queryId,h=aI(f,b);return cF(a,f,new bB(by(g),h,c))}}function cy(a,b,c){let d=a.pendingWriteTree_,e=a.syncPointTree_.findOnPath(b,(a,c)=>{let d=cn(c,aI(a,b));if(d)return d});return bY(d,b,e,c,!0)}function cz(a,b){var c;return function a(b,c,d,e){if(aH(b.path))return function a(b,c,d,e){let f=c.get(az());null==d&&null!=f&&(d=cn(f,az()));let g=[];return c.children.inorderTraversal((c,f)=>{let h=d?d.getImmediateChild(c):null,i=b2(e,c),j=b.operationForChild(c);j&&(g=g.concat(a(j,f,h,i)))}),f&&(g=g.concat(ck(f,b,e,d))),g}(b,c,d,e);{let f=c.get(az());null==d&&null!=f&&(d=cn(f,az()));let g=[],h=aA(b.path),i=b.operationForChild(h),j=c.children.get(h);if(j&&i){let b=d?d.getImmediateChild(h):null,c=b2(e,h);g=g.concat(a(i,j,b,c))}return f&&(g=g.concat(ck(f,b,e,d))),g}}(b,a.syncPointTree_,null,(c=a.pendingWriteTree_,b3(az(),c)))}function cA(a,b){let c=b.query,d=cB(a,c);return{hashFn:()=>(b.viewCache_.serverCache.getNode()||a9.EMPTY_NODE).hash(),onComplete:b=>{if("ok"===b)if(d)return function(a,b,c){let d=cD(a,c);if(!d)return[];{let c=cE(d),e=c.path,f=c.queryId,g=aI(e,b);return cF(a,e,new bA(by(f),g))}}(a,c._path,d);else{var e;return e=c._path,cz(a,new bA(bx(),e))}{let d=function(a,b){let c="Unknown Error";"too_big"===a?c="The data requested exceeds the maximum size that can be accessed with a single request.":"permission_denied"===a?c="Client doesn't have permission to access the desired data.":"unavailable"===a&&(c="The service is unavailable");let d=Error(a+" at "+b._path.toString()+": "+c);return d.code=a.toUpperCase(),d}(b,c);return cw(a,c,null,d)}}}}function cB(a,b){let c=cC(b);return a.queryToTagMap.get(c)}function cC(a){return a._path.toString()+"$"+a._queryIdentifier}function cD(a,b){return a.tagToQueryMap.get(b)}function cE(a){let b=a.indexOf("$");return(0,p.vA)(-1!==b&&b<a.length-1,"Bad queryKey."),{queryId:a.substr(b+1),path:new ay(a.substr(0,b))}}function cF(a,b,c){let d=a.syncPointTree_.get(b);return(0,p.vA)(d,"Missing sync point for query tag that we're tracking"),ck(d,c,b3(b,a.pendingWriteTree_),null)}function cG(a){return a._queryParams.loadsAllData()&&!a._queryParams.isDefault()?new((0,p.vA)(m,"Reference.ts has not been loaded"),m)(a._repo,a._path):a}class cH{constructor(a){this.node_=a}getImmediateChild(a){return new cH(this.node_.getImmediateChild(a))}node(){return this.node_}}class cI{constructor(a,b){this.syncTree_=a,this.path_=b}getImmediateChild(a){let b=aG(this.path_,a);return new cI(this.syncTree_,b)}node(){return cy(this.syncTree_,this.path_)}}let cJ=function(a,b,c){return a&&"object"==typeof a?((0,p.vA)(".sv"in a,"Unexpected leaf node or priority contents"),"string"==typeof a[".sv"])?cK(a[".sv"],b,c):"object"==typeof a[".sv"]?cL(a[".sv"],b):void(0,p.vA)(!1,"Unexpected server value: "+JSON.stringify(a,null,2)):a},cK=function(a,b,c){if("timestamp"===a)return c.timestamp;(0,p.vA)(!1,"Unexpected server value: "+a)},cL=function(a,b,c){a.hasOwnProperty("increment")||(0,p.vA)(!1,"Unexpected server value: "+JSON.stringify(a,null,2));let d=a.increment;"number"!=typeof d&&(0,p.vA)(!1,"Unexpected increment value: "+d);let e=b.node();if((0,p.vA)(null!=e,"Expected ChildrenNode.EMPTY_NODE for nulls"),!e.isLeafNode())return d;let f=e.getValue();return"number"!=typeof f?d:f+d},cM=function(a,b,c,d){return cO(b,new cI(c,a),d)},cN=function(a,b,c){return cO(a,new cH(b),c)};function cO(a,b,c){let d,e=cJ(a.getPriority().val(),b.getImmediateChild(".priority"),c);if(!a.isLeafNode())return d=a,e!==a.getPriority().val()&&(d=d.updatePriority(new a1(e))),a.forEachChild(a3,(a,e)=>{let f=cO(e,b.getImmediateChild(a),c);f!==e&&(d=d.updateImmediateChild(a,f))}),d;{let d=cJ(a.getValue(),b,c);return d!==a.getValue()||e!==a.getPriority().val()?new a1(d,bc(e)):a}}class cP{constructor(a="",b=null,c={children:{},childCount:0}){this.name=a,this.parent=b,this.node=c}}function cQ(a,b){let c=b instanceof ay?b:new ay(b),d=a,e=aA(c);for(;null!==e;){let a=(0,p.yw)(d.node.children,e)||{children:{},childCount:0};d=new cP(e,d,a),e=aA(c=aC(c))}return d}function cR(a){return a.node.value}function cS(a,b){a.node.value=b,function a(b){null!==b.parent&&function(b,c,d){let e=void 0===cR(d)&&!cT(d),f=(0,p.gR)(b.node.children,c);e&&f?(delete b.node.children[c],b.node.childCount--,a(b)):e||f||(b.node.children[c]=d.node,b.node.childCount++,a(b))}(b.parent,b.name,b)}(a)}function cT(a){return a.node.childCount>0}function cU(a,b){X(a.node.children,(c,d)=>{b(new cP(c,a,d))})}function cV(a){return new ay(null===a.parent?a.name:cV(a.parent)+"/"+a.name)}let cW=/[\[\].#$\/\u0000-\u001F\u007F]/,cX=/[\[\].#$\u0000-\u001F\u007F]/,cY=function(a){return"string"==typeof a&&0!==a.length&&!cW.test(a)},cZ=function(a){return"string"==typeof a&&0!==a.length&&!cX.test(a)},c$=function(a,b,c,d){d&&void 0===b||c_((0,p.dI)(a,"value"),b,c)},c_=function(a,b,c){let d=c instanceof ay?new aM(c,a):c;if(void 0===b)throw Error(a+"contains undefined "+aO(d));if("function"==typeof b)throw Error(a+"contains a function "+aO(d)+" with contents = "+b.toString());if(O(b))throw Error(a+"contains "+b.toString()+" "+aO(d));if("string"==typeof b&&b.length>3495253.3333333335&&(0,p.OE)(b)>0xa00000)throw Error(a+"contains a string greater than 10485760 utf8 bytes "+aO(d)+" ('"+b.substring(0,50)+"...')");if(b&&"object"==typeof b){let c=!1,e=!1;if(X(b,(b,f)=>{if(".value"===b)c=!0;else if(".priority"!==b&&".sv"!==b&&(e=!0,!cY(b)))throw Error(a+" contains an invalid key ("+b+") "+aO(d)+'.  Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"');d.parts_.length>0&&(d.byteLength_+=1),d.parts_.push(b),d.byteLength_+=(0,p.OE)(b),aN(d),c_(a,f,d);let g=d.parts_.pop();d.byteLength_-=(0,p.OE)(g),d.parts_.length>0&&(d.byteLength_-=1)}),c&&e)throw Error(a+' contains ".value" child '+aO(d)+" in addition to actual children.")}},c0=function(a,b){let c,d;for(c=0;c<b.length;c++){let e=aE(d=b[c]);for(let b=0;b<e.length;b++)if(".priority"===e[b]&&b===e.length-1);else if(!cY(e[b]))throw Error(a+"contains an invalid key ("+e[b]+") in path "+d.toString()+'. Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"')}b.sort(aJ);let e=null;for(c=0;c<b.length;c++){if(d=b[c],null!==e&&aL(e,d))throw Error(a+"contains a path "+e.toString()+" that is ancestor of another path "+d.toString());e=d}},c1=function(a,b,c,d){if(d&&void 0===b)return;let e=(0,p.dI)(a,"values");if(!(b&&"object"==typeof b)||Array.isArray(b))throw Error(e+" must be an object containing the children to replace.");let f=[];X(b,(a,b)=>{let d=new ay(a);if(c_(e,b,aG(c,d)),".priority"===aD(d)&&!(null===b||"string"==typeof b||"number"==typeof b&&!O(b)||b&&"object"==typeof b&&(0,p.gR)(b,".sv")))throw Error(e+"contains an invalid value for '"+d.toString()+"', which must be a valid Firebase priority (a string, finite number, server value, or null).");f.push(d)}),c0(e,f)},c2=function(a,b,c,d){if((!d||void 0!==c)&&!cZ(c))throw Error((0,p.dI)(a,b)+'was an invalid path = "'+c+'". Paths must be non-empty strings and can\'t contain ".", "#", "$", "[", or "]"')},c3=function(a,b,c,d){c&&(c=c.replace(/^\/*\.info(\/|$)/,"/")),c2(a,b,c,d)},c4=function(a,b){if(".info"===aA(b))throw Error(a+" failed = Can't modify data under /.info/")},c5=function(a,b){var c;let d=b.path.toString();if("string"!=typeof b.repoInfo.host||0===b.repoInfo.host.length||!cY(b.repoInfo.namespace)&&"localhost"!==b.repoInfo.host.split(":")[0]||0!==d.length&&((c=d)&&(c=c.replace(/^\/*\.info(\/|$)/,"/")),!cZ(c)))throw Error((0,p.dI)(a,"url")+'must be a valid firebase URL and the path can\'t contain ".", "#", "$", "[", or "]".')};class c6{constructor(){this.eventLists_=[],this.recursionDepth_=0}}function c7(a,b){let c=null;for(let d=0;d<b.length;d++){let e=b[d],f=e.getPath();null===c||aK(f,c.path)||(a.eventLists_.push(c),c=null),null===c&&(c={events:[],path:f}),c.events.push(e)}c&&a.eventLists_.push(c)}function c8(a,b,c){c7(a,c),function(a,b){a.recursionDepth_++;let c=!0;for(let d=0;d<a.eventLists_.length;d++){let e=a.eventLists_[d];e&&(b(e.path)?(function(a){for(let b=0;b<a.events.length;b++){let c=a.events[b];if(null!==c){a.events[b]=null;let d=c.getEventRunner();F&&I("event: "+c.toString()),_(d)}}}(a.eventLists_[d]),a.eventLists_[d]=null):c=!1)}c&&(a.eventLists_=[]),a.recursionDepth_--}(a,a=>aL(a,b)||aL(b,a))}class c9{constructor(a,b,c,d){this.repoInfo_=a,this.forceRestClient_=b,this.authTokenProvider_=c,this.appCheckProvider_=d,this.dataUpdateCount=0,this.statsListener_=null,this.eventQueue_=new c6,this.nextWriteId_=1,this.interceptServerDataCallback_=null,this.onDisconnect_=bs(),this.transactionQueueTree_=new cP,this.persistentConnection_=null,this.key=this.repoInfo_.toURLString()}toString(){return(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host}}function da(a){let b=a.infoData_.getNode(new ay(".info/serverTimeOffset")).val()||0;return new Date().getTime()+b}function db(a){var b;return(b=b={timestamp:da(a)}).timestamp=b.timestamp||new Date().getTime(),b}function dc(a,b,c,d,e){a.dataUpdateCount++;let f=new ay(b);c=a.interceptServerDataCallback_?a.interceptServerDataCallback_(b,c):c;let g=[];if(e)if(d){let b=(0,p.kH)(c,a=>bc(a));g=function(a,b,c,d){let e=cD(a,d);if(!e)return[];{let d=cE(e),f=d.path,g=d.queryId,h=aI(f,b),i=bL.fromObject(c);return cF(a,f,new bC(by(g),h,i))}}(a.serverSyncTree_,f,b,e)}else{let b=bc(c);g=cx(a.serverSyncTree_,f,b,e)}else if(d){let b=(0,p.kH)(c,a=>bc(a));g=function(a,b,c){let d=bL.fromObject(c);return cz(a,new bC(bx(),b,d))}(a.serverSyncTree_,f,b)}else{let b=bc(c);g=cv(a.serverSyncTree_,f,b)}let h=f;g.length>0&&(h=dj(a,f)),c8(a.eventQueue_,h,g)}function dd(a,b){de(a,"connected",b),!1===b&&function(a){dg(a,"onDisconnectEvents");let b=db(a),c=bs();bt(a.onDisconnect_,az(),(d,e)=>{let f=cM(d,e,a.serverSyncTree_,b);!function a(b,c,d){if(aH(c))b.value=d,b.children.clear();else if(null!==b.value)b.value=b.value.updateChild(c,d);else{let e=aA(c);b.children.has(e)||b.children.set(e,bs()),a(b.children.get(e),c=aC(c),d)}}(c,d,f)});let d=[];bt(c,az(),(b,c)=>{d=d.concat(cv(a.serverSyncTree_,b,c));let e=dn(a,b);dj(a,e)}),a.onDisconnect_=bs(),c8(a.eventQueue_,az(),d)}(a)}function de(a,b,c){let d=new ay("/.info/"+b),e=bc(c);a.infoData_.updateSnapshot(d,e);let f=cv(a.infoSyncTree_,d,e);c8(a.eventQueue_,d,f)}function df(a){return a.nextWriteId_++}function dg(a,...b){let c="";a.persistentConnection_&&(c=a.persistentConnection_.id+":"),I(c,...b)}function dh(a,b,c,d){b&&_(()=>{if("ok"===c)b(null);else{let a=(c||"error").toUpperCase(),e=a;d&&(e+=": "+d);let f=Error(e);f.code=a,b(f)}})}function di(a,b,c){return cy(a.serverSyncTree_,b,c)||a9.EMPTY_NODE}function dj(a,b){let c=dk(a,b),d=cV(c),e=dl(a,c);return function(a,b,c){if(0===b.length)return;let d=[],e=[],f=b.filter(a=>0===a.status).map(a=>a.currentWriteId);for(let g=0;g<b.length;g++){let h=b[g],i=aI(c,h.path),j=!1,k;if((0,p.vA)(null!==i,"rerunTransactionsUnderNode_: relativePath should not be null."),4===h.status)j=!0,k=h.abortReason,e=e.concat(cu(a.serverSyncTree_,h.currentWriteId,!0));else if(0===h.status)if(h.retryCount>=25)j=!0,k="maxretry",e=e.concat(cu(a.serverSyncTree_,h.currentWriteId,!0));else{let c=di(a,h.path,f);h.currentInputSnapshot=c;let d=b[g].update(c.val());if(void 0!==d){c_("transaction failed: Data returned ",d,h.path);let b=bc(d);"object"==typeof d&&null!=d&&(0,p.gR)(d,".priority")||(b=b.updatePriority(c.getPriority()));let g=h.currentWriteId,i=cN(b,c,db(a));h.currentOutputSnapshotRaw=b,h.currentOutputSnapshotResolved=i,h.currentWriteId=df(a),f.splice(f.indexOf(g),1),e=(e=e.concat(ct(a.serverSyncTree_,h.path,i,h.currentWriteId,h.applyLocally))).concat(cu(a.serverSyncTree_,g,!0))}else j=!0,k="nodata",e=e.concat(cu(a.serverSyncTree_,h.currentWriteId,!0))}c8(a.eventQueue_,c,e),e=[],j&&(b[g].status=2,setTimeout(b[g].unwatcher,Math.floor(0)),b[g].onComplete&&("nodata"===k?d.push(()=>b[g].onComplete(null,!1,b[g].currentInputSnapshot)):d.push(()=>b[g].onComplete(Error(k),!1,null))))}dm(a,a.transactionQueueTree_);for(let a=0;a<d.length;a++)_(d[a]);!function a(b,c=b.transactionQueueTree_){if(c||dm(b,c),cR(c)){let d=dl(b,c);(0,p.vA)(d.length>0,"Sending zero length transaction queue"),d.every(a=>0===a.status)&&function(b,c,d){let e=di(b,c,d.map(a=>a.currentWriteId)),f=e,g=e.hash();for(let a=0;a<d.length;a++){let b=d[a];(0,p.vA)(0===b.status,"tryToSendTransactionQueue_: items in queue should all be run."),b.status=1,b.retryCount++;let e=aI(c,b.path);f=f.updateChild(e,b.currentOutputSnapshotRaw)}let h=f.val(!0);b.server_.put(c.toString(),h,e=>{dg(b,"transaction put response",{path:c.toString(),status:e});let f=[];if("ok"===e){let e=[];for(let a=0;a<d.length;a++)d[a].status=2,f=f.concat(cu(b.serverSyncTree_,d[a].currentWriteId)),d[a].onComplete&&e.push(()=>d[a].onComplete(null,!0,d[a].currentOutputSnapshotResolved)),d[a].unwatcher();dm(b,cQ(b.transactionQueueTree_,c)),a(b,b.transactionQueueTree_),c8(b.eventQueue_,c,f);for(let a=0;a<e.length;a++)_(e[a])}else{if("datastale"===e)for(let a=0;a<d.length;a++)3===d[a].status?d[a].status=4:d[a].status=0;else{M("transaction at "+c.toString()+" failed: "+e);for(let a=0;a<d.length;a++)d[a].status=4,d[a].abortReason=e}dj(b,c)}},g)}(b,cV(c),d)}else cT(c)&&cU(c,c=>{a(b,c)})}(a,a.transactionQueueTree_)}(a,e,d),d}function dk(a,b){let c,d=a.transactionQueueTree_;for(c=aA(b);null!==c&&void 0===cR(d);)d=cQ(d,c),c=aA(b=aC(b));return d}function dl(a,b){let c=[];return function a(b,c,d){let e=cR(c);if(e)for(let a=0;a<e.length;a++)d.push(e[a]);cU(c,c=>{a(b,c,d)})}(a,b,c),c.sort((a,b)=>a.order-b.order),c}function dm(a,b){let c=cR(b);if(c){let a=0;for(let b=0;b<c.length;b++)2!==c[b].status&&(c[a]=c[b],a++);c.length=a,cS(b,c.length>0?c:void 0)}cU(b,b=>{dm(a,b)})}function dn(a,b){let c=cV(dk(a,b)),d=cQ(a.transactionQueueTree_,b);return!function(a,b,c){let d=a.parent;for(;null!==d;){if(b(d))return!0;d=d.parent}}(d,b=>{dp(a,b)}),dp(a,d),!function a(b,c,d,e){d&&!e&&c(b),cU(b,b=>{a(b,c,!0,e)}),d&&e&&c(b)}(d,b=>{dp(a,b)}),c}function dp(a,b){let c=cR(b);if(c){let d=[],e=[],f=-1;for(let b=0;b<c.length;b++)3===c[b].status||(1===c[b].status?((0,p.vA)(f===b-1,"All SENT items should be at beginning of queue."),f=b,c[b].status=3,c[b].abortReason="set"):((0,p.vA)(0===c[b].status,"Unexpected transaction status in abort"),c[b].unwatcher(),e=e.concat(cu(a.serverSyncTree_,c[b].currentWriteId,!0)),c[b].onComplete&&d.push(c[b].onComplete.bind(null,Error("set"),!1,null))));-1===f?cS(b,void 0):c.length=f+1,c8(a.eventQueue_,cV(b),e);for(let a=0;a<d.length;a++)_(d[a])}}let dq=function(a,b){let c=dr(a),d=c.namespace;"firebase.com"===c.domain&&L(c.host+" is no longer supported. Please use <YOUR FIREBASE>.firebaseio.com instead"),d&&"undefined"!==d||"localhost"===c.domain||L("Cannot parse Firebase url. Please use https://<YOUR FIREBASE>.firebaseio.com"),c.secure||N();let e="ws"===c.scheme||"wss"===c.scheme;return{repoInfo:new ab(c.host,c.secure,d,e,b,"",d!==c.subdomain),path:new ay(c.pathString)}},dr=function(a){let b="",c="",d="",e="",f="",g=!0,h="https",i=443;if("string"==typeof a){let j=a.indexOf("//");j>=0&&(h=a.substring(0,j-1),a=a.substring(j+2));let k=a.indexOf("/");-1===k&&(k=a.length);let l=a.indexOf("?");-1===l&&(l=a.length),b=a.substring(0,Math.min(k,l)),k<l&&(e=function(a){let b="",c=a.split("/");for(let a=0;a<c.length;a++)if(c[a].length>0){let d=c[a];try{d=decodeURIComponent(d.replace(/\+/g," "))}catch(a){}b+="/"+d}return b}(a.substring(k,l)));let m=function(a){let b={};for(let c of("?"===a.charAt(0)&&(a=a.substring(1)),a.split("&"))){if(0===c.length)continue;let d=c.split("=");2===d.length?b[decodeURIComponent(d[0])]=decodeURIComponent(d[1]):M(`Invalid query segment '${c}' in query '${a}'`)}return b}(a.substring(Math.min(a.length,l)));(j=b.indexOf(":"))>=0?(g="https"===h||"wss"===h,i=parseInt(b.substring(j+1),10)):j=b.length;let n=b.slice(0,j);if("localhost"===n.toLowerCase())c="localhost";else if(n.split(".").length<=2)c=n;else{let a=b.indexOf(".");d=b.substring(0,a).toLowerCase(),c=b.substring(a+1),f=d}"ns"in m&&(f=m.ns)}return{host:b,port:i,domain:c,subdomain:d,secure:g,scheme:h,pathString:e,namespace:f}},ds="-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz",dt=function(){let a=0,b=[];return function(c){let d,e=c===a;a=c;let f=Array(8);for(d=7;d>=0;d--)f[d]=ds.charAt(c%64),c=Math.floor(c/64);(0,p.vA)(0===c,"Cannot push at time == 0");let g=f.join("");if(e){for(d=11;d>=0&&63===b[d];d--)b[d]=0;b[d]++}else for(d=0;d<12;d++)b[d]=Math.floor(64*Math.random());for(d=0;d<12;d++)g+=ds.charAt(b[d]);return(0,p.vA)(20===g.length,"nextPushId: Length should be 20."),g}}();class du{constructor(a,b,c,d){this.eventType=a,this.eventRegistration=b,this.snapshot=c,this.prevName=d}getPath(){let a=this.snapshot.ref;return"value"===this.eventType?a._path:a.parent._path}getEventType(){return this.eventType}getEventRunner(){return this.eventRegistration.getEventRunner(this)}toString(){return this.getPath().toString()+":"+this.eventType+":"+(0,p.As)(this.snapshot.exportVal())}}class dv{constructor(a,b,c){this.eventRegistration=a,this.error=b,this.path=c}getPath(){return this.path}getEventType(){return"cancel"}getEventRunner(){return this.eventRegistration.getEventRunner(this)}toString(){return this.path.toString()+":cancel"}}class dw{constructor(a,b){this.snapshotCallback=a,this.cancelCallback=b}onValue(a,b){this.snapshotCallback.call(null,a,b)}onCancel(a){return(0,p.vA)(this.hasCancelCallback,"Raising a cancel event on a listener with no cancel callback"),this.cancelCallback.call(null,a)}get hasCancelCallback(){return!!this.cancelCallback}matches(a){return this.snapshotCallback===a.snapshotCallback||void 0!==this.snapshotCallback.userCallback&&this.snapshotCallback.userCallback===a.snapshotCallback.userCallback&&this.snapshotCallback.context===a.snapshotCallback.context}}class dx{constructor(a,b,c,d){this._repo=a,this._path=b,this._queryParams=c,this._orderByCalled=d}get key(){return aH(this._path)?null:aD(this._path)}get ref(){return new dy(this._repo,this._path)}get _queryIdentifier(){let a=V(bp(this._queryParams));return"{}"===a?"default":a}get _queryObject(){return bp(this._queryParams)}isEqual(a){if(!((a=(0,p.Ku)(a))instanceof dx))return!1;let b=this._repo===a._repo,c=aK(this._path,a._path),d=this._queryIdentifier===a._queryIdentifier;return b&&c&&d}toJSON(){return this.toString()}toString(){return this._repo.toString()+function(a){let b="";for(let c=a.pieceNum_;c<a.pieces_.length;c++)""!==a.pieces_[c]&&(b+="/"+encodeURIComponent(String(a.pieces_[c])));return b||"/"}(this._path)}}class dy extends dx{constructor(a,b){super(a,b,new bn,!1)}get parent(){let a=aF(this._path);return null===a?null:new dy(this._repo,a)}get root(){let a=this;for(;null!==a.parent;)a=a.parent;return a}}class dz{constructor(a,b,c){this._node=a,this.ref=b,this._index=c}get priority(){return this._node.getPriority().val()}get key(){return this.ref.key}get size(){return this._node.numChildren()}child(a){let b=new ay(a),c=dB(this.ref,a);return new dz(this._node.getChild(b),c,a3)}exists(){return!this._node.isEmpty()}exportVal(){return this._node.val(!0)}forEach(a){return!this._node.isLeafNode()&&!!this._node.forEachChild(this._index,(b,c)=>a(new dz(c,dB(this.ref,b),a3)))}hasChild(a){let b=new ay(a);return!this._node.getChild(b).isEmpty()}hasChildren(){return!this._node.isLeafNode()&&!this._node.isEmpty()}toJSON(){return this.exportVal()}val(){return this._node.val()}}function dA(a,b){return(a=(0,p.Ku)(a))._checkNotDeleted("ref"),void 0!==b?dB(a._root,b):a._root}function dB(a,b){return null===aA((a=(0,p.Ku)(a))._path)?c3("child","path",b,!1):c2("child","path",b,!1),new dy(a._repo,aG(a._path,b))}function dC(a,b){let c;c4("push",(a=(0,p.Ku)(a))._path),c$("push",b,a._path,!0);let d=dt(da(a._repo)),e=dB(a,d),f=dB(a,d);return e.then=(c=null!=b?dE(f,b).then(()=>f):Promise.resolve(f)).then.bind(c),e.catch=c.then.bind(c,void 0),e}function dD(a){return c4("remove",a._path),dE(a,null)}function dE(a,b){c4("set",(a=(0,p.Ku)(a))._path),c$("set",b,a._path,!1);let c=new p.cY;return!function(a,b,c,d,e){dg(a,"set",{path:b.toString(),value:c,priority:null});let f=db(a),g=bc(c,null),h=cN(g,cy(a.serverSyncTree_,b),f),i=df(a),j=ct(a.serverSyncTree_,b,h,i,!0);c7(a.eventQueue_,j),a.server_.put(b.toString(),g.val(!0),(c,d)=>{let f="ok"===c;f||M("set at "+b+" failed: "+c);let g=cu(a.serverSyncTree_,i,!f);c8(a.eventQueue_,b,g),dh(a,e,c,d)});let k=dn(a,b);dj(a,k),c8(a.eventQueue_,k,[])}(a._repo,a._path,b,null,c.wrapCallback(()=>{})),c.promise}function dF(a,b){c1("update",b,a._path,!1);let c=new p.cY;return!function(a,b,c,d){dg(a,"update",{path:b.toString(),value:c});let e=!0,f=db(a),g={};if(X(c,(c,d)=>{e=!1,g[c]=cM(aG(b,c),bc(d),a.serverSyncTree_,f)}),e)I("update() called with empty data.  Don't do anything."),dh(a,d,"ok",void 0);else{let e=df(a),f=function(a,b,c,d){var e;e=a.pendingWriteTree_,(0,p.vA)(d>e.lastWriteId,"Stacking an older merge on top of newer ones"),e.allWrites.push({path:b,children:c,writeId:d,visible:!0}),e.visibleWrites=bO(e.visibleWrites,b,c),e.lastWriteId=d;let f=bL.fromObject(c);return cz(a,new bC(bw(),b,f))}(a.serverSyncTree_,b,g,e);c7(a.eventQueue_,f),a.server_.merge(b.toString(),c,(c,f)=>{let g="ok"===c;g||M("update at "+b+" failed: "+c);let h=cu(a.serverSyncTree_,e,!g),i=h.length>0?dj(a,b):b;c8(a.eventQueue_,i,h),dh(a,d,c,f)}),X(c,c=>{let d=dn(a,aG(b,c));dj(a,d)}),c8(a.eventQueue_,b,[])}}(a._repo,a._path,b,c.wrapCallback(()=>{})),c.promise}function dG(a){a=(0,p.Ku)(a);let b=new dH(new dw(()=>{}));return(function(a,b,c){let d=function(a,b){var c;let d=b._path,e=null;a.syncPointTree_.foreachOnPath(d,(a,b)=>{let c=aI(a,d);e=e||cn(b,c)});let f=a.syncPointTree_.get(d);f?e=e||cn(f,az()):(f=new cj,a.syncPointTree_=a.syncPointTree_.set(d,f));let g=null!=e,h=g?new bD(e,!0,!1):null,i=(c=a.pendingWriteTree_,b3(b._path,c));return bJ(cl(f,b,i,g?h.getNode():a9.EMPTY_NODE,g).viewCache_)}(a.serverSyncTree_,b);return null!=d?Promise.resolve(d):a.server_.get(b).then(d=>{let e,f=bc(d).withIndex(b._queryParams.getIndex());if(!function(a,b,c,d=!1){let e,f=b._path,g=null,h=!1;a.syncPointTree_.foreachOnPath(f,(a,b)=>{let c=aI(a,f);g=g||cn(b,c),h=h||cp(b)});let i=a.syncPointTree_.get(f);i?(h=h||cp(i),g=g||cn(i,az())):(i=new cj,a.syncPointTree_=a.syncPointTree_.set(f,i)),null!=g?e=!0:(e=!1,g=a9.EMPTY_NODE,a.syncPointTree_.subtree(f).foreachChild((a,b)=>{let c=cn(b,az());c&&(g=g.updateImmediateChild(a,c))}));let j=null!=co(i,b);if(!j&&!b._queryParams.loadsAllData()){let c=cC(b);(0,p.vA)(!a.queryToTagMap.has(c),"View does not exist, but we have a tag");let d=cr++;a.queryToTagMap.set(c,d),a.tagToQueryMap.set(d,c)}let k=function(a,b,c,d,e,f){let g=cl(a,b,d,e,f);a.views.has(b._queryIdentifier)||a.views.set(b._queryIdentifier,g),g.eventRegistrations_.push(c);let h=g.viewCache_.eventCache,i=[];return h.getNode().isLeafNode()||h.getNode().forEachChild(a3,(a,b)=>{i.push(bh(a,b))}),h.isFullyInitialized()&&i.push(bg(h.getNode())),ci(g,i,h.getNode(),c)}(i,b,c,b3(f,a.pendingWriteTree_),g,e);if(!j&&!h&&!d){let c=co(i,b);k=k.concat(function(a,b,c){let d=b._path,e=cB(a,b),f=cA(a,c),g=a.listenProvider_.startListening(cG(b),e,f.hashFn,f.onComplete),h=a.syncPointTree_.subtree(d);if(e)(0,p.vA)(!cp(h.value),"If we're adding a query, it shouldn't be shadowed");else{let b=h.fold((a,b,c)=>{if(!aH(a)&&b&&cp(b))return[cq(b).query];{let a=[];return b&&(a=a.concat(cm(b).map(a=>a.query))),X(c,(b,c)=>{a=a.concat(c)}),a}});for(let c=0;c<b.length;++c){let d=b[c];a.listenProvider_.stopListening(cG(d),cB(a,d))}}return g}(a,b,c))}}(a.serverSyncTree_,b,c,!0),b._queryParams.loadsAllData())e=cv(a.serverSyncTree_,b._path,f);else{let c=cB(a.serverSyncTree_,b);e=cx(a.serverSyncTree_,b._path,f,c)}return c8(a.eventQueue_,b._path,e),cw(a.serverSyncTree_,b,c,null,!0),f},c=>(dg(a,"get for query "+(0,p.As)(b)+" failed: "+c),Promise.reject(Error(c))))})(a._repo,a,b).then(b=>new dz(b,new dy(a._repo,a._path),a._queryParams.getIndex()))}class dH{constructor(a){this.callbackContext=a}respondsTo(a){return"value"===a}createEvent(a,b){let c=b._queryParams.getIndex();return new du("value",this,new dz(a.snapshotNode,new dy(b._repo,b._path),c))}getEventRunner(a){return"cancel"===a.getEventType()?()=>this.callbackContext.onCancel(a.error):()=>this.callbackContext.onValue(a.snapshot,null)}createCancelEvent(a,b){return this.callbackContext.hasCancelCallback?new dv(this,a,b):null}matches(a){return a instanceof dH&&(!a.callbackContext||!this.callbackContext||a.callbackContext.matches(this.callbackContext))}hasAnyCallback(){return null!==this.callbackContext}}(0,p.vA)(!l,"__referenceConstructor has already been defined"),l=dy,(0,p.vA)(!m,"__referenceConstructor has already been defined"),m=dy;let dI={};class dJ{constructor(a,b){this._repoInternal=a,this.app=b,this.type="database",this._instanceStarted=!1}get _repo(){return this._instanceStarted||(!function(a,b,c){if(a.stats_=ag(a.repoInfo_),a.forceRestClient_||("object"==typeof window&&window.navigator&&window.navigator.userAgent||"").search(/googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i)>=0)a.server_=new bq(a.repoInfo_,(b,c,d,e)=>{dc(a,b,c,d,e)},a.authTokenProvider_,a.appCheckProvider_),setTimeout(()=>dd(a,!0),0);else{if(null!=c){if("object"!=typeof c)throw Error("Only objects are supported for option databaseAuthVariableOverride");try{(0,p.As)(c)}catch(a){throw Error("Invalid authOverride provided: "+a)}}a.persistentConnection_=new aQ(a.repoInfo_,b,(b,c,d,e)=>{dc(a,b,c,d,e)},b=>{dd(a,b)},b=>{var c;c=a,X(b,(a,b)=>{de(c,a,b)})},a.authTokenProvider_,a.appCheckProvider_,c),a.server_=a.persistentConnection_}a.authTokenProvider_.addTokenChangeListener(b=>{a.server_.refreshAuthToken(b)}),a.appCheckProvider_.addTokenChangeListener(b=>{a.server_.refreshAppCheckToken(b.token)}),a.statsReporter_=function(a,b){let c=a.toString();return af[c]||(af[c]=b()),af[c]}(a.repoInfo_,()=>new bv(a.stats_,a.server_)),a.infoData_=new br,a.infoSyncTree_=new cs({startListening:(b,c,d,e)=>{let f=[],g=a.infoData_.getNode(b._path);return g.isEmpty()||(f=cv(a.infoSyncTree_,b._path,g),setTimeout(()=>{e("ok")},0)),f},stopListening:()=>{}}),de(a,"connected",!1),a.serverSyncTree_=new cs({startListening:(b,c,d,e)=>(a.server_.listen(b,d,c,(c,d)=>{let f=e(c,d);c8(a.eventQueue_,b._path,f)}),[]),stopListening:(b,c)=>{a.server_.unlisten(b,c)}})}(this._repoInternal,this.app.options.appId,this.app.options.databaseAuthVariableOverride),this._instanceStarted=!0),this._repoInternal}get _root(){return this._rootInternal||(this._rootInternal=new dy(this._repo,az())),this._rootInternal}_delete(){return null!==this._rootInternal&&(!function(a,b){let c=dI[b];c&&c[a.key]===a||L(`Database ${b}(${a.repoInfo_}) has already been deleted.`),a.persistentConnection_&&a.persistentConnection_.interrupt("repo_interrupt"),delete c[a.key]}(this._repo,this.app.name),this._repoInternal=null,this._rootInternal=null),Promise.resolve()}_checkNotDeleted(a){null===this._rootInternal&&L("Cannot call "+a+" on a deleted database.")}}function dK(a=(0,r.Sx)(),b){let c=(0,r.j6)(a,"database").getImmediate({identifier:b});if(!c._instanceStarted){let a=(0,p.yU)("database");a&&function(a,b,c,d={}){let e;(a=(0,p.Ku)(a))._checkNotDeleted("useEmulator");let f=`${b}:${c}`,g=a._repoInternal;if(a._instanceStarted){if(f===a._repoInternal.repoInfo_.host&&(0,p.bD)(d,g.repoInfo_.emulatorOptions))return;L("connectDatabaseEmulator() cannot initialize or alter the emulator configuration after the database instance has started.")}g.repoInfo_.nodeAdmin?(d.mockUserToken&&L('mockUserToken is not supported by the Admin SDK. For client access with mock users, please use the "firebase" package instead of "firebase-admin".'),e=new ao(ao.OWNER)):d.mockUserToken&&(e=new ao("string"==typeof d.mockUserToken?d.mockUserToken:(0,p.Fy)(d.mockUserToken,a.app.options.projectId))),(0,p.zJ)(b)&&((0,p.gE)(b),(0,p.P1)("Database",!0));var h=e;let i=f.lastIndexOf(":"),j=f.substring(0,i);g.repoInfo_=new ab(f,(0,p.zJ)(j),g.repoInfo_.namespace,g.repoInfo_.webSocketOnly,g.repoInfo_.nodeAdmin,g.repoInfo_.persistenceKey,g.repoInfo_.includeNamespaceInQueryParams,!0,d),h&&(g.authTokenProvider_=h)}(c,...a)}return c}aQ.prototype.simpleListen=function(a,b){this.sendRequest("q",{p:a},b)},aQ.prototype.echo=function(a,b){this.sendRequest("echo",{d:a},b)},ai=o.Client;ah=r.MF,(0,r.om)(new s.uA("database",(a,{instanceIdentifier:b})=>{let c=a.getProvider("app").getImmediate();return function(a,b,c,d,e){var f,g,h,i;let j,k,l,m,n=d||a.options.databaseURL;void 0===n&&(a.options.projectId||L("Can't determine Firebase Database URL. Be sure to include  a Project ID when calling firebase.initializeApp()."),I("Using default host for project ",a.options.projectId),n=`${a.options.projectId}-default-rtdb.firebaseio.com`);let o=dq(n,void 0),p=o.repoInfo;"undefined"!=typeof process&&process.env&&(l=process.env.FIREBASE_DATABASE_EMULATOR_HOST),l?(m=!0,p=(o=dq(n=`http://${l}?ns=${p.namespace}`,void 0)).repoInfo):m=!o.repoInfo.secure;let q=e&&m?new ao(ao.OWNER):new an(a.name,a.options,b);return c5("Invalid Firebase Database URL",o),aH(o.path)||L("Database URL must point to the root of a Firebase Database (not including a child path)."),new dJ((f=p,g=a,h=q,i=new am(a,c),(j=dI[g.name])||(j={},dI[g.name]=j),(k=j[f.toURLString()])&&L("Database initialized multiple times. Please make sure the format of the database URL matches with each database() call."),k=new c9(f,!1,h,i),j[f.toURLString()]=k,k),a)}(c,a.getProvider("auth-internal"),a.getProvider("app-check-internal"),b)},"PUBLIC").setMultipleInstances(!0)),(0,r.KO)(ak,al,"node"),(0,r.KO)(ak,al,"esm2020")},90654:(a,b,c)=>{"use strict";var d=c(90380),e=function(a,b){this._session=a,this._method=b,this._queue=new d(e.QUEUE_SIZE),this._stopped=!1,this.pending=0};e.QUEUE_SIZE=8,e.prototype.call=function(a,b,c,d){if(!this._stopped){var e={error:a,message:b,callback:c,context:d,done:!1},f=!1,g=this;if(this._queue.push(e),e.error)return e.done=!0,this._stop(),this._flushQueue();var h=function(a,b){f^(f=!0)&&(a?(g._stop(),e.error=a,e.message=null):e.message=b,e.done=!0,g._flushQueue())};try{this._session[this._method](b,h)}catch(a){h(a)}}},e.prototype._stop=function(){this.pending=this._queue.length,this._stopped=!0},e.prototype._flushQueue=function(){for(var a,b=this._queue;b.length>0&&b.peek().done;)(a=b.shift()).error?(this.pending=0,b.clear()):this.pending-=1,a.callback.call(a.context,a.error,a.message)},a.exports=e},92139:(a,b,c)=>{"use strict";var d=c(27910).Stream,e=c(28354),f=c(74366),g=c(37030),h=c(64492),i=function(a){a=a||{},f.validateOptions(a,["headers","extensions","maxLength","ping","proxy","tls","ca"]),this.readable=this.writable=!0;var b=a.headers;if(b)for(var c in b)this._driver.setHeader(c,b[c]);var d=a.extensions;d&&[].concat(d).forEach(this._driver.addExtension,this._driver),this._ping=a.ping,this._pingId=0,this.readyState=i.CONNECTING,this.bufferedAmount=0,this.protocol="",this.url=this._driver.url,this.version=this._driver.version;var e=this;this._driver.on("open",function(a){e._open()}),this._driver.on("message",function(a){e._receiveMessage(a.data)}),this._driver.on("close",function(a){e._beginClose(a.reason,a.code)}),this._driver.on("error",function(a){e._emitError(a.message)}),this.on("error",function(){}),this._driver.messages.on("drain",function(){e.emit("drain")}),this._ping&&(this._pingTimer=setInterval(function(){e._pingId+=1,e.ping(e._pingId.toString())},1e3*this._ping)),this._configureStream(),this._proxy||(this._stream.pipe(this._driver.io),this._driver.io.pipe(this._stream))};e.inherits(i,d),i.CONNECTING=0,i.OPEN=1,i.CLOSING=2,i.CLOSED=3,i.CLOSE_TIMEOUT=3e4;var j={write:function(a){return this.send(a)},end:function(a){void 0!==a&&this.send(a),this.close()},pause:function(){return this._driver.messages.pause()},resume:function(){return this._driver.messages.resume()},send:function(a){return!(this.readyState>i.OPEN)&&(a instanceof Buffer||(a=String(a)),this._driver.messages.write(a))},ping:function(a,b){return!(this.readyState>i.OPEN)&&this._driver.ping(a,b)},close:function(a,b){if(void 0===a&&(a=1e3),void 0===b&&(b=""),1e3!==a&&(a<3e3||a>4999))throw Error("Failed to execute 'close' on WebSocket: The code must be either 1000, or between 3000 and 4999. "+a+" is neither.");if(this.readyState<i.CLOSING){var c=this;this._closeTimer=setTimeout(function(){c._beginClose("",1006)},i.CLOSE_TIMEOUT)}this.readyState!==i.CLOSED&&(this.readyState=i.CLOSING),this._driver.close(b,a)},_configureStream:function(){var a=this;this._stream.setTimeout(0),this._stream.setNoDelay(!0),["close","end"].forEach(function(b){this._stream.on(b,function(){a._finalizeClose()})},this),this._stream.on("error",function(b){a._emitError("Network error: "+a.url+": "+b.message),a._finalizeClose()})},_open:function(){if(this.readyState===i.CONNECTING){this.readyState=i.OPEN,this.protocol=this._driver.protocol||"";var a=new h("open");a.initEvent("open",!1,!1),this.dispatchEvent(a)}},_receiveMessage:function(a){if(this.readyState>i.OPEN)return!1;this.readable&&this.emit("data",a);var b=new h("message",{data:a});b.initEvent("message",!1,!1),this.dispatchEvent(b)},_emitError:function(a){if(!(this.readyState>=i.CLOSING)){var b=new h("error",{message:a});b.initEvent("error",!1,!1),this.dispatchEvent(b)}},_beginClose:function(a,b){this.readyState!==i.CLOSED&&(this.readyState=i.CLOSING,this._closeParams=[a,b],this._stream&&(this._stream.destroy(),this._stream.readable||this._finalizeClose()))},_finalizeClose:function(){if(this.readyState!==i.CLOSED){this.readyState=i.CLOSED,this._closeTimer&&clearTimeout(this._closeTimer),this._pingTimer&&clearInterval(this._pingTimer),this._stream&&this._stream.end(),this.readable&&this.emit("end"),this.readable=this.writable=!1;var a=this._closeParams?this._closeParams[0]:"",b=new h("close",{code:this._closeParams?this._closeParams[1]:1006,reason:a});b.initEvent("close",!1,!1),this.dispatchEvent(b)}}};for(var k in j)i.prototype[k]=j[k];for(var l in g)i.prototype[l]=g[l];a.exports=i},93841:(a,b,c)=>{"use strict";var d=c(90654),e=c(70376),f=function(a){this._ext=a[0],this._session=a[1],this._functors={incoming:new d(this._session,"processIncomingMessage"),outgoing:new d(this._session,"processOutgoingMessage")}};f.prototype.pending=function(a){var b=this._functors[a];b._stopped||(b.pending+=1)},f.prototype.incoming=function(a,b,c,d){this._exec("incoming",a,b,c,d)},f.prototype.outgoing=function(a,b,c,d){this._exec("outgoing",a,b,c,d)},f.prototype.close=function(){return this._closed=this._closed||new e,this._doClose(),this._closed},f.prototype._exec=function(a,b,c,d,e){this._functors[a].call(b,c,function(a,b){a&&(a.message=this._ext.name+": "+a.message),d.call(e,a,b),this._doClose()},this)},f.prototype._doClose=function(){var a=this._functors.incoming,b=this._functors.outgoing;this._closed&&a.pending+b.pending===0&&(this._session&&this._session.close(),this._session=null,this._closed.done())},a.exports=f}};