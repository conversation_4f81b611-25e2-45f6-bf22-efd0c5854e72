(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[714],{4295:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(5155),l=r(6766),o=r(8274),s=r(2115);function i(e){let{id:t,imageUrl:r,caption:i,name:n,onEdit:c,onDelete:d,isAdmin:u=!1}=e,[h,x]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!0),[g,f]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=()=>{f(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.jsxs)(o.P.div,{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",initial:{opacity:1,y:0},whileInView:{opacity:1,y:0},viewport:{once:!0,amount:.3},transition:{duration:0},whileHover:g?{}:{y:-5,transition:{duration:.2}},children:[(0,a.jsxs)("div",{className:"relative w-full h-0 pb-[100%] overflow-hidden bg-gray-100",children:[m&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}),h?(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCF7"}),(0,a.jsx)("div",{className:"text-sm",children:"Image not available"})]})}):(0,a.jsx)(o.P.div,{whileHover:g?{}:{scale:1.05},transition:{duration:.3},className:"absolute inset-0 w-full h-full",children:(0,a.jsx)(l.default,{src:r,alt:i,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",priority:!1,quality:g?75:85,placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",loading:g?"eager":"lazy",onLoad:()=>{p(!1),console.log("Image loaded successfully: ".concat(r))},onError:e=>{console.error("Image failed to load: ".concat(r),e),x(!0),p(!1)}})})]}),(0,a.jsxs)("div",{className:"p-4",children:[n&&(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 text-lg",children:n}),(0,a.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:i}),u&&(c||d)&&(0,a.jsxs)("div",{className:"mt-4 flex gap-2",children:[c&&(0,a.jsx)(o.P.button,{onClick:()=>c(t),className:"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Edit"}),d&&(0,a.jsx)(o.P.button,{onClick:()=>d(t),className:"px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Delete"})]})]})]})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6104:(e,t,r)=>{"use strict";r.d(t,{OO:()=>c,j2:()=>n});var a=r(3915),l=r(5404),o=r(1115),s=r(7505);let i=(0,a.Wp)({apiKey:"AIzaSyBsEcUNPz8nDZyGRNU2bh1DpXPxnyV3b88",authDomain:"utsavfootwear-as.firebaseapp.com",databaseURL:"https://utsavfootwear-as-default-rtdb.firebaseio.com",projectId:"utsavfootwear-as",storageBucket:"utsavfootwear-as.firebasestorage.app",messagingSenderId:"74148351385",appId:"1:74148351385:web:ff9c1a3cff4395e3d19708",measurementId:"G-YW880XL7ME"}),n=(0,l.xI)(i),c=(0,o.C3)(i);(0,s.c7)(i)},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let a=r(2115);function l(e,t){let r=(0,a.useRef)(null),l=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=o(e,a)),t&&(l.current=o(t,a))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7827:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var a=r(6104),l=r(1115);async function o(e){try{let t=new FormData;t.append("file",e);let r=await fetch("/api/uploadToCloudinary",{method:"POST",body:t});if(!r.ok)throw Error("Failed to upload image to Cloudinary");let a=await r.json();return{url:a.url,publicId:a.public_id}}catch(e){throw console.error("Error uploading to Cloudinary:",e),Error("Failed to upload image")}}async function s(e){try{if(!(await fetch("/api/deleteFromCloudinary",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({publicId:e})})).ok)throw Error("Failed to delete image from Cloudinary")}catch(e){throw console.error("Error deleting from Cloudinary:",e),Error("Failed to delete image")}}class i{async getAllProducts(){try{let e=await (0,l.Jt)(this.productsRef);if(e.exists()){let t=e.val(),r=[];return Object.keys(t).forEach(e=>{r.push({id:e,...t[e]})}),r}return[]}catch(e){throw console.error("Error fetching products:",e),Error("Failed to fetch products")}}async getPaginatedProducts(e,t){try{let r=await (0,l.Jt)(this.productsRef);if(r.exists()){let a=r.val(),l=[];Object.keys(a).forEach(e=>{l.push({id:e,...a[e]})});let o=(l=l.sort((e,t)=>{let r=e.updatedAt||e.createdAt||0;return(t.updatedAt||t.createdAt||0)-r})).length,s=(e-1)*t;return{products:l.slice(s,s+t),total:o}}return{products:[],total:0}}catch(e){throw console.error("Error fetching products:",e),Error("Failed to fetch products")}}async getProductById(e){try{let t=(0,l.KR)(a.OO,"products/".concat(e)),r=await (0,l.Jt)(t);if(r.exists())return{id:e,...r.val()};return null}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}async addProduct(e,t){try{let{url:r,publicId:a}=await o(t),s=Date.now(),i={...e,imageUrl:r,publicId:a,createdAt:s,updatedAt:s},n=(0,l.VC)(this.productsRef);return await (0,l.hZ)(n,i),n.key}catch(e){throw console.error("Error adding product:",e),Error("Failed to add product")}}async updateProduct(e,t,r){try{let i=(0,l.KR)(a.OO,"products/".concat(e)),n=await this.getProductById(e);if(!n)throw Error("Product not found");let c=n.imageUrl,d=n.publicId;if(r){let{url:e,publicId:t}=await o(r);if(c=e,n.publicId)try{await s(n.publicId)}catch(e){console.error("Error deleting old image:",e)}d=t}let u={...n,...t,imageUrl:c,publicId:d,updatedAt:Date.now()};await (0,l.yo)(i,u)}catch(e){throw console.error("Error updating product:",e),Error("Failed to update product")}}async deleteProduct(e){try{let t=await this.getProductById(e);if(!t)throw Error("Product not found");if(t.publicId)try{await s(t.publicId)}catch(e){console.error("Error deleting image from Cloudinary:",e)}let r=(0,l.KR)(a.OO,"products/".concat(e));await (0,l.TF)(r)}catch(e){throw console.error("Error deleting product:",e),Error("Failed to delete product")}}async searchProducts(e){try{let t=await this.getAllProducts(),r=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(r)||e.caption.toLowerCase().includes(r))}catch(e){throw console.error("Error searching products:",e),Error("Failed to search products")}}constructor(){this.productsRef=(0,l.KR)(a.OO,"products")}}let n=new i},9182:(e,t,r)=>{Promise.resolve().then(r.bind(r,9386))},9386:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(5155),l=r(2115),o=r(5695),s=r(5404),i=r(6104),n=r(7827);function c(e){let{children:t}=e,[r,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(!0),u=(0,o.useRouter)();return((0,l.useEffect)(()=>{let e=(0,s.hg)(i.j2,e=>{e?n(e):u.push("/manage-photos/login"),d(!1)});return()=>e()},[u]),c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):r?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Redirecting to login..."})})})}var d=r(4295),u=r(6766);function h(e){let{isOpen:t,onClose:r,onSubmit:o,editData:s}=e,[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[h,x]=(0,l.useState)(""),[m,p]=(0,l.useState)(null);(0,l.useEffect)(()=>{t&&(d((null==s?void 0:s.name)||""),x((null==s?void 0:s.caption)||""),p((null==s?void 0:s.imageUrl)||null),n(null))},[t,s]);let g=()=>{n(null),d((null==s?void 0:s.name)||""),x((null==s?void 0:s.caption)||""),p((null==s?void 0:s.imageUrl)||null),r()};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:s?"Edit Photo":"Upload New Photo"}),(0,a.jsx)("button",{onClick:g,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),s)o({file:i,name:c,caption:h});else{if(!i)return void alert("Please select a file");o({file:i,name:c,caption:h})}n(null),d(""),x(""),p(null),r()},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:s?"Change Photo (optional)":"Select Photo"}),(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){n(r);let e=new FileReader;e.onload=e=>{var t;p(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(r)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100",required:!s})]}),m&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preview"}),(0,a.jsx)(u.default,{src:m,alt:"Preview",width:400,height:192,className:"w-full h-48 object-cover rounded-lg border"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Name"}),(0,a.jsx)("input",{type:"text",value:c,onChange:e=>d(e.target.value),className:"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"Enter product name..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Caption"}),(0,a.jsx)("textarea",{value:h,onChange:e=>x(e.target.value),rows:3,className:"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700",placeholder:"Enter a caption for this photo..."})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:g,className:"flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors",children:s?"Update":"Upload"})]})]})]})})}):null}function x(){let[e,t]=(0,l.useState)([]),[r,u]=(0,l.useState)(!0),[x,m]=(0,l.useState)(!1),[p,g]=(0,l.useState)(null),[f,b]=(0,l.useState)(!1),[y,A]=(0,l.useState)(1),[w,j]=(0,l.useState)(1),v=(0,o.useRouter)(),N=(0,l.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;try{u(!0);let{products:r,total:a}=await n.j.getPaginatedProducts(e,12);t(r),j(Math.ceil(a/12))}catch(e){console.error("Error fetching products:",e),alert("Failed to load products")}finally{u(!1)}},[y,12]);(0,l.useEffect)(()=>{N(y)},[y,N]);let C=async()=>{try{await (0,s.CI)(i.j2),v.push("/")}catch(e){console.error("Logout error:",e)}},E=async e=>{b(!0);try{await n.j.addProduct({name:e.name,caption:e.caption},e.file),await N(),m(!1),alert("Photo uploaded successfully!")}catch(e){console.error("Upload error:",e),alert("Failed to upload photo. Please try again.")}finally{b(!1)}},P=t=>{let r=e.find(e=>e.id===t);r&&(g(r),m(!0))},k=async e=>{if(p){b(!0);try{await n.j.updateProduct(p.id,{name:e.name,caption:e.caption},e.file),await N(),m(!1),g(null),alert("Photo updated successfully!")}catch(e){console.error("Update error:",e),alert("Failed to update photo. Please try again.")}finally{b(!1)}}},S=async e=>{if(confirm("Are you sure you want to delete this photo? This action cannot be undone."))try{await n.j.deleteProduct(e),await N(),alert("Photo deleted successfully!")}catch(e){console.error("Delete error:",e),alert("Failed to delete photo. Please try again.")}};return(0,a.jsx)(c,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Photo Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your product gallery"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>m(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Add Photo"}),(0,a.jsx)("button",{onClick:C,className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors",children:"Logout"})]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:[r?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading products..."})]}):0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("span",{className:"text-4xl",children:"\uD83D\uDCF7"})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"No Photos Yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Start building your product gallery by adding your first photo."}),(0,a.jsx)("button",{onClick:()=>m(!0),className:"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors",children:"Add First Photo"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,a.jsx)(d.A,{id:e.id,imageUrl:e.imageUrl,caption:e.caption,name:e.name,onEdit:P,onDelete:S,isAdmin:!0},e.id))}),w>1&&(0,a.jsxs)("div",{className:"mt-8 flex justify-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>A(e=>Math.max(e-1,1)),disabled:1===y,className:"px-4 py-2 rounded-md ".concat(1===y?"bg-gray-200 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:"Previous"}),(0,a.jsxs)("span",{className:"px-4 py-2 text-gray-700",children:["Page ",y," of ",w]}),(0,a.jsx)("button",{onClick:()=>A(e=>Math.min(e+1,w)),disabled:y>=w,className:"px-4 py-2 rounded-md ".concat(y>=w?"bg-gray-200 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:"Next"})]})]}),(0,a.jsx)(h,{isOpen:x,onClose:()=>{m(!1),setTimeout(()=>{g(null)},300)},onSubmit:e=>{p?k(e):E(e)},editData:p?{id:p.id,name:p.name,caption:p.caption,imageUrl:p.imageUrl}:null}),f&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-700",children:p?"Updating photo...":"Uploading photo..."})]})})]})})}}},e=>{e.O(0,[595,965,274,852,766,441,964,358],()=>e(e.s=9182)),_N_E=e.O()}]);