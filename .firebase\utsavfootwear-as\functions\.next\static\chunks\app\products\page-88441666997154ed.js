(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{3153:(e,t,r)=>{Promise.resolve().then(r.bind(r,9457))},3584:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(5155),o=r(8274);function s(e){let{children:t,className:r="",delay:s=0,direction:l="up",duration:i=.6,disableScrollAnimation:n=!1}=e,c=()=>{switch(l){case"up":case"down":default:return{opacity:1,y:0};case"left":case"right":return{opacity:1,x:0}}};return(0,a.jsx)(o.P.div,{className:r,initial:(()=>{switch(l){case"up":default:return{opacity:0,y:50};case"down":return{opacity:0,y:-50};case"left":return{opacity:0,x:50};case"right":return{opacity:0,x:-50}}})(),animate:n?c():void 0,whileInView:n?void 0:c(),viewport:n?void 0:{once:!0,amount:.3},transition:{duration:i,delay:s,ease:"easeOut"},children:t})}},4295:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(5155),o=r(6766),s=r(8274),l=r(2115);function i(e){let{id:t,imageUrl:r,caption:i,name:n,onEdit:c,onDelete:d,isAdmin:u=!1}=e,[h,x]=(0,l.useState)(!1),[m,p]=(0,l.useState)(!0),[g,f]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=()=>{f(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.jsxs)(s.P.div,{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",initial:{opacity:1,y:0},whileInView:{opacity:1,y:0},viewport:{once:!0,amount:.3},transition:{duration:0},whileHover:g?{}:{y:-5,transition:{duration:.2}},children:[(0,a.jsxs)("div",{className:"relative w-full h-0 pb-[100%] overflow-hidden bg-gray-100",children:[m&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}),h?(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCF7"}),(0,a.jsx)("div",{className:"text-sm",children:"Image not available"})]})}):(0,a.jsx)(s.P.div,{whileHover:g?{}:{scale:1.05},transition:{duration:.3},className:"absolute inset-0 w-full h-full",children:(0,a.jsx)(o.default,{src:r,alt:i,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",priority:!1,quality:g?75:85,placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",loading:g?"eager":"lazy",onLoad:()=>{p(!1),console.log("Image loaded successfully: ".concat(r))},onError:e=>{console.error("Image failed to load: ".concat(r),e),x(!0),p(!1)}})})]}),(0,a.jsxs)("div",{className:"p-4",children:[n&&(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 text-lg",children:n}),(0,a.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:i}),u&&(c||d)&&(0,a.jsxs)("div",{className:"mt-4 flex gap-2",children:[c&&(0,a.jsx)(s.P.button,{onClick:()=>c(t),className:"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Edit"}),d&&(0,a.jsx)(s.P.button,{onClick:()=>d(t),className:"px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Delete"})]})]})]})}},6104:(e,t,r)=>{"use strict";r.d(t,{OO:()=>c,j2:()=>n});var a=r(3915),o=r(5404),s=r(1115),l=r(7505);let i=(0,a.Wp)({apiKey:"AIzaSyBsEcUNPz8nDZyGRNU2bh1DpXPxnyV3b88",authDomain:"utsavfootwear-as.firebaseapp.com",databaseURL:"https://utsavfootwear-as-default-rtdb.firebaseio.com",projectId:"utsavfootwear-as",storageBucket:"utsavfootwear-as.firebasestorage.app",messagingSenderId:"74148351385",appId:"1:74148351385:web:ff9c1a3cff4395e3d19708",measurementId:"G-YW880XL7ME"}),n=(0,o.xI)(i),c=(0,s.C3)(i);(0,l.c7)(i)},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let a=r(2115);function o(e,t){let r=(0,a.useRef)(null),o=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=s(e,a)),t&&(o.current=s(t,a))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7827:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var a=r(6104),o=r(1115);async function s(e){try{let t=new FormData;t.append("file",e);let r=await fetch("/api/uploadToCloudinary",{method:"POST",body:t});if(!r.ok)throw Error("Failed to upload image to Cloudinary");let a=await r.json();return{url:a.url,publicId:a.public_id}}catch(e){throw console.error("Error uploading to Cloudinary:",e),Error("Failed to upload image")}}async function l(e){try{if(!(await fetch("/api/deleteFromCloudinary",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({publicId:e})})).ok)throw Error("Failed to delete image from Cloudinary")}catch(e){throw console.error("Error deleting from Cloudinary:",e),Error("Failed to delete image")}}class i{async getAllProducts(){try{let e=await (0,o.Jt)(this.productsRef);if(e.exists()){let t=e.val(),r=[];return Object.keys(t).forEach(e=>{r.push({id:e,...t[e]})}),r}return[]}catch(e){throw console.error("Error fetching products:",e),Error("Failed to fetch products")}}async getPaginatedProducts(e,t){try{let r=await (0,o.Jt)(this.productsRef);if(r.exists()){let a=r.val(),o=[];Object.keys(a).forEach(e=>{o.push({id:e,...a[e]})});let s=(o=o.sort((e,t)=>{let r=e.updatedAt||e.createdAt||0;return(t.updatedAt||t.createdAt||0)-r})).length,l=(e-1)*t;return{products:o.slice(l,l+t),total:s}}return{products:[],total:0}}catch(e){throw console.error("Error fetching products:",e),Error("Failed to fetch products")}}async getProductById(e){try{let t=(0,o.KR)(a.OO,"products/".concat(e)),r=await (0,o.Jt)(t);if(r.exists())return{id:e,...r.val()};return null}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}async addProduct(e,t){try{let{url:r,publicId:a}=await s(t),l=Date.now(),i={...e,imageUrl:r,publicId:a,createdAt:l,updatedAt:l},n=(0,o.VC)(this.productsRef);return await (0,o.hZ)(n,i),n.key}catch(e){throw console.error("Error adding product:",e),Error("Failed to add product")}}async updateProduct(e,t,r){try{let i=(0,o.KR)(a.OO,"products/".concat(e)),n=await this.getProductById(e);if(!n)throw Error("Product not found");let c=n.imageUrl,d=n.publicId;if(r){let{url:e,publicId:t}=await s(r);if(c=e,n.publicId)try{await l(n.publicId)}catch(e){console.error("Error deleting old image:",e)}d=t}let u={...n,...t,imageUrl:c,publicId:d,updatedAt:Date.now()};await (0,o.yo)(i,u)}catch(e){throw console.error("Error updating product:",e),Error("Failed to update product")}}async deleteProduct(e){try{let t=await this.getProductById(e);if(!t)throw Error("Product not found");if(t.publicId)try{await l(t.publicId)}catch(e){console.error("Error deleting image from Cloudinary:",e)}let r=(0,o.KR)(a.OO,"products/".concat(e));await (0,o.TF)(r)}catch(e){throw console.error("Error deleting product:",e),Error("Failed to delete product")}}async searchProducts(e){try{let t=await this.getAllProducts(),r=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(r)||e.caption.toLowerCase().includes(r))}catch(e){throw console.error("Error searching products:",e),Error("Failed to search products")}}constructor(){this.productsRef=(0,o.KR)(a.OO,"products")}}let n=new i},8932:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5155),o=r(8274);let s={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},l={type:"tween",ease:"easeInOut",duration:.5};function i(e){let{children:t,className:r=""}=e;return(0,a.jsx)(o.P.div,{className:r,initial:"initial",animate:"in",exit:"out",variants:s,transition:l,children:t})}},9457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),o=r(2115),s=r(7827),l=r(4295),i=r(8932),n=r(3584),c=r(8274);function d(e){let{size:t="md",color:r="text-blue-600",text:o="Loading..."}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8",children:[(0,a.jsx)(c.P.div,{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," ").concat(r," mb-4"),animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,a.jsxs)("svg",{className:"w-full h-full",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),(0,a.jsx)(c.P.p,{className:"".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[t]," ").concat(r," font-medium"),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:o})]})}let u=[12,24,48,96];function h(){let[e,t]=(0,o.useState)([]),[r,c]=(0,o.useState)(!0),[h,x]=(0,o.useState)(null),[m,p]=(0,o.useState)(1),[g,f]=(0,o.useState)(12),[y,A]=(0,o.useState)(1),b=(0,o.useCallback)(async()=>{try{c(!0),x(null);let{products:e,total:r}=await s.j.getPaginatedProducts(m,g);t(e),A(Math.ceil(r/g))}catch(e){console.error("Error fetching products:",e),x("Failed to load products. Please try again later.")}finally{c(!1)}},[m,g]);return((0,o.useEffect)(()=>{b()},[b]),(0,o.useEffect)(()=>{let e=e=>{"ArrowLeft"===e.key&&m>1?p(e=>e-1):"ArrowRight"===e.key&&m<y&&p(e=>e+1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[m,y]),r)?(0,a.jsx)(i.default,{className:"min-h-screen py-12",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)(d,{size:"lg",text:"Loading products..."})})}):h?(0,a.jsx)("div",{className:"min-h-screen py-12",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:h}),(0,a.jsx)("button",{onClick:b,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors",children:"Try Again"})]})})}):(0,a.jsxs)(i.default,{className:"min-h-screen py-12 relative bg-[url('/KCbg1.png')] bg-cover bg-center bg-no-repeat py-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/40 z-0"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ",children:[(0,a.jsxs)(n.default,{className:"text-center mb-12",disableScrollAnimation:!0,children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Our Products"}),(0,a.jsxs)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:["Discover our kolahpuri chappal collection and quality footwear's.",(0,a.jsx)("br",{}),"Each pair is selected for comfort, style, and durability."]})]}),0===e.length?(0,a.jsxs)(n.default,{className:"text-center py-16",disableScrollAnimation:!0,children:[(0,a.jsx)("div",{className:"bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 transform transition-transform duration-300 hover:scale-110",children:(0,a.jsx)("span",{className:"text-4xl",children:"\uD83D\uDC5F"})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold text-white mb-4",children:"No Products Yet"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-md mx-auto",children:"We're currently updating our product gallery. Please check back soon to see our latest collection of quality footwear."})]}):(0,a.jsxs)(n.default,{disableScrollAnimation:!0,children:[(0,a.jsx)("div",{className:"flex justify-end mb-4",children:(0,a.jsx)("select",{value:g,onChange:e=>{f(Number(e.target.value)),p(1)},className:"bg-white text-gray-700 px-4 py-2 rounded-lg shadow border border-gray-200",children:u.map(e=>(0,a.jsxs)("option",{value:e,children:["Show ",e," per page"]},e))})}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,a.jsx)(l.A,{id:e.id,imageUrl:e.imageUrl,caption:e.caption,name:e.name,isAdmin:!1},e.id))}),y>1&&(0,a.jsxs)("div",{className:"mt-8 flex justify-center items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>p(e=>Math.max(e-1,1)),disabled:1===m,className:"px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors","aria-label":"Previous page",children:"←"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:Array.from({length:y},(e,t)=>t+1).filter(e=>1===e||e===y||1>=Math.abs(e-m)).map((e,t,r)=>(0,a.jsxs)(o.Fragment,{children:[t>0&&r[t-1]!==e-1&&(0,a.jsx)("span",{className:"text-white",children:"..."}),(0,a.jsx)("button",{onClick:()=>p(e),className:"w-10 h-10 rounded-lg flex items-center justify-center ".concat(m===e?"bg-blue-500 text-white":"bg-white text-gray-700 hover:bg-gray-50"," border border-gray-200 shadow transition-colors"),children:e})]},e))}),(0,a.jsx)("button",{onClick:()=>p(e=>Math.min(e+1,y)),disabled:m===y,className:"px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors","aria-label":"Next page",children:"→"})]}),y>1&&(0,a.jsx)("div",{className:"mt-4 text-center text-white text-sm",children:"Use ← and → arrow keys to navigate between pages"})]}),(0,a.jsx)(n.default,{className:"mt-16 text-center",delay:.3,disableScrollAnimation:!0,children:(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-8 transform transition-transform duration-300 hover:scale-105",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Interested in Our Products?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Visit our store to see our full collection, try on different styles, and get personalized fitting assistance from our knowledgeable staff."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"/about",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:"Store Locations"}),(0,a.jsx)("a",{href:"/contact",className:"border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:"Contact Us"})]})]})})]})]})}}},e=>{e.O(0,[595,965,274,852,766,441,964,358],()=>e(e.s=3153)),_N_E=e.O()}]);