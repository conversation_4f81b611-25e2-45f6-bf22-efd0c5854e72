{"version": 3, "file": "index.cjs.js", "sources": ["../src/constants.ts", "../src/logger.ts", "../src/errors.ts", "../src/helpers.ts", "../src/get-config.ts", "../src/functions.ts", "../src/initialize-analytics.ts", "../src/factory.ts", "../src/api.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Analytics.\n */\nexport const ANALYTICS_TYPE = 'analytics';\n\n// Key to attach FID to in gtag params.\nexport const GA_FID_KEY = 'firebase_id';\nexport const ORIGIN_KEY = 'origin';\n\nexport const FETCH_TIMEOUT_MILLIS = 60 * 1000;\n\nexport const DYNAMIC_CONFIG_URL =\n  'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\n\nexport const GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\nexport const enum GtagCommand {\n  EVENT = 'event',\n  SET = 'set',\n  CONFIG = 'config',\n  CONSENT = 'consent',\n  GET = 'get'\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/analytics');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AnalyticsError {\n  ALREADY_EXISTS = 'already-exists',\n  ALREADY_INITIALIZED = 'already-initialized',\n  ALREADY_INITIALIZED_SETTINGS = 'already-initialized-settings',\n  INTEROP_COMPONENT_REG_FAILED = 'interop-component-reg-failed',\n  INVALID_ANALYTICS_CONTEXT = 'invalid-analytics-context',\n  INDEXEDDB_UNAVAILABLE = 'indexeddb-unavailable',\n  FETCH_THROTTLE = 'fetch-throttle',\n  CONFIG_FETCH_FAILED = 'config-fetch-failed',\n  NO_API_KEY = 'no-api-key',\n  NO_APP_ID = 'no-app-id',\n  NO_CLIENT_ID = 'no-client-id',\n  INVALID_GTAG_RESOURCE = 'invalid-gtag-resource'\n}\n\nconst ERRORS: ErrorMap<AnalyticsError> = {\n  [AnalyticsError.ALREADY_EXISTS]:\n    'A Firebase Analytics instance with the appId {$id} ' +\n    ' already exists. ' +\n    'Only one Firebase Analytics instance can be created for each appId.',\n  [AnalyticsError.ALREADY_INITIALIZED]:\n    'initializeAnalytics() cannot be called again with different options than those ' +\n    'it was initially called with. It can be called again with the same options to ' +\n    'return the existing instance, or getAnalytics() can be used ' +\n    'to get a reference to the already-initialized instance.',\n  [AnalyticsError.ALREADY_INITIALIZED_SETTINGS]:\n    'Firebase Analytics has already been initialized.' +\n    'settings() must be called before initializing any Analytics instance' +\n    'or it will have no effect.',\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]:\n    'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]:\n    'Firebase Analytics is not supported in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]:\n    'IndexedDB unavailable or restricted in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [AnalyticsError.CONFIG_FETCH_FAILED]:\n    'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [AnalyticsError.NO_API_KEY]:\n    'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid API key.',\n  [AnalyticsError.NO_APP_ID]:\n    'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid app ID.',\n  [AnalyticsError.NO_CLIENT_ID]: 'The \"client_id\" field is empty.',\n  [AnalyticsError.INVALID_GTAG_RESOURCE]:\n    'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\n\ninterface ErrorParams {\n  [AnalyticsError.ALREADY_EXISTS]: { id: string };\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]: { reason: Error };\n  [AnalyticsError.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [AnalyticsError.CONFIG_FETCH_FAILED]: {\n    httpStatus: number;\n    responseMessage: string;\n  };\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]: { errorInfo: string };\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]: { errorInfo: string };\n  [AnalyticsError.INVALID_GTAG_RESOURCE]: { gtagURL: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AnalyticsError, ErrorParams>(\n  'analytics',\n  'Analytics',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { DynamicConfig, DataLayer, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GTAG_URL } from './constants';\nimport { logger } from './logger';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n// Possible parameter types for gtag 'event' and 'config' commands\ntype GtagConfigOrEventParams = ControlParams & EventParams & CustomParams;\n\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nexport function createGtagTrustedTypesScriptURL(url: string): string {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_GTAG_RESOURCE, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nexport function promiseAllSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<T[]> {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nexport function createTrustedTypesPolicy(\n  policyName: string,\n  policyOptions: Partial<TrustedTypePolicyOptions>\n): Partial<TrustedTypePolicy> | undefined {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy: Partial<TrustedTypePolicy> | undefined;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(\n      policyName,\n      policyOptions\n    );\n  }\n  return trustedTypesPolicy;\n}\n\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function insertScriptTag(\n  dataLayerName: string,\n  measurementId: string\n): void {\n  const trustedTypesPolicy = createTrustedTypesPolicy(\n    'firebase-js-sdk-policy',\n    {\n      createScriptURL: createGtagTrustedTypesScriptURL\n    }\n  );\n\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  (script.src as string | TrustedScriptURL) = trustedTypesPolicy\n    ? (trustedTypesPolicy as TrustedTypePolicy)?.createScriptURL(gtagScriptURL)\n    : gtagScriptURL;\n\n  script.async = true;\n  document.head.appendChild(script);\n}\n\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function getOrCreateDataLayer(dataLayerName: string): DataLayer {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer: DataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName] as DataLayer;\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId as string];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      const foundConfig = dynamicConfigResults.find(\n        config => config.measurementId === measurementId\n      );\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(GtagCommand.CONFIG, measurementId, gtagParams);\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  try {\n    let initializationPromisesToWaitFor: Array<Promise<string>> = [];\n\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList: string | string[] = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(\n          config => config.measurementId === sendToId\n        );\n        const initializationPromise =\n          foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      /* eslint-disable-next-line @typescript-eslint/no-floating-promises */\n      initializationPromisesToWaitFor = Object.values(\n        initializationPromisesMap\n      );\n    }\n\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(GtagCommand.EVENT, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(\n  gtagCore: Gtag,\n  /**\n   * Allows wrapped gtag calls to wait on whichever initialization promises are required,\n   * depending on the contents of the gtag params' `send_to` field, if any.\n   */\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  /**\n   * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n   * before determining what initialization promises (which include FIDs) to wait for.\n   */\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  /**\n   * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n   * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n   * which is the key for the initialization promises map.\n   */\n  measurementIdToAppId: { [measurementId: string]: string }\n): Gtag {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(\n    command: 'config' | 'set' | 'event' | 'consent' | 'get' | string,\n    ...args: unknown[]\n  ): Promise<void> {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === GtagCommand.EVENT) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONFIG) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementIdToAppId,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONSENT) {\n        const [consentAction, gtagParams] = args;\n        // consentAction can be one of 'default' or 'update'.\n        gtagCore(\n          GtagCommand.CONSENT,\n          consentAction,\n          gtagParams as ConsentSettings\n        );\n      } else if (command === GtagCommand.GET) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\n          GtagCommand.GET,\n          measurementId as string,\n          fieldName as string,\n          callback as (...args: unknown[]) => void\n        );\n      } else if (command === GtagCommand.SET) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(GtagCommand.SET, customParams as CustomParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper as Gtag;\n}\n\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nexport function wrapOrCreateGtag(\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  dataLayerName: string,\n  gtagFunctionName: string\n): {\n  gtagCore: Gtag;\n  wrappedGtag: Gtag;\n} {\n  // Create a basic core gtag function\n  let gtagCore: Gtag = function (..._args: unknown[]) {\n    // Must push IArguments object, not an array.\n    (window[dataLayerName] as DataLayer).push(arguments);\n  };\n\n  // Replace it with existing one if found\n  if (\n    window[gtagFunctionName] &&\n    typeof window[gtagFunctionName] === 'function'\n  ) {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n\n  window[gtagFunctionName] = wrapGtag(\n    gtagCore,\n    initializationPromisesMap,\n    dynamicConfigPromisesList,\n    measurementIdToAppId\n  );\n\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName] as Gtag\n  };\n}\n\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nexport function findGtagScriptOnPage(\n  dataLayerName: string\n): HTMLScriptElement | null {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (\n      tag.src &&\n      tag.src.includes(GTAG_URL) &&\n      tag.src.includes(dataLayerName)\n    ) {\n      return tag;\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Most logic is copied from packages/remote-config/src/client/retrying_client.ts\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { DynamicConfig, ThrottleMetadata, MinimalDynamicConfig } from './types';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { DYNAMIC_CONFIG_URL, FETCH_TIMEOUT_MILLIS } from './constants';\nimport { logger } from './logger';\n\n// App config fields needed by analytics.\nexport interface AppFields {\n  appId: string;\n  apiKey: string;\n  measurementId?: string;\n}\n\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nexport const LONG_RETRY_FACTOR = 30;\n\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(\n    public throttleMetadata: { [appId: string]: ThrottleMetadata } = {},\n    public intervalMillis: number = BASE_INTERVAL_MILLIS\n  ) {}\n\n  getThrottleMetadata(appId: string): ThrottleMetadata {\n    return this.throttleMetadata[appId];\n  }\n\n  setThrottleMetadata(appId: string, metadata: ThrottleMetadata): void {\n    this.throttleMetadata[appId] = metadata;\n  }\n\n  deleteThrottleMetadata(appId: string): void {\n    delete this.throttleMetadata[appId];\n  }\n}\n\nconst defaultRetryData = new RetryData();\n\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey: string): Headers {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfig(\n  appFields: AppFields\n): Promise<DynamicConfig> {\n  const { appId, apiKey } = appFields;\n  const request: RequestInit = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = (await response.json()) as {\n        error?: { message?: string };\n      };\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(AnalyticsError.CONFIG_FETCH_FAILED, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfigWithRetry(\n  app: FirebaseApp,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData: RetryData = defaultRetryData,\n  timeoutMillis?: number\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, apiKey, measurementId } = app.options;\n\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n  }\n\n  const throttleMetadata: ThrottleMetadata = retryData.getThrottleMetadata(\n    appId\n  ) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n\n  const signal = new AnalyticsAbortSignal();\n\n  setTimeout(\n    async () => {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    },\n    timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS\n  );\n\n  return attemptFetchDynamicConfigWithRetry(\n    { appId, apiKey, measurementId },\n    throttleMetadata,\n    signal,\n    retryData\n  );\n}\n\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(\n  appFields: AppFields,\n  { throttleEndTimeMillis, backoffCount }: ThrottleMetadata,\n  signal: AnalyticsAbortSignal,\n  retryData: RetryData = defaultRetryData // for testing\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, measurementId } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(\n        `Timed out fetching this Firebase app's measurement ID from the server.` +\n          ` Falling back to the measurement ID ${measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config. [${\n            (e as Error)?.message\n          }]`\n      );\n      return { appId, measurementId };\n    }\n    throw e;\n  }\n\n  try {\n    const response = await fetchDynamicConfig(appFields);\n\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n\n    return response;\n  } catch (e) {\n    const error = e as Error;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(\n          `Failed to fetch this Firebase app's measurement ID from the server.` +\n            ` Falling back to the measurement ID ${measurementId}` +\n            ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`\n        );\n        return { appId, measurementId };\n      } else {\n        throw e;\n      }\n    }\n\n    const backoffMillis =\n      Number(error?.customData?.httpStatus) === 503\n        ? calculateBackoffMillis(\n            backoffCount,\n            retryData.intervalMillis,\n            LONG_RETRY_FACTOR\n          )\n        : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n\n    return attemptFetchDynamicConfigWithRetry(\n      appFields,\n      throttleMetadata,\n      signal,\n      retryData\n    );\n  }\n}\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(\n  signal: AnalyticsAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(AnalyticsError.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class AnalyticsAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { Gtag } from './types';\nimport { GtagCommand } from './constants';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nexport let defaultEventParametersForInit: CustomParams | undefined;\n\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nexport async function logEvent(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.EVENT, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params: EventParams | ControlParams = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(GtagCommand.EVENT, eventName, params);\n  }\n}\n\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nexport async function setCurrentScreen(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  screenName: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'screen_name': screenName });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nexport async function setUserId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'user_id': id });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nexport async function setUserProperties(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    const flatProperties: { [key: string]: unknown } = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(GtagCommand.SET, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nexport async function internalGetGoogleAnalyticsClientId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>\n): Promise<string> {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\n      GtagCommand.GET,\n      measurementId,\n      'client_id',\n      (clientId: string) => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(AnalyticsError.NO_CLIENT_ID));\n        }\n        resolve(clientId);\n      }\n    );\n  });\n}\n\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nexport async function setAnalyticsCollectionEnabled(\n  initializationPromise: Promise<string>,\n  enabled: boolean\n): Promise<void> {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nexport let defaultConsentSettingsForInit: ConsentSettings | undefined;\n\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nexport function _setConsentDefaultForInit(\n  consentSettings?: ConsentSettings\n): void {\n  defaultConsentSettingsForInit = consentSettings;\n}\n\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nexport function _setDefaultEventParametersForInit(\n  customParams?: CustomParams\n): void {\n  defaultEventParametersForInit = customParams;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DynamicConfig, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GA_FID_KEY, ORIGIN_KEY } from './constants';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { fetchDynamicConfigWithRetry } from './get-config';\nimport { logger } from './logger';\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { findGtagScriptOnPage, insertScriptTag } from './helpers';\nimport { AnalyticsSettings } from './public-types';\nimport {\n  defaultConsentSettingsForInit,\n  _setConsentDefaultForInit,\n  defaultEventParametersForInit,\n  _setDefaultEventParametersForInit\n} from './functions';\n\nasync function validateIndexedDB(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(\n      ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message\n    );\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(\n        ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n          errorInfo: (e as Error)?.toString()\n        }).message\n      );\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nexport async function _initializeAnalytics(\n  app: FirebaseApp,\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [key: string]: string },\n  installations: _FirebaseInstallationsInternal,\n  gtagCore: Gtag,\n  dataLayerName: string,\n  options?: AnalyticsSettings\n): Promise<string> {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise\n    .then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (\n        app.options.measurementId &&\n        config.measurementId !== app.options.measurementId\n      ) {\n        logger.warn(\n          `The measurement ID in the local Firebase config (${app.options.measurementId})` +\n            ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\n            ` To ensure analytics events are always sent to the correct Analytics property,` +\n            ` update the` +\n            ` measurement ID field in the local config or remove it from the local config.`\n        );\n      }\n    })\n    .catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n\n  const fidPromise: Promise<string | undefined> = validateIndexedDB().then(\n    envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    }\n  );\n\n  const [dynamicConfig, fid] = await Promise.all([\n    dynamicConfigPromise,\n    fidPromise\n  ]);\n\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(GtagCommand.CONSENT, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (gtagCore as any)('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties: Record<string, unknown> = options?.config ?? {};\n\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(GtagCommand.CONFIG, dynamicConfig.measurementId, configProperties);\n\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(GtagCommand.SET, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n\n  return dynamicConfig.measurementId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsOptions, Analytics, AnalyticsSettings } from './public-types';\nimport { Gtag, DynamicConfig, MinimalDynamicConfig } from './types';\nimport { getOrCreateDataLayer, wrapOrCreateGtag } from './helpers';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { areCookiesEnabled, isBrowserExtension } from '@firebase/util';\nimport { _initializeAnalytics } from './initialize-analytics';\nimport { logger } from './logger';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\n\n/**\n * Analytics Service class.\n */\nexport class AnalyticsService implements Analytics, _FirebaseService {\n  constructor(public app: FirebaseApp) {}\n  _delete(): Promise<void> {\n    delete initializationPromisesMap[this.app.options.appId!];\n    return Promise.resolve();\n  }\n}\n\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nexport let initializationPromisesMap: {\n  [appId: string]: Promise<string>; // Promise contains measurement ID string.\n} = {};\n\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList: Array<\n  Promise<DynamicConfig | MinimalDynamicConfig>\n> = [];\n\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId: { [measurementId: string]: string } = {};\n\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName: string = 'dataLayer';\n\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName: string = 'gtag';\n\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction: Gtag;\n\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nexport let wrappedGtagFunction: Gtag;\n\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone: boolean = false;\n\n/**\n * For testing\n * @internal\n */\nexport function resetGlobalVars(\n  newGlobalInitDone = false,\n  newInitializationPromisesMap = {},\n  newDynamicPromises = []\n): void {\n  globalInitDone = newGlobalInitDone;\n  initializationPromisesMap = newInitializationPromisesMap;\n  dynamicConfigPromisesList = newDynamicPromises;\n  dataLayerName = 'dataLayer';\n  gtagName = 'gtag';\n}\n\n/**\n * For testing\n * @internal\n */\nexport function getGlobalVars(): {\n  initializationPromisesMap: { [appId: string]: Promise<string> };\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >;\n} {\n  return {\n    initializationPromisesMap,\n    dynamicConfigPromisesList\n  };\n}\n\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nexport function settings(options: SettingsOptions): void {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch(): void {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages\n      .map((message, index) => `(${index + 1}) ${message}`)\n      .join(' ');\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_ANALYTICS_CONTEXT, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n\n/**\n * Analytics instance factory.\n * @internal\n */\nexport function factory(\n  app: FirebaseApp,\n  installations: _FirebaseInstallationsInternal,\n  options?: AnalyticsSettings\n): AnalyticsService {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(\n        `The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\n          ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config.`\n      );\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_EXISTS, {\n      id: appId\n    });\n  }\n\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n\n    getOrCreateDataLayer(dataLayerName);\n\n    const { wrappedGtag, gtagCore } = wrapOrCreateGtag(\n      initializationPromisesMap,\n      dynamicConfigPromisesList,\n      measurementIdToAppId,\n      dataLayerName,\n      gtagName\n    );\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(\n    app,\n    dynamicConfigPromisesList,\n    measurementIdToAppId,\n    installations,\n    gtagCoreFunction,\n    dataLayerName,\n    options\n  );\n\n  const analyticsInstance: AnalyticsService = new AnalyticsService(app);\n\n  return analyticsInstance;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable camelcase */\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  Analytics,\n  AnalyticsCallOptions,\n  AnalyticsSettings,\n  ConsentSettings,\n  CustomParams,\n  EventNameString,\n  EventParams\n} from './public-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable,\n  areCookiesEnabled,\n  isBrowserExtension,\n  getModularInstance,\n  deepEqual\n} from '@firebase/util';\nimport { ANALYTICS_TYPE, GtagCommand } from './constants';\nimport {\n  AnalyticsService,\n  initializationPromisesMap,\n  wrappedGtagFunction\n} from './factory';\nimport { logger } from './logger';\nimport {\n  logEvent as internalLogEvent,\n  setCurrentScreen as internalSetCurrentScreen,\n  setUserId as internalSetUserId,\n  setUserProperties as internalSetUserProperties,\n  setAnalyticsCollectionEnabled as internalSetAnalyticsCollectionEnabled,\n  _setConsentDefaultForInit,\n  _setDefaultEventParametersForInit,\n  internalGetGoogleAnalyticsClientId\n} from './functions';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\n\nexport { settings } from './factory';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [ANALYTICS_TYPE]: AnalyticsService;\n  }\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getAnalytics(app: FirebaseApp = getApp()): Analytics {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n\n  return initializeAnalytics(app);\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function initializeAnalytics(\n  app: FirebaseApp,\n  options: AnalyticsSettings = {}\n): Analytics {\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({ options });\n  return analyticsInstance;\n}\n\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nexport async function isSupported(): Promise<boolean> {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nexport function setCurrentScreen(\n  analyticsInstance: Analytics,\n  screenName: string,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetCurrentScreen(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    screenName,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport async function getGoogleAnalyticsClientId(\n  analyticsInstance: Analytics\n): Promise<string> {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!]\n  );\n}\n\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nexport function setUserId(\n  analyticsInstance: Analytics,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    id,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nexport function setUserProperties(\n  analyticsInstance: Analytics,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserProperties(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    properties,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nexport function setAnalyticsCollectionEnabled(\n  analyticsInstance: Analytics,\n  enabled: boolean\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetAnalyticsCollectionEnabled(\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    enabled\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nexport function setDefaultEventParameters(customParams: CustomParams): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.SET, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_payment_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    payment_type?: EventParams['payment_type'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_shipping_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    shipping_tier?: EventParams['shipping_tier'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_to_cart' | 'add_to_wishlist' | 'remove_from_cart',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'begin_checkout',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'checkout_progress',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/exceptions\n * | Measure exceptions}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'exception',\n  eventParams?: {\n    description?: EventParams['description'];\n    fatal?: EventParams['fatal'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'generate_lead',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'login',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/views\n * | Page views}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'page_view',\n  eventParams?: {\n    page_title?: string;\n    page_location?: string;\n    page_path?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'purchase' | 'refund',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    transaction_id: EventParams['transaction_id'];\n    tax?: EventParams['tax'];\n    shipping?: EventParams['shipping'];\n    items?: EventParams['items'];\n    coupon?: EventParams['coupon'];\n    affiliation?: EventParams['affiliation'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See {@link https://firebase.google.com/docs/analytics/screenviews\n * | Track Screenviews}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'screen_view',\n  eventParams?: {\n    firebase_screen: EventParams['firebase_screen'];\n    firebase_screen_class: EventParams['firebase_screen_class'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'search' | 'view_search_results',\n  eventParams?: {\n    search_term?: EventParams['search_term'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_content',\n  eventParams?: {\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_item',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_promotion' | 'view_promotion',\n  eventParams?: {\n    items?: EventParams['items'];\n    promotion_id?: EventParams['promotion_id'];\n    promotion_name?: EventParams['promotion_name'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'set_checkout_option',\n  eventParams?: {\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'share',\n  eventParams?: {\n    method?: EventParams['method'];\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'sign_up',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'timing_complete',\n  eventParams?: {\n    name: string;\n    value: number;\n    event_category?: string;\n    event_label?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_cart' | 'view_item',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_item_list',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent<T extends string>(\n  analyticsInstance: Analytics,\n  eventName: CustomEventName<T>,\n  eventParams?: { [key: string]: any },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalLogEvent(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    eventName,\n    eventParams,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Any custom event name string not in the standard list of recommended\n * event names.\n * @public\n */\nexport type CustomEventName<T> = T extends EventNameString ? never : T;\n\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nexport function setConsent(consentSettings: ConsentSettings): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.CONSENT, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\n", "/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { FirebaseAnalyticsInternal } from '@firebase/analytics-interop-types';\nimport { factory } from './factory';\nimport { ANALYTICS_TYPE } from './constants';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { logEvent } from './api';\nimport { name, version } from '../package.json';\nimport { AnalyticsCallOptions } from './public-types';\nimport '@firebase/installations';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAnalytics(): void {\n  _registerComponent(\n    new Component(\n      ANALYTICS_TYPE,\n      (container, { options: analyticsOptions }: InstanceFactoryOptions) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const installations = container\n          .getProvider('installations-internal')\n          .getImmediate();\n\n        return factory(app, installations, analyticsOptions);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  _registerComponent(\n    new Component('analytics-internal', internalFactory, ComponentType.PRIVATE)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n\n  function internalFactory(\n    container: ComponentContainer\n  ): FirebaseAnalyticsInternal {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (\n          eventName: string,\n          eventParams?: { [key: string]: unknown },\n          options?: AnalyticsCallOptions\n        ) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(AnalyticsError.INTEROP_COMPONENT_REG_FAILED, {\n        reason: e as Error\n      });\n    }\n  }\n}\n\nregisterAnalytics();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": ["<PERSON><PERSON>", "ErrorFactory", "calculateBackoffMillis", "FirebaseError", "logEvent", "setCurrentScreen", "setUserId", "setUserProperties", "setAnalyticsCollectionEnabled", "isIndexedDBAvailable", "validateIndexedDBOpenable", "isBrowserExtension", "areCookiesEnabled", "app", "getApp", "getModularInstance", "_get<PERSON><PERSON><PERSON>", "deepEqual", "internalSetCurrentScreen", "internalSetUserId", "internalSetUserProperties", "internalSetAnalyticsCollectionEnabled", "internalLogEvent", "_registerComponent", "Component", "registerVersion"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAM,cAAc,GAAG,WAAW,CAAC;AAE1C;AACO,MAAM,UAAU,GAAG,aAAa,CAAC;AACjC,MAAM,UAAU,GAAG,QAAQ,CAAC;AAE5B,MAAM,oBAAoB,GAAG,EAAE,GAAG,IAAI,CAAC;AAEvC,MAAM,kBAAkB,GAC7B,4EAA4E,CAAC;AAExE,MAAM,QAAQ,GAAG,0CAA0C;;AC/BlE;;;;;;;;;;;;;;;AAeG;AAII,MAAM,MAAM,GAAG,IAAIA,eAAM,CAAC,qBAAqB,CAAC;;ACnBvD;;;;;;;;;;;;;;;AAeG;AAmBH,MAAM,MAAM,GAA6B;AACvC,IAAA,CAAA,gBAAA,uCACE,qDAAqD;QACrD,mBAAmB;QACnB,qEAAqE;AACvE,IAAA,CAAA,qBAAA,4CACE,iFAAiF;QACjF,gFAAgF;QAChF,8DAA8D;QAC9D,yDAAyD;AAC3D,IAAA,CAAA,8BAAA,qDACE,kDAAkD;QAClD,sEAAsE;QACtE,4BAA4B;AAC9B,IAAA,CAAA,8BAAA,qDACE,uEAAuE;AACzE,IAAA,CAAA,2BAAA,kDACE,2DAA2D;QAC3D,8DAA8D;QAC9D,8EAA8E;AAChF,IAAA,CAAA,uBAAA,8CACE,2DAA2D;QAC3D,8DAA8D;QAC9D,8EAA8E;AAChF,IAAA,CAAA,gBAAA,uCACE,2EAA2E;QAC3E,+FAA+F;AACjG,IAAA,CAAA,qBAAA,4CACE,iEAAiE;AACnE,IAAA,CAAA,YAAA,mCACE,qGAAqG;QACrG,0BAA0B;AAC5B,IAAA,CAAA,WAAA,kCACE,oGAAoG;QACpG,yBAAyB;AAC3B,IAAA,CAAA,cAAA,qCAA+B,iCAAiC;AAChE,IAAA,CAAA,uBAAA,8CACE,8DAA8D;CACjE,CAAC;AAeK,MAAM,aAAa,GAAG,IAAIC,iBAAY,CAC3C,WAAW,EACX,WAAW,EACX,MAAM,CACP;;AC3FD;;;;;;;;;;;;;;;AAeG;AAgBH;;AAEG;AACG,SAAU,+BAA+B,CAAC,GAAW,EAAA;IACzD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC7B,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;AACrE,YAAA,OAAO,EAAE,GAAG;AACb,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACzB,QAAA,OAAO,EAAE,CAAC;KACX;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;;AAKG;AACG,SAAU,iBAAiB,CAC/B,QAA2B,EAAA;IAE3B,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC;AAED;;;;;;AAMG;AACa,SAAA,wBAAwB,CACtC,UAAkB,EAClB,aAAgD,EAAA;;;AAIhD,IAAA,IAAI,kBAA0D,CAAC;AAC/D,IAAA,IAAI,MAAM,CAAC,YAAY,EAAE;QACvB,kBAAkB,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CACnD,UAAU,EACV,aAAa,CACd,CAAC;KACH;AACD,IAAA,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC7B,aAAqB,EACrB,aAAqB,EAAA;AAErB,IAAA,MAAM,kBAAkB,GAAG,wBAAwB,CACjD,wBAAwB,EACxB;AACE,QAAA,eAAe,EAAE,+BAA+B;AACjD,KAAA,CACF,CAAC;IAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;IAIhD,MAAM,aAAa,GAAG,CAAG,EAAA,QAAQ,MAAM,aAAa,CAAA,IAAA,EAAO,aAAa,CAAA,CAAE,CAAC;IAC1E,MAAM,CAAC,GAAiC,GAAG,kBAAkB;AAC5D,UAAG,kBAAwC,EAAE,eAAe,CAAC,aAAa,CAAC;UACzE,aAAa,CAAC;AAElB,IAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACpB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED;;;AAGG;AACG,SAAU,oBAAoB,CAAC,aAAqB,EAAA;;IAExD,IAAI,SAAS,GAAc,EAAE,CAAC;IAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;AACxC,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,CAAc,CAAC;KAChD;SAAM;AACL,QAAA,MAAM,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;KACnC;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;;;AASG;AACH,eAAe,YAAY,CACzB,QAAc,EACd,yBAA+D,EAC/D,yBAEC,EACD,oBAAyD,EACzD,aAAqB,EACrB,UAAuD,EAAA;;;AAIvD,IAAA,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,aAAuB,CAAC,CAAC;AACzE,IAAA,IAAI;QACF,IAAI,kBAAkB,EAAE;AACtB,YAAA,MAAM,yBAAyB,CAAC,kBAAkB,CAAC,CAAC;SACrD;aAAM;;;;;AAKL,YAAA,MAAM,oBAAoB,GAAG,MAAM,iBAAiB,CAClD,yBAAyB,CAC1B,CAAC;AACF,YAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAC3C,MAAM,IAAI,MAAM,CAAC,aAAa,KAAK,aAAa,CACjD,CAAC;YACF,IAAI,WAAW,EAAE;AACf,gBAAA,MAAM,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACpD;SACF;KACF;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACjB;AACD,IAAA,QAAQ,CAAqB,QAAA,2BAAA,aAAa,EAAE,UAAU,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;AAQG;AACH,eAAe,WAAW,CACxB,QAAc,EACd,yBAA+D,EAC/D,yBAEC,EACD,aAAqB,EACrB,UAAuD,EAAA;AAEvD,IAAA,IAAI;QACF,IAAI,+BAA+B,GAA2B,EAAE,CAAC;;;AAIjE,QAAA,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE;AACvC,YAAA,IAAI,YAAY,GAAsB,UAAU,CAAC,SAAS,CAAC,CAAC;;YAE5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAChC,gBAAA,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;aAC/B;;;AAGD,YAAA,MAAM,oBAAoB,GAAG,MAAM,iBAAiB,CAClD,yBAAyB,CAC1B,CAAC;AACF,YAAA,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE;;AAEnC,gBAAA,MAAM,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAC3C,MAAM,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,CAC5C,CAAC;gBACF,MAAM,qBAAqB,GACzB,WAAW,IAAI,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC9D,IAAI,qBAAqB,EAAE;AACzB,oBAAA,+BAA+B,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBAC7D;qBAAM;;;;oBAIL,+BAA+B,GAAG,EAAE,CAAC;oBACrC,MAAM;iBACP;aACF;SACF;;;;AAKD,QAAA,IAAI,+BAA+B,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEhD,YAAA,+BAA+B,GAAG,MAAM,CAAC,MAAM,CAC7C,yBAAyB,CAC1B,CAAC;SACH;;;AAID,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;;AAEnD,QAAA,QAAQ,kCAAoB,aAAa,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;KAC9D;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC;AAED;;;;;;;;AAQG;AACH,SAAS,QAAQ,CACf,QAAc;AACd;;;AAGG;AACH,yBAA+D;AAC/D;;;AAGG;AACH,yBAEC;AACD;;;;AAIG;AACH,oBAAyD,EAAA;AAEzD;;;;;AAKG;AACH,IAAA,eAAe,WAAW,CACxB,OAAgE,EAChE,GAAG,IAAe,EAAA;AAElB,QAAA,IAAI;;YAEF,IAAI,OAAO,KAAsB,OAAA,0BAAE;AACjC,gBAAA,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC;;AAEzC,gBAAA,MAAM,WAAW,CACf,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,aAAuB,EACvB,UAAqC,CACtC,CAAC;aACH;iBAAM,IAAI,OAAO,KAAuB,QAAA,2BAAE;AACzC,gBAAA,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC;;AAEzC,gBAAA,MAAM,YAAY,CAChB,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,aAAuB,EACvB,UAAqC,CACtC,CAAC;aACH;iBAAM,IAAI,OAAO,KAAwB,SAAA,4BAAE;AAC1C,gBAAA,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC;;AAEzC,gBAAA,QAAQ,CAEN,SAAA,4BAAA,aAAa,EACb,UAA6B,CAC9B,CAAC;aACH;iBAAM,IAAI,OAAO,KAAoB,KAAA,wBAAE;gBACtC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;AAClD,gBAAA,QAAQ,8BAEN,aAAuB,EACvB,SAAmB,EACnB,QAAwC,CACzC,CAAC;aACH;iBAAM,IAAI,OAAO,KAAoB,KAAA,wBAAE;AACtC,gBAAA,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;;gBAE5B,QAAQ,CAAA,KAAA,wBAAkB,YAA4B,CAAC,CAAC;aACzD;iBAAM;AACL,gBAAA,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;aAC5B;SACF;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACjB;KACF;AACD,IAAA,OAAO,WAAmB,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,gBAAgB,CAC9B,yBAA+D,EAC/D,yBAEC,EACD,oBAAyD,EACzD,aAAqB,EACrB,gBAAwB,EAAA;;AAMxB,IAAA,IAAI,QAAQ,GAAS,UAAU,GAAG,KAAgB,EAAA;;QAE/C,MAAM,CAAC,aAAa,CAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvD,KAAC,CAAC;;IAGF,IACE,MAAM,CAAC,gBAAgB,CAAC;AACxB,QAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAC9C;;AAEA,QAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;KACrC;AAED,IAAA,MAAM,CAAC,gBAAgB,CAAC,GAAG,QAAQ,CACjC,QAAQ,EACR,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,CACrB,CAAC;IAEF,OAAO;QACL,QAAQ;AACR,QAAA,WAAW,EAAE,MAAM,CAAC,gBAAgB,CAAS;KAC9C,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,oBAAoB,CAClC,aAAqB,EAAA;IAErB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAClE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC3C,IACE,GAAG,CAAC,GAAG;AACP,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC1B,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC/B;AACA,YAAA,OAAO,GAAG,CAAC;SACZ;KACF;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;ACrZA;;;;;;;;;;;;;;;AAeG;AAoBH;;;;;AAKG;AACI,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAEpC;;AAEG;AACH,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAElC;;AAEG;AACH,MAAM,SAAS,CAAA;AACb,IAAA,WAAA,CACS,gBAA0D,GAAA,EAAE,EAC5D,cAAA,GAAyB,oBAAoB,EAAA;QAD7C,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA4C;QAC5D,IAAc,CAAA,cAAA,GAAd,cAAc,CAA+B;KAClD;AAEJ,IAAA,mBAAmB,CAAC,KAAa,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACrC;IAED,mBAAmB,CAAC,KAAa,EAAE,QAA0B,EAAA;AAC3D,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;KACzC;AAED,IAAA,sBAAsB,CAAC,KAAa,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACrC;AACF,CAAA;AAED,MAAM,gBAAgB,GAAG,IAAI,SAAS,EAAE,CAAC;AAEzC;;;AAGG;AACH,SAAS,UAAU,CAAC,MAAc,EAAA;IAChC,OAAO,IAAI,OAAO,CAAC;AACjB,QAAA,MAAM,EAAE,kBAAkB;AAC1B,QAAA,gBAAgB,EAAE,MAAM;AACzB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACI,eAAe,kBAAkB,CACtC,SAAoB,EAAA;AAEpB,IAAA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;AACpC,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC;KAC5B,CAAC;IACF,MAAM,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9C,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QACtD,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI;;YAEF,MAAM,YAAY,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,CAE1C,CAAC;AACF,YAAA,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;AAC/B,gBAAA,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;aAC3C;SACF;AAAC,QAAA,OAAO,QAAQ,EAAE,GAAE;QACrB,MAAM,aAAa,CAAC,MAAM,CAAqC,qBAAA,2CAAA;YAC7D,UAAU,EAAE,QAAQ,CAAC,MAAM;AAC3B,YAAA,eAAe,EAAE,YAAY;AAC9B,SAAA,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,CAAC;AAED;;;AAGG;AACI,eAAe,2BAA2B,CAC/C,GAAgB;AAChB;AACA,SAAuB,GAAA,gBAAgB,EACvC,aAAsB,EAAA;IAEtB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,gCAA0B,CAAC;KACtD;IAED,IAAI,CAAC,MAAM,EAAE;QACX,IAAI,aAAa,EAAE;YACjB,OAAO;gBACL,aAAa;gBACb,KAAK;aACN,CAAC;SACH;AACD,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,iCAA2B,CAAC;KACvD;IAED,MAAM,gBAAgB,GAAqB,SAAS,CAAC,mBAAmB,CACtE,KAAK,CACN,IAAI;AACH,QAAA,YAAY,EAAE,CAAC;AACf,QAAA,qBAAqB,EAAE,IAAI,CAAC,GAAG,EAAE;KAClC,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;IAE1C,UAAU,CACR,YAAW;;QAET,MAAM,CAAC,KAAK,EAAE,CAAC;AACjB,KAAC,EACD,aAAa,KAAK,SAAS,GAAG,aAAa,GAAG,oBAAoB,CACnE,CAAC;AAEF,IAAA,OAAO,kCAAkC,CACvC,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,EAChC,gBAAgB,EAChB,MAAM,EACN,SAAS,CACV,CAAC;AACJ,CAAC;AAED;;;;;AAKG;AACH,eAAe,kCAAkC,CAC/C,SAAoB,EACpB,EAAE,qBAAqB,EAAE,YAAY,EAAoB,EACzD,MAA4B,EAC5B,SAAuB,GAAA,gBAAgB;;AAEvC,IAAA,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,SAAS,CAAC;;;;AAI3C,IAAA,IAAI;AACF,QAAA,MAAM,mBAAmB,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;KAC1D;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,aAAa,EAAE;YACjB,MAAM,CAAC,IAAI,CACT,CAAwE,sEAAA,CAAA;AACtE,gBAAA,CAAA,oCAAA,EAAuC,aAAa,CAAE,CAAA;AACtD,gBAAA,CAAA,sEAAA,EACG,CAAW,EAAE,OAChB,CAAA,CAAA,CAAG,CACN,CAAC;AACF,YAAA,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;SACjC;AACD,QAAA,MAAM,CAAC,CAAC;KACT;AAED,IAAA,IAAI;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC,CAAC;;AAGrD,QAAA,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAExC,QAAA,OAAO,QAAQ,CAAC;KACjB;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,KAAK,GAAG,CAAU,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,SAAS,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,aAAa,EAAE;gBACjB,MAAM,CAAC,IAAI,CACT,CAAqE,mEAAA,CAAA;AACnE,oBAAA,CAAA,oCAAA,EAAuC,aAAa,CAAE,CAAA;AACtD,oBAAA,CAAA,sEAAA,EAAyE,KAAK,EAAE,OAAO,CAAA,CAAA,CAAG,CAC7F,CAAC;AACF,gBAAA,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;aACjC;iBAAM;AACL,gBAAA,MAAM,CAAC,CAAC;aACT;SACF;QAED,MAAM,aAAa,GACjB,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,KAAK,GAAG;cACzCC,2BAAsB,CACpB,YAAY,EACZ,SAAS,CAAC,cAAc,EACxB,iBAAiB,CAClB;cACDA,2BAAsB,CAAC,YAAY,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;;AAGrE,QAAA,MAAM,gBAAgB,GAAG;AACvB,YAAA,qBAAqB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;YACjD,YAAY,EAAE,YAAY,GAAG,CAAC;SAC/B,CAAC;;AAGF,QAAA,SAAS,CAAC,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACvD,QAAA,MAAM,CAAC,KAAK,CAAC,iCAAiC,aAAa,CAAA,OAAA,CAAS,CAAC,CAAC;QAEtE,OAAO,kCAAkC,CACvC,SAAS,EACT,gBAAgB,EAChB,MAAM,EACN,SAAS,CACV,CAAC;KACH;AACH,CAAC;AAED;;;;;;;;;;;AAWG;AACH,SAAS,mBAAmB,CAC1B,MAA4B,EAC5B,qBAA6B,EAAA;IAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;;AAErC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtE,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;;AAGnD,QAAA,MAAM,CAAC,gBAAgB,CAAC,MAAK;YAC3B,YAAY,CAAC,OAAO,CAAC,CAAC;;AAEtB,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAAgC,gBAAA,sCAAA;gBAClD,qBAAqB;AACtB,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAID;;AAEG;AACH,SAAS,gBAAgB,CAAC,CAAQ,EAAA;AAChC,IAAA,IAAI,EAAE,CAAC,YAAYC,kBAAa,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE;AAClD,QAAA,OAAO,KAAK,CAAC;KACd;;IAGD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;IAEtD,QACE,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;AAClB,QAAA,UAAU,KAAK,GAAG;QAClB,UAAU,KAAK,GAAG,EAClB;AACJ,CAAC;AAED;;;;;;;AAOG;MACU,oBAAoB,CAAA;AAAjC,IAAA,WAAA,GAAA;QACE,IAAS,CAAA,SAAA,GAAsB,EAAE,CAAC;KAOnC;AANC,IAAA,gBAAgB,CAAC,QAAoB,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;IACD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC;KAChD;AACF;;AClUD;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACI,IAAI,6BAAuD,CAAC;AAEnE;;;;;;AAMG;AACI,eAAeC,UAAQ,CAC5B,YAAkB,EAClB,qBAAsC,EACtC,SAAiB,EACjB,WAAyB,EACzB,OAA8B,EAAA;AAE9B,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;AAC7B,QAAA,YAAY,CAAoB,OAAA,0BAAA,SAAS,EAAE,WAAW,CAAC,CAAC;QACxD,OAAO;KACR;SAAM;AACL,QAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;AAClD,QAAA,MAAM,MAAM,GAAgC;AAC1C,YAAA,GAAG,WAAW;AACd,YAAA,SAAS,EAAE,aAAa;SACzB,CAAC;AACF,QAAA,YAAY,CAAoB,OAAA,0BAAA,SAAS,EAAE,MAAM,CAAC,CAAC;KACpD;AACH,CAAC;AAED;;;;;;;;AAQG;AACI,eAAeC,kBAAgB,CACpC,YAAkB,EAClB,qBAAsC,EACtC,UAAyB,EACzB,OAA8B,EAAA;AAE9B,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;AAC7B,QAAA,YAAY,8BAAkB,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;AAC7D,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;SAAM;AACL,QAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;QAClD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,YAAA,MAAM,EAAE,IAAI;AACZ,YAAA,aAAa,EAAE,UAAU;AAC1B,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;AAKG;AACI,eAAeC,WAAS,CAC7B,YAAkB,EAClB,qBAAsC,EACtC,EAAiB,EACjB,OAA8B,EAAA;AAE9B,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;AAC7B,QAAA,YAAY,8BAAkB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;SAAM;AACL,QAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;QAClD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,YAAA,MAAM,EAAE,IAAI;AACZ,YAAA,SAAS,EAAE,EAAE;AACd,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;AAKG;AACI,eAAeC,mBAAiB,CACrC,YAAkB,EAClB,qBAAsC,EACtC,UAAwB,EACxB,OAA8B,EAAA;AAE9B,IAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;QAC7B,MAAM,cAAc,GAA+B,EAAE,CAAC;QACtD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;;YAEzC,cAAc,CAAC,CAAmB,gBAAA,EAAA,GAAG,CAAE,CAAA,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;SAC5D;QACD,YAAY,CAAA,KAAA,wBAAkB,cAAc,CAAC,CAAC;AAC9C,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;SAAM;AACL,QAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;QAClD,YAAY,CAAA,QAAA,2BAAqB,aAAa,EAAE;AAC9C,YAAA,MAAM,EAAE,IAAI;AACZ,YAAA,iBAAiB,EAAE,UAAU;AAC9B,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;AAKG;AACI,eAAe,kCAAkC,CACtD,YAAkB,EAClB,qBAAsC,EAAA;AAEtC,IAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;IAClD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;QACrC,YAAY,CAAA,KAAA,wBAEV,aAAa,EACb,WAAW,EACX,CAAC,QAAgB,KAAI;YACnB,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,MAAM,CAAC,aAAa,CAAC,MAAM,CAAA,cAAA,mCAA6B,CAAC,CAAC;aAC3D;YACD,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpB,SAAC,CACF,CAAC;AACJ,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;AAIG;AACI,eAAeC,+BAA6B,CACjD,qBAAsC,EACtC,OAAgB,EAAA;AAEhB,IAAA,MAAM,aAAa,GAAG,MAAM,qBAAqB,CAAC;IAClD,MAAM,CAAC,cAAc,aAAa,CAAA,CAAE,CAAC,GAAG,CAAC,OAAO,CAAC;AACnD,CAAC;AAED;;AAEG;AACI,IAAI,6BAA0D,CAAC;AAEtE;;;;;AAKG;AACG,SAAU,yBAAyB,CACvC,eAAiC,EAAA;IAEjC,6BAA6B,GAAG,eAAe,CAAC;AAClD,CAAC;AAED;;;;;AAKG;AACG,SAAU,iCAAiC,CAC/C,YAA2B,EAAA;IAE3B,6BAA6B,GAAG,YAAY,CAAC;AAC/C;;AC9MA;;;;;;;;;;;;;;;AAeG;AAsBH,eAAe,iBAAiB,GAAA;AAC9B,IAAA,IAAI,CAACC,yBAAoB,EAAE,EAAE;AAC3B,QAAA,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;AACzD,YAAA,SAAS,EAAE,iDAAiD;SAC7D,CAAC,CAAC,OAAO,CACX,CAAC;AACF,QAAA,OAAO,KAAK,CAAC;KACd;SAAM;AACL,QAAA,IAAI;YACF,MAAMC,8BAAyB,EAAE,CAAC;SACnC;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,MAAM,CAAuC,uBAAA,6CAAA;AACzD,gBAAA,SAAS,EAAG,CAAW,EAAE,QAAQ,EAAE;aACpC,CAAC,CAAC,OAAO,CACX,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;;;;AAYG;AACI,eAAe,oBAAoB,CACxC,GAAgB,EAChB,yBAEC,EACD,oBAA+C,EAC/C,aAA6C,EAC7C,QAAc,EACd,aAAqB,EACrB,OAA2B,EAAA;AAE3B,IAAA,MAAM,oBAAoB,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAC;;IAE9D,oBAAoB;SACjB,IAAI,CAAC,MAAM,IAAG;QACb,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;AAC1D,QAAA,IACE,GAAG,CAAC,OAAO,CAAC,aAAa;YACzB,MAAM,CAAC,aAAa,KAAK,GAAG,CAAC,OAAO,CAAC,aAAa,EAClD;YACA,MAAM,CAAC,IAAI,CACT,CAAA,iDAAA,EAAoD,GAAG,CAAC,OAAO,CAAC,aAAa,CAAG,CAAA,CAAA;gBAC9E,CAA+D,4DAAA,EAAA,MAAM,CAAC,aAAa,CAAI,EAAA,CAAA;gBACvF,CAAgF,8EAAA,CAAA;gBAChF,CAAa,WAAA,CAAA;AACb,gBAAA,CAAA,6EAAA,CAA+E,CAClF,CAAC;SACH;AACH,KAAC,CAAC;AACD,SAAA,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE/B,IAAA,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAErD,MAAM,UAAU,GAAgC,iBAAiB,EAAE,CAAC,IAAI,CACtE,UAAU,IAAG;QACX,IAAI,UAAU,EAAE;AACd,YAAA,OAAO,aAAa,CAAC,KAAK,EAAE,CAAC;SAC9B;aAAM;AACL,YAAA,OAAO,SAAS,CAAC;SAClB;AACH,KAAC,CACF,CAAC;IAEF,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC7C,oBAAoB;QACpB,UAAU;AACX,KAAA,CAAC,CAAC;;;AAIH,IAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,EAAE;AACxC,QAAA,eAAe,CAAC,aAAa,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;KAC7D;;IAGD,IAAI,6BAA6B,EAAE;AACjC,QAAA,QAAQ,CAAsB,SAAA,4BAAA,SAAS,EAAE,6BAA6B,CAAC,CAAC;QACxE,yBAAyB,CAAC,SAAS,CAAC,CAAC;KACtC;;;;;AAMA,IAAA,QAAgB,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;;;AAGpC,IAAA,MAAM,gBAAgB,GAA4B,OAAO,EAAE,MAAM,IAAI,EAAE,CAAC;;AAGxE,IAAA,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAC1C,IAAA,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;AAE/B,IAAA,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,QAAA,gBAAgB,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;KACpC;;;;;AAMD,IAAA,QAAQ,oCAAqB,aAAa,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;;IAG5E,IAAI,6BAA6B,EAAE;QACjC,QAAQ,CAAA,KAAA,wBAAkB,6BAA6B,CAAC,CAAC;QACzD,iCAAiC,CAAC,SAAS,CAAC,CAAC;KAC9C;IAED,OAAO,aAAa,CAAC,aAAa,CAAC;AACrC;;ACnKA;;;;;;;;;;;;;;;AAeG;AAYH;;AAEG;MACU,gBAAgB,CAAA;AAC3B,IAAA,WAAA,CAAmB,GAAgB,EAAA;QAAhB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;KAAI;IACvC,OAAO,GAAA;QACL,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,CAAC;AAC1D,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF,CAAA;AAED;;;;AAIG;AACI,IAAI,yBAAyB,GAEhC,EAAE,CAAC;AAEP;;;;AAIG;AACH,IAAI,yBAAyB,GAEzB,EAAE,CAAC;AAEP;;;;;AAKG;AACH,MAAM,oBAAoB,GAAwC,EAAE,CAAC;AAErE;;AAEG;AACH,IAAI,aAAa,GAAW,WAAW,CAAC;AAExC;;AAEG;AACH,IAAI,QAAQ,GAAW,MAAM,CAAC;AAE9B;;;AAGG;AACH,IAAI,gBAAsB,CAAC;AAE3B;;;AAGG;AACI,IAAI,mBAAyB,CAAC;AAErC;;;AAGG;AACH,IAAI,cAAc,GAAY,KAAK,CAAC;AAkCpC;;;;;;;;;;;AAWG;AACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;IAC/C,IAAI,cAAc,EAAE;AAClB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,0CAAoC,CAAC;KAChE;AACD,IAAA,IAAI,OAAO,CAAC,aAAa,EAAE;AACzB,QAAA,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;KACvC;AACD,IAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,QAAA,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAC7B;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,4BAA4B,GAAA;IACnC,MAAM,qBAAqB,GAAG,EAAE,CAAC;IACjC,IAAIC,uBAAkB,EAAE,EAAE;AACxB,QAAA,qBAAqB,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;KACxE;AACD,IAAA,IAAI,CAACC,sBAAiB,EAAE,EAAE;AACxB,QAAA,qBAAqB,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;KAC1D;AACD,IAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;QACpC,MAAM,OAAO,GAAG,qBAAqB;AAClC,aAAA,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK,CAAA,CAAA,EAAI,KAAK,GAAG,CAAC,CAAK,EAAA,EAAA,OAAO,EAAE,CAAC;aACpD,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAA2C,2BAAA,iDAAA;AACzE,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KAC1B;AACH,CAAC;AAED;;;AAGG;SACa,OAAO,CACrB,GAAgB,EAChB,aAA6C,EAC7C,OAA2B,EAAA;AAE3B,IAAA,4BAA4B,EAAE,CAAC;AAC/B,IAAA,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;IAChC,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,gCAA0B,CAAC;KACtD;AACD,IAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,QAAA,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE;YAC7B,MAAM,CAAC,IAAI,CACT,CAA8F,4FAAA,CAAA;AAC5F,gBAAA,CAAA,0EAAA,EAA6E,GAAG,CAAC,OAAO,CAAC,aAAa,CAAE,CAAA;AACxG,gBAAA,CAAA,oEAAA,CAAsE,CACzE,CAAC;SACH;aAAM;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,iCAA2B,CAAC;SACvD;KACF;AACD,IAAA,IAAI,yBAAyB,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;QAC5C,MAAM,aAAa,CAAC,MAAM,CAAgC,gBAAA,sCAAA;AACxD,YAAA,EAAE,EAAE,KAAK;AACV,SAAA,CAAC,CAAC;KACJ;IAED,IAAI,CAAC,cAAc,EAAE;;;QAInB,oBAAoB,CAAC,aAAa,CAAC,CAAC;AAEpC,QAAA,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAChD,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,aAAa,EACb,QAAQ,CACT,CAAC;QACF,mBAAmB,GAAG,WAAW,CAAC;QAClC,gBAAgB,GAAG,QAAQ,CAAC;QAE5B,cAAc,GAAG,IAAI,CAAC;KACvB;;;IAGD,yBAAyB,CAAC,KAAK,CAAC,GAAG,oBAAoB,CACrD,GAAG,EACH,yBAAyB,EACzB,oBAAoB,EACpB,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,OAAO,CACR,CAAC;AAEF,IAAA,MAAM,iBAAiB,GAAqB,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAEtE,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;AC5OA;AAiEA;;;;;;AAMG;AACa,SAAA,YAAY,CAACC,KAAA,GAAmBC,UAAM,EAAE,EAAA;AACtD,IAAAD,KAAG,GAAGE,uBAAkB,CAACF,KAAG,CAAC,CAAC;;IAE9B,MAAM,iBAAiB,GAA0BG,gBAAY,CAC3DH,KAAG,EACH,cAAc,CACf,CAAC;AAEF,IAAA,IAAI,iBAAiB,CAAC,aAAa,EAAE,EAAE;AACrC,QAAA,OAAO,iBAAiB,CAAC,YAAY,EAAE,CAAC;KACzC;AAED,IAAA,OAAO,mBAAmB,CAACA,KAAG,CAAC,CAAC;AAClC,CAAC;AAED;;;;;;AAMG;SACa,mBAAmB,CACjCA,KAAgB,EAChB,UAA6B,EAAE,EAAA;;IAG/B,MAAM,iBAAiB,GAA0BG,gBAAY,CAC3DH,KAAG,EACH,cAAc,CACf,CAAC;AACF,IAAA,IAAI,iBAAiB,CAAC,aAAa,EAAE,EAAE;AACrC,QAAA,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,YAAY,EAAE,CAAC;QAC1D,IAAII,cAAS,CAAC,OAAO,EAAE,iBAAiB,CAAC,UAAU,EAAE,CAAC,EAAE;AACtD,YAAA,OAAO,gBAAgB,CAAC;SACzB;aAAM;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,0CAAoC,CAAC;SAChE;KACF;IACD,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AACpE,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;;;;AAUG;AACI,eAAe,WAAW,GAAA;IAC/B,IAAIN,uBAAkB,EAAE,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,IAAI,CAACC,sBAAiB,EAAE,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,IAAI,CAACH,yBAAoB,EAAE,EAAE;AAC3B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI;AACF,QAAA,MAAM,YAAY,GAAY,MAAMC,8BAAyB,EAAE,CAAC;AAChE,QAAA,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;AACd,QAAA,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;;;;;;;AAUG;SACa,gBAAgB,CAC9B,iBAA4B,EAC5B,UAAkB,EAClB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAGK,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAG,kBAAwB,CACtB,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,UAAU,EACV,OAAO,CACR,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;AAOG;AACI,eAAe,0BAA0B,CAC9C,iBAA4B,EAAA;AAE5B,IAAA,iBAAiB,GAAGH,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAA,OAAO,kCAAkC,CACvC,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,CAChE,CAAC;AACJ,CAAC;AAED;;;;;;;AAOG;SACa,SAAS,CACvB,iBAA4B,EAC5B,EAAiB,EACjB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAGA,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAI,WAAiB,CACf,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,EAAE,EACF,OAAO,CACR,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AAED;;;;AAIG;SACa,iBAAiB,CAC/B,iBAA4B,EAC5B,UAAwB,EACxB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAGJ,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAK,mBAAyB,CACvB,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,UAAU,EACV,OAAO,CACR,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,6BAA6B,CAC3C,iBAA4B,EAC5B,OAAgB,EAAA;AAEhB,IAAA,iBAAiB,GAAGL,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAM,+BAAqC,CACnC,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,OAAO,CACR,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;AAMG;AACG,SAAU,yBAAyB,CAAC,YAA0B,EAAA;;IAElE,IAAI,mBAAmB,EAAE;QACvB,mBAAmB,CAAA,KAAA,wBAAkB,YAAY,CAAC,CAAC;KACpD;SAAM;QACL,iCAAiC,CAAC,YAAY,CAAC,CAAC;KACjD;AACH,CAAC;AA6cD;;;;;;;;;;AAUG;AACG,SAAU,QAAQ,CACtB,iBAA4B,EAC5B,SAAiB,EACjB,WAAyB,EACzB,OAA8B,EAAA;AAE9B,IAAA,iBAAiB,GAAGN,uBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,IAAAO,UAAgB,CACd,mBAAmB,EACnB,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAM,CAAC,EAC/D,SAAS,EACT,WAAW,EACX,OAAO,CACR,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC;AASD;;;;;;;;AAQG;AACG,SAAU,UAAU,CAAC,eAAgC,EAAA;;IAEzD,IAAI,mBAAmB,EAAE;AACvB,QAAA,mBAAmB,CAAsB,SAAA,4BAAA,QAAQ,EAAE,eAAe,CAAC,CAAC;KACrE;SAAM;QACL,yBAAyB,CAAC,eAAe,CAAC,CAAC;KAC5C;AACH;;;;;ACtwBA;;;;;AAKG;AAyCH,SAAS,iBAAiB,GAAA;AACxB,IAAAC,sBAAkB,CAChB,IAAIC,mBAAS,CACX,cAAc,EACd,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAA0B,KAAI;;QAEnE,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,MAAM,aAAa,GAAG,SAAS;aAC5B,WAAW,CAAC,wBAAwB,CAAC;AACrC,aAAA,YAAY,EAAE,CAAC;QAElB,OAAO,OAAO,CAAC,GAAG,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;KACtD,EAAA,QAAA,4BAEF,CACF,CAAC;IAEFD,sBAAkB,CAChB,IAAIC,mBAAS,CAAC,oBAAoB,EAAE,eAAe,EAAwB,SAAA,6BAAA,CAC5E,CAAC;AAEF,IAAAC,mBAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAAA,mBAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;IAEnD,SAAS,eAAe,CACtB,SAA6B,EAAA;AAE7B,QAAA,IAAI;YACF,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,YAAY,EAAE,CAAC;YACvE,OAAO;AACL,gBAAA,QAAQ,EAAE,CACR,SAAiB,EACjB,WAAwC,EACxC,OAA8B,KAC3B,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;aAC1D,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,aAAa,CAAC,MAAM,CAA8C,8BAAA,oDAAA;AACtE,gBAAA,MAAM,EAAE,CAAU;AACnB,aAAA,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED,iBAAiB,EAAE;;;;;;;;;;;;;;;"}