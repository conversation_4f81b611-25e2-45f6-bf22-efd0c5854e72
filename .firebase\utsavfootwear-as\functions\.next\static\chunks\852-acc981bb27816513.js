"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[852],{1055:(e,t,r)=>{let i,n;r.d(t,{MF:()=>B,j6:()=>R,xZ:()=>N,om:()=>T,Sx:()=>U,Wp:()=>P,KO:()=>M});var s=r(2881),a=r(6702),o=r(1280);let l=new WeakMap,c=new WeakMap,h=new WeakMap,u=new WeakMap,d=new WeakMap,p={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return c.get(e);if("objectStoreNames"===t)return e.objectStoreNames||h.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return f(e[t])},set:(e,t,r)=>(e[t]=r,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function f(e){if(e instanceof IDBRequest){let t=new Promise((t,r)=>{let i=()=>{e.removeEventListener("success",n),e.removeEventListener("error",s)},n=()=>{t(f(e.result)),i()},s=()=>{r(e.error),i()};e.addEventListener("success",n),e.addEventListener("error",s)});return t.then(t=>{t instanceof IDBCursor&&l.set(t,e)}).catch(()=>{}),d.set(t,e),t}if(u.has(e))return u.get(e);let t=function(e){if("function"==typeof e)return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(n||(n=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(g(this),t),f(l.get(this))}:function(...t){return f(e.apply(g(this),t))}:function(t,...r){let i=e.call(g(this),t,...r);return h.set(i,t.sort?t.sort():[t]),f(i)};return e instanceof IDBTransaction&&function(e){if(c.has(e))return;let t=new Promise((t,r)=>{let i=()=>{e.removeEventListener("complete",n),e.removeEventListener("error",s),e.removeEventListener("abort",s)},n=()=>{t(),i()},s=()=>{r(e.error||new DOMException("AbortError","AbortError")),i()};e.addEventListener("complete",n),e.addEventListener("error",s),e.addEventListener("abort",s)});c.set(e,t)}(e),(i||(i=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some(t=>e instanceof t)?new Proxy(e,p):e}(e);return t!==e&&(u.set(e,t),d.set(t,e)),t}let g=e=>d.get(e),_=["get","getKey","getAll","getAllKeys","count"],m=["put","add","delete","clear"],b=new Map;function E(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(b.get(t))return b.get(t);let r=t.replace(/FromIndex$/,""),i=t!==r,n=m.includes(r);if(!(r in(i?IDBIndex:IDBObjectStore).prototype)||!(n||_.includes(r)))return;let s=async function(e,...t){let s=this.transaction(e,n?"readwrite":"readonly"),a=s.store;return i&&(a=a.index(t.shift())),(await Promise.all([a[r](...t),n&&s.done]))[0]};return b.set(t,s),s}p=(e=>({...e,get:(t,r,i)=>E(t,r)||e.get(t,r,i),has:(t,r)=>!!E(t,r)||e.has(t,r)}))(p);class v{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(e=>{if(!function(e){let t=e.getComponent();return t?.type==="VERSION"}(e))return null;{let t=e.getImmediate();return`${t.library}/${t.version}`}}).filter(e=>e).join(" ")}}let y="@firebase/app",C="0.14.0",w=new a.Vy("@firebase/app"),I="[DEFAULT]",A={[y]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},O=new Map,D=new Map,S=new Map;function k(e,t){try{e.container.addComponent(t)}catch(r){w.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,r)}}function T(e){let t=e.name;if(S.has(t))return w.debug(`There were multiple attempts to register component ${t}.`),!1;for(let r of(S.set(t,e),O.values()))k(r,e);for(let t of D.values())k(t,e);return!0}function R(e,t){let r=e.container.getProvider("heartbeat").getImmediate({optional:!0});return r&&r.triggerHeartbeat(),e.container.getProvider(t)}function N(e){return null!=e&&void 0!==e.settings}let L=new o.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class x{constructor(e,t,r){this._isDeleted=!1,this._options={...e},this._config={...t},this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new s.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw L.create("app-deleted",{appName:this._name})}}let B="12.0.0";function P(e,t={}){let r=e;"object"!=typeof t&&(t={name:t});let i={name:I,automaticDataCollectionEnabled:!0,...t},n=i.name;if("string"!=typeof n||!n)throw L.create("bad-app-name",{appName:String(n)});if(r||(r=(0,o.T9)()),!r)throw L.create("no-options");let a=O.get(n);if(a)if((0,o.bD)(r,a.options)&&(0,o.bD)(i,a.config))return a;else throw L.create("duplicate-app",{appName:n});let l=new s.h1(n);for(let e of S.values())l.addComponent(e);let c=new x(r,i,l);return O.set(n,c),c}function U(e=I){let t=O.get(e);if(!t&&e===I&&(0,o.T9)())return P();if(!t)throw L.create("no-app",{appName:e});return t}function M(e,t,r){let i=A[e]??e;r&&(i+=`-${r}`);let n=i.match(/\s|\//),a=t.match(/\s|\//);if(n||a){let e=[`Unable to register library "${i}" with version "${t}":`];n&&e.push(`library name "${i}" contains illegal characters (whitespace or "/")`),n&&a&&e.push("and"),a&&e.push(`version name "${t}" contains illegal characters (whitespace or "/")`),w.warn(e.join(" "));return}T(new s.uA(`${i}-version`,()=>({library:i,version:t}),"VERSION"))}let $="firebase-heartbeat-store",F=null;function j(){return F||(F=(function(e,t,{blocked:r,upgrade:i,blocking:n,terminated:s}={}){let a=indexedDB.open(e,1),o=f(a);return i&&a.addEventListener("upgradeneeded",e=>{i(f(a.result),e.oldVersion,e.newVersion,f(a.transaction),e)}),r&&a.addEventListener("blocked",e=>r(e.oldVersion,e.newVersion,e)),o.then(e=>{s&&e.addEventListener("close",()=>s()),n&&e.addEventListener("versionchange",e=>n(e.oldVersion,e.newVersion,e))}).catch(()=>{}),o})("firebase-heartbeat-database",0,{upgrade:(e,t)=>{if(0===t)try{e.createObjectStore($)}catch(e){console.warn(e)}}}).catch(e=>{throw L.create("idb-open",{originalErrorMessage:e.message})})),F}async function V(e){try{let t=(await j()).transaction($),r=await t.objectStore($).get(z(e));return await t.done,r}catch(e){if(e instanceof o.g)w.warn(e.message);else{let t=L.create("idb-get",{originalErrorMessage:e?.message});w.warn(t.message)}}}async function H(e,t){try{let r=(await j()).transaction($,"readwrite"),i=r.objectStore($);await i.put(t,z(e)),await r.done}catch(e){if(e instanceof o.g)w.warn(e.message);else{let t=L.create("idb-set",{originalErrorMessage:e?.message});w.warn(t.message)}}}function z(e){return`${e.name}!${e.options.appId}`}class W{constructor(e){this.container=e,this._heartbeatsCache=null;let t=this.container.getProvider("app").getImmediate();this._storage=new Z(t),this._heartbeatsCachePromise=this._storage.read().then(e=>(this._heartbeatsCache=e,e))}async triggerHeartbeat(){try{let e=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),t=K();if(this._heartbeatsCache?.heartbeats==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null)||this._heartbeatsCache.lastSentHeartbeatDate===t||this._heartbeatsCache.heartbeats.some(e=>e.date===t))return;if(this._heartbeatsCache.heartbeats.push({date:t,agent:e}),this._heartbeatsCache.heartbeats.length>30){let e=function(e){if(0===e.length)return -1;let t=0,r=e[0].date;for(let i=1;i<e.length;i++)e[i].date<r&&(r=e[i].date,t=i);return t}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(e){w.warn(e)}}async getHeartbeatsHeader(){try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null||0===this._heartbeatsCache.heartbeats.length)return"";let e=K(),{heartbeatsToSend:t,unsentEntries:r}=function(e,t=1024){let r=[],i=e.slice();for(let n of e){let e=r.find(e=>e.agent===n.agent);if(e){if(e.dates.push(n.date),J(r)>t){e.dates.pop();break}}else if(r.push({agent:n.agent,dates:[n.date]}),J(r)>t){r.pop();break}i=i.slice(1)}return{heartbeatsToSend:r,unsentEntries:i}}(this._heartbeatsCache.heartbeats),i=(0,o.Uj)(JSON.stringify({version:2,heartbeats:t}));return this._heartbeatsCache.lastSentHeartbeatDate=e,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(e){return w.warn(e),""}}}function K(){return new Date().toISOString().substring(0,10)}class Z{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,o.zW)()&&(0,o.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let e=await V(this.app);return e?.heartbeats?e:{heartbeats:[]}}}async overwrite(e){if(await this._canUseIndexedDBPromise){let t=await this.read();return H(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??t.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){if(await this._canUseIndexedDBPromise){let t=await this.read();return H(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??t.lastSentHeartbeatDate,heartbeats:[...t.heartbeats,...e.heartbeats]})}}}function J(e){return(0,o.Uj)(JSON.stringify({version:2,heartbeats:e})).length}T(new s.uA("platform-logger",e=>new v(e),"PRIVATE")),T(new s.uA("heartbeat",e=>new W(e),"PRIVATE")),M(y,C,""),M(y,C,"esm2020"),M("fire-js","")},1115:(e,t,r)=>{r.d(t,{C3:()=>i.C3,Jt:()=>i.Jt,KR:()=>i.KR,TF:()=>i.TF,VC:()=>i.VC,hZ:()=>i.hZ,yo:()=>i.yo});var i=r(9655)},1280:(e,t,r)=>{r.d(t,{cY:()=>v,FA:()=>U,g:()=>P,gz:()=>Y,vA:()=>s,Hk:()=>a,K3:()=>c,u:()=>p,KA:()=>u,Uj:()=>d,gR:()=>z,Fy:()=>w,tD:()=>Q,A4:()=>f,bD:()=>function e(t,r){if(t===r)return!0;let i=Object.keys(t),n=Object.keys(r);for(let s of i){if(!n.includes(s))return!1;let i=t[s],a=r[s];if(J(i)&&J(a)){if(!e(i,a))return!1}else if(i!==a)return!1}for(let e of n)if(!i.includes(e))return!1;return!0},dI:()=>er,hp:()=>X,T9:()=>b,Tj:()=>_,yU:()=>m,XA:()=>E,Ku:()=>es,ZQ:()=>D,qc:()=>H,sr:()=>T,zJ:()=>y,c1:()=>k,Im:()=>K,lT:()=>N,zW:()=>x,jZ:()=>S,$g:()=>L,lV:()=>R,Cv:()=>V,$L:()=>$,kH:()=>Z,gE:()=>C,Am:()=>q,I9:()=>G,yw:()=>W,OE:()=>en,kj:()=>ei,As:()=>F,P1:()=>O,eX:()=>B});var i=r(1890);let n={NODE_CLIENT:!1,NODE_ADMIN:!1,SDK_VERSION:"${JSCORE_VERSION}"},s=function(e,t){if(!e)throw a(t)},a=function(e){return Error("Firebase Database ("+n.SDK_VERSION+") INTERNAL ASSERT FAILED: "+e)},o=function(e){let t=[],r=0;for(let i=0;i<e.length;i++){let n=e.charCodeAt(i);n<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:((64512&n)==55296&&i+1<e.length&&(64512&e.charCodeAt(i+1))==56320?(n=65536+((1023&n)<<10)+(1023&e.charCodeAt(++i)),t[r++]=n>>18|240,t[r++]=n>>12&63|128):t[r++]=n>>12|224,t[r++]=n>>6&63|128),t[r++]=63&n|128)}return t},l=function(e){let t=[],r=0,i=0;for(;r<e.length;){let n=e[r++];if(n<128)t[i++]=String.fromCharCode(n);else if(n>191&&n<224){let s=e[r++];t[i++]=String.fromCharCode((31&n)<<6|63&s)}else if(n>239&&n<365){let s=e[r++],a=((7&n)<<18|(63&s)<<12|(63&e[r++])<<6|63&e[r++])-65536;t[i++]=String.fromCharCode(55296+(a>>10)),t[i++]=String.fromCharCode(56320+(1023&a))}else{let s=e[r++],a=e[r++];t[i++]=String.fromCharCode((15&n)<<12|(63&s)<<6|63&a)}}return t.join("")},c={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();let r=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let t=0;t<e.length;t+=3){let n=e[t],s=t+1<e.length,a=s?e[t+1]:0,o=t+2<e.length,l=o?e[t+2]:0,c=n>>2,h=(3&n)<<4|a>>4,u=(15&a)<<2|l>>6,d=63&l;!o&&(d=64,s||(u=64)),i.push(r[c],r[h],r[u],r[d])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(o(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):l(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();let r=t?this.charToByteMapWebSafe_:this.charToByteMap_,i=[];for(let t=0;t<e.length;){let n=r[e.charAt(t++)],s=t<e.length?r[e.charAt(t)]:0,a=++t<e.length?r[e.charAt(t)]:64,o=++t<e.length?r[e.charAt(t)]:64;if(++t,null==n||null==s||null==a||null==o)throw new h;let l=n<<2|s>>4;if(i.push(l),64!==a){let e=s<<4&240|a>>2;if(i.push(e),64!==o){let e=a<<6&192|o;i.push(e)}}}return i},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class h extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let u=function(e){let t=o(e);return c.encodeByteArray(t,!0)},d=function(e){return u(e).replace(/\./g,"")},p=function(e){try{return c.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function f(e){return function e(t,r){if(!(r instanceof Object))return r;switch(r.constructor){case Date:return new Date(r.getTime());case Object:void 0===t&&(t={});break;case Array:t=[];break;default:return r}for(let i in r)r.hasOwnProperty(i)&&"__proto__"!==i&&(t[i]=e(t[i],r[i]));return t}(void 0,e)}let g=()=>{try{return function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__||(()=>{if(void 0===i||void 0===i.env)return;let e=i.env.__FIREBASE_DEFAULTS__;if(e)return JSON.parse(e)})()||(()=>{let e;if("undefined"==typeof document)return;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}let t=e&&p(e[1]);return t&&JSON.parse(t)})()}catch(e){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);return}},_=e=>g()?.emulatorHosts?.[e],m=e=>{let t=_(e);if(!t)return;let r=t.lastIndexOf(":");if(r<=0||r+1===t.length)throw Error(`Invalid host ${t} with no separate hostname and port!`);let i=parseInt(t.substring(r+1),10);return"["===t[0]?[t.substring(1,r-1),i]:[t.substring(0,r),i]},b=()=>g()?.config,E=e=>g()?.[`_${e}`];class v{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}wrapCallback(e){return(t,r)=>{t?this.reject(t):this.resolve(r),"function"==typeof e&&(this.promise.catch(()=>{}),1===e.length?e(t):e(t,r))}}}function y(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch{return!1}}async function C(e){return(await fetch(e,{credentials:"include"})).ok}function w(e,t){if(e.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let r=t||"demo-project",i=e.iat||0,n=e.sub||e.user_id;if(!n)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let s={iss:`https://securetoken.google.com/${r}`,aud:r,iat:i,exp:i+3600,auth_time:i,sub:n,user_id:n,firebase:{sign_in_provider:"custom",identities:{}},...e};return[d(JSON.stringify({alg:"none",type:"JWT"})),d(JSON.stringify(s)),""].join(".")}let I={},A=!1;function O(e,t){if("undefined"==typeof window||"undefined"==typeof document||!y(window.location.host)||I[e]===t||I[e]||A)return;function r(e){return`__firebase__banner__${e}`}I[e]=t;let i="__firebase__banner",n=function(){let e={prod:[],emulator:[]};for(let t of Object.keys(I))I[t]?e.emulator.push(t):e.prod.push(t);return e}().prod.length>0;function s(){let e,t,s=(e=document.getElementById(i),t=!1,e||((e=document.createElement("div")).setAttribute("id",i),t=!0),{created:t,element:e}),a=r("text"),o=document.getElementById(a)||document.createElement("span"),l=r("learnmore"),c=document.getElementById(l)||document.createElement("a"),h=r("preprendIcon"),u=document.getElementById(h)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(s.created){let e=s.element;e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center",c.setAttribute("id",l),c.innerText="Learn more",c.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",c.setAttribute("target","__blank"),c.style.paddingLeft="5px",c.style.textDecoration="underline";let t=function(){let e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{A=!0;let e=document.getElementById(i);e&&e.remove()},e}();u.setAttribute("width","24"),u.setAttribute("id",h),u.setAttribute("height","24"),u.setAttribute("viewBox","0 0 24 24"),u.setAttribute("fill","none"),u.style.marginLeft="-6px",e.append(u,o,c,t),document.body.appendChild(e)}n?(o.innerText="Preview backend disconnected.",u.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(u.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,o.innerText="Preview backend running in this workspace."),o.setAttribute("id",a)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",s):s()}function D(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function S(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(D())}function k(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function T(){let e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function R(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function N(){let e=D();return e.indexOf("MSIE ")>=0||e.indexOf("Trident/")>=0}function L(){return!0===n.NODE_CLIENT||!0===n.NODE_ADMIN}function x(){try{return"object"==typeof indexedDB}catch(e){return!1}}function B(){return new Promise((e,t)=>{try{let r=!0,i="validate-browser-context-for-indexeddb-analytics-module",n=self.indexedDB.open(i);n.onsuccess=()=>{n.result.close(),r||self.indexedDB.deleteDatabase(i),e(!0)},n.onupgradeneeded=()=>{r=!1},n.onerror=()=>{t(n.error?.message||"")}}catch(e){t(e)}})}class P extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,P.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,U.prototype.create)}}class U{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var r,i;let n=t[0]||{},s=`${this.service}/${e}`,a=this.errors[e],o=a?(r=a,i=n,r.replace(M,(e,t)=>{let r=i[t];return null!=r?String(r):`<${t}?>`})):"Error",l=`${this.serviceName}: ${o} (${s}).`;return new P(s,l,n)}}let M=/\{\$([^}]+)}/g;function $(e){return JSON.parse(e)}function F(e){return JSON.stringify(e)}let j=function(e){let t={},r={},i={},n="";try{let s=e.split(".");t=$(p(s[0])||""),r=$(p(s[1])||""),n=s[2],i=r.d||{},delete r.d}catch(e){}return{header:t,claims:r,data:i,signature:n}},V=function(e){let t=j(e).claims;return!!t&&"object"==typeof t&&t.hasOwnProperty("iat")},H=function(e){let t=j(e).claims;return"object"==typeof t&&!0===t.admin};function z(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function W(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0}function K(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}function Z(e,t,r){let i={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=t.call(r,e[n],n,e));return i}function J(e){return null!==e&&"object"==typeof e}function q(e){let t=[];for(let[r,i]of Object.entries(e))Array.isArray(i)?i.forEach(e=>{t.push(encodeURIComponent(r)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(r)+"="+encodeURIComponent(i));return t.length?"&"+t.join("&"):""}function G(e){let t={};return e.replace(/^\?/,"").split("&").forEach(e=>{if(e){let[r,i]=e.split("=");t[decodeURIComponent(r)]=decodeURIComponent(i)}}),t}function X(e){let t=e.indexOf("?");if(!t)return"";let r=e.indexOf("#",t);return e.substring(t,r>0?r:void 0)}class Y{constructor(){this.chain_=[],this.buf_=[],this.W_=[],this.pad_=[],this.inbuf_=0,this.total_=0,this.blockSize=64,this.pad_[0]=128;for(let e=1;e<this.blockSize;++e)this.pad_[e]=0;this.reset()}reset(){this.chain_[0]=0x67452301,this.chain_[1]=0xefcdab89,this.chain_[2]=0x98badcfe,this.chain_[3]=0x10325476,this.chain_[4]=0xc3d2e1f0,this.inbuf_=0,this.total_=0}compress_(e,t){let r,i;t||(t=0);let n=this.W_;if("string"==typeof e)for(let r=0;r<16;r++)n[r]=e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|e.charCodeAt(t+3),t+=4;else for(let r=0;r<16;r++)n[r]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4;for(let e=16;e<80;e++){let t=n[e-3]^n[e-8]^n[e-14]^n[e-16];n[e]=t<<1|t>>>31}let s=this.chain_[0],a=this.chain_[1],o=this.chain_[2],l=this.chain_[3],c=this.chain_[4];for(let e=0;e<80;e++){e<40?e<20?(r=l^a&(o^l),i=0x5a827999):(r=a^o^l,i=0x6ed9eba1):e<60?(r=a&o|l&(a|o),i=0x8f1bbcdc):(r=a^o^l,i=0xca62c1d6);let t=(s<<5|s>>>27)+r+c+i+n[e]|0;c=l,l=o,o=a<<30|a>>>2,a=s,s=t}this.chain_[0]=this.chain_[0]+s|0,this.chain_[1]=this.chain_[1]+a|0,this.chain_[2]=this.chain_[2]+o|0,this.chain_[3]=this.chain_[3]+l|0,this.chain_[4]=this.chain_[4]+c|0}update(e,t){if(null==e)return;void 0===t&&(t=e.length);let r=t-this.blockSize,i=0,n=this.buf_,s=this.inbuf_;for(;i<t;){if(0===s)for(;i<=r;)this.compress_(e,i),i+=this.blockSize;if("string"==typeof e){for(;i<t;)if(n[s]=e.charCodeAt(i),++s,++i,s===this.blockSize){this.compress_(n),s=0;break}}else for(;i<t;)if(n[s]=e[i],++s,++i,s===this.blockSize){this.compress_(n),s=0;break}}this.inbuf_=s,this.total_+=t}digest(){let e=[],t=8*this.total_;this.inbuf_<56?this.update(this.pad_,56-this.inbuf_):this.update(this.pad_,this.blockSize-(this.inbuf_-56));for(let e=this.blockSize-1;e>=56;e--)this.buf_[e]=255&t,t/=256;this.compress_(this.buf_);let r=0;for(let t=0;t<5;t++)for(let i=24;i>=0;i-=8)e[r]=this.chain_[t]>>i&255,++r;return e}}function Q(e,t){let r=new ee(e,t);return r.subscribe.bind(r)}class ee{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(e){this.forEachObserver(t=>{t.next(e)})}error(e){this.forEachObserver(t=>{t.error(e)}),this.close(e)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let i;if(void 0===e&&void 0===t&&void 0===r)throw Error("Missing Observer.");void 0===(i=!function(e,t){if("object"!=typeof e||null===e)return!1;for(let r of t)if(r in e&&"function"==typeof e[r])return!0;return!1}(e,["next","error","complete"])?{next:e,error:t,complete:r}:e).next&&(i.next=et),void 0===i.error&&(i.error=et),void 0===i.complete&&(i.complete=et);let n=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch(e){}}),this.observers.push(i),n}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(e){if(!this.finalized)for(let t=0;t<this.observers.length;t++)this.sendOne(t,e)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function et(){}function er(e,t){return`${e} failed: ${t} argument `}let ei=function(e){let t=[],r=0;for(let i=0;i<e.length;i++){let n=e.charCodeAt(i);if(n>=55296&&n<=56319){let t=n-55296;s(++i<e.length,"Surrogate pair missing trail surrogate."),n=65536+(t<<10)+(e.charCodeAt(i)-56320)}n<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:(n<65536?t[r++]=n>>12|224:(t[r++]=n>>18|240,t[r++]=n>>12&63|128),t[r++]=n>>6&63|128),t[r++]=63&n|128)}return t},en=function(e){let t=0;for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);i<128?t++:i<2048?t+=2:i>=55296&&i<=56319?(t+=4,r++):t+=3}return t};function es(e){return e&&e._delegate?e._delegate:e}},2881:(e,t,r)=>{r.d(t,{h1:()=>o,uA:()=>n});var i=r(1280);class n{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let s="[DEFAULT]";class a{constructor(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){let t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){let e=new i.cY;if(this.instancesDeferred.set(t,e),this.isInitialized(t)||this.shouldAutoInitialize())try{let r=this.getOrInitializeService({instanceIdentifier:t});r&&e.resolve(r)}catch(e){}}return this.instancesDeferred.get(t).promise}getImmediate(e){let t=this.normalizeInstanceIdentifier(e?.identifier),r=e?.optional??!1;if(this.isInitialized(t)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:t})}catch(e){if(r)return null;throw e}if(r)return null;throw Error(`Service ${this.name} is not available`)}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if("EAGER"===e.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:s})}catch(e){}for(let[e,t]of this.instancesDeferred.entries()){let r=this.normalizeInstanceIdentifier(e);try{let e=this.getOrInitializeService({instanceIdentifier:r});t.resolve(e)}catch(e){}}}}clearInstance(e=s){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){let e=Array.from(this.instances.values());await Promise.all([...e.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...e.filter(e=>"_delete"in e).map(e=>e._delete())])}isComponentSet(){return null!=this.component}isInitialized(e=s){return this.instances.has(e)}getOptions(e=s){return this.instancesOptions.get(e)||{}}initialize(e={}){let{options:t={}}=e,r=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let i=this.getOrInitializeService({instanceIdentifier:r,options:t});for(let[e,t]of this.instancesDeferred.entries())r===this.normalizeInstanceIdentifier(e)&&t.resolve(i);return i}onInit(e,t){let r=this.normalizeInstanceIdentifier(t),i=this.onInitCallbacks.get(r)??new Set;i.add(e),this.onInitCallbacks.set(r,i);let n=this.instances.get(r);return n&&e(n,r),()=>{i.delete(e)}}invokeOnInitCallbacks(e,t){let r=this.onInitCallbacks.get(t);if(r)for(let i of r)try{i(e,t)}catch{}}getOrInitializeService({instanceIdentifier:e,options:t={}}){var r;let i=this.instances.get(e);if(!i&&this.component&&(i=this.component.instanceFactory(this.container,{instanceIdentifier:(r=e)===s?void 0:r,options:t}),this.instances.set(e,i),this.instancesOptions.set(e,t),this.invokeOnInitCallbacks(i,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,i)}catch{}return i||null}normalizeInstanceIdentifier(e=s){return this.component?this.component.multipleInstances?e:s:e}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class o{constructor(e){this.name=e,this.providers=new Map}addComponent(e){let t=this.getProvider(e.name);if(t.isComponentSet())throw Error(`Component ${e.name} has already been registered with ${this.name}`);t.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);let t=new a(e,this);return this.providers.set(e,t),t}getProviders(){return Array.from(this.providers.values())}}},3915:(e,t,r)=>{r.d(t,{Wp:()=>i.Wp});var i=r(1055);(0,i.KO)("firebase","12.0.0","app")},5404:(e,t,r)=>{r.d(t,{xI:()=>i.p,hg:()=>i.z,x9:()=>i.ac,CI:()=>i.D});var i=r(3239);r(1055),r(1280),r(6702),r(2881)},6702:(e,t,r)=>{var i;r.d(t,{$b:()=>i,Vy:()=>c});let n=[];!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(i||(i={}));let s={debug:i.DEBUG,verbose:i.VERBOSE,info:i.INFO,warn:i.WARN,error:i.ERROR,silent:i.SILENT},a=i.INFO,o={[i.DEBUG]:"log",[i.VERBOSE]:"log",[i.INFO]:"info",[i.WARN]:"warn",[i.ERROR]:"error"},l=(e,t,...r)=>{if(t<e.logLevel)return;let i=new Date().toISOString(),n=o[t];if(n)console[n](`[${i}]  ${e.name}:`,...r);else throw Error(`Attempted to log a message with an invalid logType (value: ${t})`)};class c{constructor(e){this.name=e,this._logLevel=a,this._logHandler=l,this._userLogHandler=null,n.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in i))throw TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?s[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,i.DEBUG,...e),this._logHandler(this,i.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,i.VERBOSE,...e),this._logHandler(this,i.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,i.INFO,...e),this._logHandler(this,i.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,i.WARN,...e),this._logHandler(this,i.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,i.ERROR,...e),this._logHandler(this,i.ERROR,...e)}}},7505:(e,t,r)=>{r.d(t,{c7:()=>I});var i,n,s=r(1055),a=r(1280),o=r(2881);let l="firebasestorage.googleapis.com";class c extends a.g{constructor(e,t,r=0){super(h(e),`Firebase Storage: ${t} (${h(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,c.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return h(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}function h(e){return"storage/"+e}function u(e){return new c(i.INVALID_ARGUMENT,e)}function d(){return new c(i.APP_DELETED,"The Firebase app was deleted.")}!function(e){e.UNKNOWN="unknown",e.OBJECT_NOT_FOUND="object-not-found",e.BUCKET_NOT_FOUND="bucket-not-found",e.PROJECT_NOT_FOUND="project-not-found",e.QUOTA_EXCEEDED="quota-exceeded",e.UNAUTHENTICATED="unauthenticated",e.UNAUTHORIZED="unauthorized",e.UNAUTHORIZED_APP="unauthorized-app",e.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",e.INVALID_CHECKSUM="invalid-checksum",e.CANCELED="canceled",e.INVALID_EVENT_NAME="invalid-event-name",e.INVALID_URL="invalid-url",e.INVALID_DEFAULT_BUCKET="invalid-default-bucket",e.NO_DEFAULT_BUCKET="no-default-bucket",e.CANNOT_SLICE_BLOB="cannot-slice-blob",e.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",e.NO_DOWNLOAD_URL="no-download-url",e.INVALID_ARGUMENT="invalid-argument",e.INVALID_ARGUMENT_COUNT="invalid-argument-count",e.APP_DELETED="app-deleted",e.INVALID_ROOT_OPERATION="invalid-root-operation",e.INVALID_FORMAT="invalid-format",e.INTERNAL_ERROR="internal-error",e.UNSUPPORTED_ENVIRONMENT="unsupported-environment"}(i||(i={}));class p{constructor(e,t){this.bucket=e,this.path_=t}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){let e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(e,t){let r;try{r=p.makeFromUrl(e,t)}catch(t){return new p(e,"")}if(""===r.path)return r;throw new c(i.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+e+"'.")}static makeFromUrl(e,t){let r=null,n="([A-Za-z0-9.\\-_]+)",s=RegExp("^gs://"+n+"(/(.*))?$","i");function a(e){e.path_=decodeURIComponent(e.path)}let o=t.replace(/[.]/g,"\\."),h=RegExp(`^https?://${o}/v[A-Za-z0-9_]+/b/${n}/o(/([^?#]*).*)?$`,"i"),u=t===l?"(?:storage.googleapis.com|storage.cloud.google.com)":t,d=[{regex:s,indices:{bucket:1,path:3},postModify:function(e){"/"===e.path.charAt(e.path.length-1)&&(e.path_=e.path_.slice(0,-1))}},{regex:h,indices:{bucket:1,path:3},postModify:a},{regex:RegExp(`^https?://${u}/${n}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:a}];for(let t=0;t<d.length;t++){let i=d[t],n=i.regex.exec(e);if(n){let e=n[i.indices.bucket],t=n[i.indices.path];t||(t=""),r=new p(e,t),i.postModify(r);break}}if(null==r)throw new c(i.INVALID_URL,"Invalid URL '"+e+"'.");return r}}class f{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}}function g(e,t,r,i){if(i<t)throw u(`Invalid value for '${e}'. Expected ${t} or greater.`);if(i>r)throw u(`Invalid value for '${e}'. Expected ${r} or less.`)}!function(e){e[e.NO_ERROR=0]="NO_ERROR",e[e.NETWORK_ERROR=1]="NETWORK_ERROR",e[e.ABORT=2]="ABORT"}(n||(n={}));class _{constructor(e,t,r,i,n,s,a,o,l,c,h,u=!0,d=!1){this.url_=e,this.method_=t,this.headers_=r,this.body_=i,this.successCodes_=n,this.additionalRetryCodes_=s,this.callback_=a,this.errorCallback_=o,this.timeout_=l,this.progressCallback_=c,this.connectionFactory_=h,this.retry=u,this.isUsingEmulator=d,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((e,t)=>{this.resolve_=e,this.reject_=t,this.start_()})}start_(){let e=(e,t)=>{if(t)return void e(!1,new m(!1,null,!0));let r=this.connectionFactory_();this.pendingConnection_=r;let i=e=>{let t=e.loaded,r=e.lengthComputable?e.total:-1;null!==this.progressCallback_&&this.progressCallback_(t,r)};null!==this.progressCallback_&&r.addUploadProgressListener(i),r.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&r.removeUploadProgressListener(i),this.pendingConnection_=null;let t=r.getErrorCode()===n.NO_ERROR,s=r.getStatus();if(!t||function(e,t){let r=e>=500&&e<600,i=-1!==[408,429].indexOf(e),n=-1!==t.indexOf(e);return r||i||n}(s,this.additionalRetryCodes_)&&this.retry)return void e(!1,new m(!1,null,r.getErrorCode()===n.ABORT));e(!0,new m(-1!==this.successCodes_.indexOf(s),r))})},t=(e,t)=>{let r=this.resolve_,n=this.reject_,s=t.connection;if(t.wasSuccessCode)try{let e=this.callback_(s,s.getResponse());void 0!==e?r(e):r()}catch(e){n(e)}else if(null!==s){let e=new c(i.UNKNOWN,"An unknown error occurred, please check the error payload for server response.");e.serverResponse=s.getErrorText(),n(this.errorCallback_?this.errorCallback_(s,e):e)}else n(t.canceled?this.appDelete_?d():new c(i.CANCELED,"User canceled the upload/download."):new c(i.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again."))};this.canceled_?t(!1,new m(!1,null,!0)):this.backoffId_=function(e,t,r){let i=1,n=null,s=null,a=!1,o=0,l=!1;function c(...e){l||(l=!0,t.apply(null,e))}function h(t){n=setTimeout(()=>{n=null,e(d,2===o)},t)}function u(){s&&clearTimeout(s)}function d(e,...t){let r;if(l)return void u();if(e||2===o||a){u(),c.call(null,e,...t);return}i<64&&(i*=2),1===o?(o=2,r=0):r=(i+Math.random())*1e3,h(r)}let p=!1;function f(e){p||(p=!0,u(),!l&&(null!==n?(e||(o=2),clearTimeout(n),h(0)):e||(o=1)))}return h(0),s=setTimeout(()=>{a=!0,f(!0)},r),f}(e,t,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class m{constructor(e,t,r){this.wasSuccessCode=e,this.connection=t,this.canceled=!!r}}class b{constructor(e,t){this._service=e,t instanceof p?this._location=t:this._location=p.makeFromUrl(t,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,t){return new b(e,t)}get root(){let e=new p(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){var e=this._location.path;let t=e.lastIndexOf("/",e.length-2);return -1===t?e:e.slice(t+1)}get storage(){return this._service}get parent(){let e=function(e){if(0===e.length)return null;let t=e.lastIndexOf("/");return -1===t?"":e.slice(0,t)}(this._location.path);if(null===e)return null;let t=new p(this._location.bucket,e);return new b(this._service,t)}_throwIfRoot(e){if(""===this._location.path)throw new c(i.INVALID_ROOT_OPERATION,"The operation '"+e+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}}function E(e,t){let r=t?.storageBucket;return null==r?null:p.makeFromBucketSpec(r,e)}class v{constructor(e,t,r,i,n,s=!1){this.app=e,this._authProvider=t,this._appCheckProvider=r,this._url=i,this._firebaseVersion=n,this._isUsingEmulator=s,this._bucket=null,this._host=l,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,null!=i?this._bucket=p.makeFromBucketSpec(i,this._host):this._bucket=E(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,null!=this._url?this._bucket=p.makeFromBucketSpec(this._url,e):this._bucket=E(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){g("time",0,1/0,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){g("time",0,1/0,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;let e=this._authProvider.getImmediate({optional:!0});if(e){let t=await e.getToken();if(null!==t)return t.accessToken}return null}async _getAppCheckToken(){if((0,s.xZ)(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let e=this._appCheckProvider.getImmediate({optional:!0});return e?(await e.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new b(this,e)}_makeRequest(e,t,r,i,n=!0){if(this._deleted)return new f(d());{let s=function(e,t,r,i,n,s,a=!0,o=!1){let l=function(e){let t=encodeURIComponent,r="?";for(let i in e)e.hasOwnProperty(i)&&(r=r+(t(i)+"=")+t(e[i])+"&");return r.slice(0,-1)}(e.urlParams),c=e.url+l,h=Object.assign({},e.headers);return t&&(h["X-Firebase-GMPID"]=t),null!==r&&r.length>0&&(h.Authorization="Firebase "+r),h["X-Firebase-Storage-Version"]="webjs/"+(s??"AppManager"),null!==i&&(h["X-Firebase-AppCheck"]=i),new _(c,e.method,h,e.body,e.successCodes,e.additionalRetryCodes,e.handler,e.errorHandler,e.timeout,e.progressCallback,n,a,o)}(e,this._appId,r,i,t,this._firebaseVersion,n,this._isUsingEmulator);return this._requests.add(s),s.getPromise().then(()=>this._requests.delete(s),()=>this._requests.delete(s)),s}}async makeRequestWithTokens(e,t){let[r,i]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,t,r,i).getPromise()}}let y="@firebase/storage",C="0.14.0",w="storage";function I(e=(0,s.Sx)(),t){e=(0,a.Ku)(e);let r=(0,s.j6)(e,w).getImmediate({identifier:t}),i=(0,a.yU)("storage");return i&&function(e,t,r,i={}){!function(e,t,r,i={}){e.host=`${t}:${r}`;let n=(0,a.zJ)(t);n&&((0,a.gE)(`https://${e.host}/b`),(0,a.P1)("Storage",!0)),e._isUsingEmulator=!0,e._protocol=n?"https":"http";let{mockUserToken:s}=i;s&&(e._overrideAuthToken="string"==typeof s?s:(0,a.Fy)(s,e.app.options.projectId))}(e,t,r,i)}(r,...i),r}(0,s.om)(new o.uA(w,function(e,{instanceIdentifier:t}){let r=e.getProvider("app").getImmediate();return new v(r,e.getProvider("auth-internal"),e.getProvider("app-check-internal"),t,s.MF)},"PUBLIC").setMultipleInstances(!0)),(0,s.KO)(y,C,""),(0,s.KO)(y,C,"esm2020")}}]);