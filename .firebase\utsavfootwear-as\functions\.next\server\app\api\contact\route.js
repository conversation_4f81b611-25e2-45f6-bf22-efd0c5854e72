(()=>{var a={};a.id=746,a.ids=[746],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7180:(a,b,c)=>{"use strict";let d=c(55511),e=c(29021),f=c(48178),g=c(27910).PassThrough,h=c(91292),i=c(18199),j=c(63356),k=c(89066),l=c(74508),m=c(38099),n=c(89047),o=c(39449),p=c(41748);class q{constructor(a,b){this.nodeCounter=0,b=b||{},this.baseBoundary=b.baseBoundary||d.randomBytes(8).toString("hex"),this.boundaryPrefix=b.boundaryPrefix||"--_NmP",this.disableFileAccess=!!b.disableFileAccess,this.disableUrlAccess=!!b.disableUrlAccess,this.normalizeHeaderKey=b.normalizeHeaderKey,this.date=new Date,this.rootNode=b.rootNode||this,this.keepBcc=!!b.keepBcc,b.filename&&(this.filename=b.filename,a||(a=i.detectMimeType(this.filename.split(".").pop()))),this.textEncoding=(b.textEncoding||"").toString().trim().charAt(0).toUpperCase(),this.parentNode=b.parentNode,this.hostname=b.hostname,this.newline=b.newline,this.childNodes=[],this._nodeId=++this.rootNode.nodeCounter,this._headers=[],this._isPlainText=!1,this._hasLongLines=!1,this._envelope=!1,this._raw=!1,this._transforms=[],this._processFuncs=[],a&&this.setHeader("Content-Type",a)}createChild(a,b){b||"object"!=typeof a||(b=a,a=void 0);let c=new q(a,b);return this.appendChild(c),c}appendChild(a){return a.rootNode!==this.rootNode&&(a.rootNode=this.rootNode,a._nodeId=++this.rootNode.nodeCounter),a.parentNode=this,this.childNodes.push(a),a}replace(a){return a===this?this:(this.parentNode.childNodes.forEach((b,c)=>{b===this&&(a.rootNode=this.rootNode,a.parentNode=this.parentNode,a._nodeId=this._nodeId,this.rootNode=this,this.parentNode=void 0,a.parentNode.childNodes[c]=a)}),a)}remove(){if(!this.parentNode)return this;for(let a=this.parentNode.childNodes.length-1;a>=0;a--)if(this.parentNode.childNodes[a]===this)return this.parentNode.childNodes.splice(a,1),this.parentNode=void 0,this.rootNode=this,this}setHeader(a,b){let c=!1,d;if(!b&&a&&"object"==typeof a)return a.key&&"value"in a?this.setHeader(a.key,a.value):Array.isArray(a)?a.forEach(a=>{this.setHeader(a.key,a.value)}):Object.keys(a).forEach(b=>{this.setHeader(b,a[b])}),this;d={key:a=this._normalizeHeaderKey(a),value:b};for(let b=0,e=this._headers.length;b<e;b++)this._headers[b].key===a&&(c?(this._headers.splice(b,1),b--,e--):(this._headers[b]=d,c=!0));return c||this._headers.push(d),this}addHeader(a,b){return!b&&a&&"object"==typeof a?a.key&&a.value?this.addHeader(a.key,a.value):Array.isArray(a)?a.forEach(a=>{this.addHeader(a.key,a.value)}):Object.keys(a).forEach(b=>{this.addHeader(b,a[b])}):Array.isArray(b)?b.forEach(b=>{this.addHeader(a,b)}):this._headers.push({key:this._normalizeHeaderKey(a),value:b}),this}getHeader(a){a=this._normalizeHeaderKey(a);for(let b=0,c=this._headers.length;b<c;b++)if(this._headers[b].key===a)return this._headers[b].value}setContent(a){return this.content=a,"function"==typeof this.content.pipe?(this._contentErrorHandler=a=>{this.content.removeListener("error",this._contentErrorHandler),this.content=a},this.content.once("error",this._contentErrorHandler)):"string"==typeof this.content&&(this._isPlainText=i.isPlainText(this.content),this._isPlainText&&i.hasLongerLines(this.content,76)&&(this._hasLongLines=!0)),this}build(a){let b;a||(b=new Promise((b,c)=>{a=h.callbackPromise(b,c)}));let c=this.createReadStream(),d=[],e=0,f=!1;return c.on("readable",()=>{let a;for(;null!==(a=c.read());)d.push(a),e+=a.length}),c.once("error",b=>{if(!f)return f=!0,a(b)}),c.once("end",b=>{if(!f)return f=!0,b&&b.length&&(d.push(b),e+=b.length),a(null,Buffer.concat(d,e))}),b}getTransferEncoding(){let a=!1,b=(this.getHeader("Content-Type")||"").toString().toLowerCase().trim();return this.content&&((a=(this.getHeader("Content-Transfer-Encoding")||"").toString().toLowerCase().trim())&&["base64","quoted-printable"].includes(a)||(/^text\//i.test(b)?a=this._isPlainText&&!this._hasLongLines?"7bit":"string"==typeof this.content||this.content instanceof Buffer?"Q"===this._getTextEncoding(this.content)?"quoted-printable":"base64":"B"===this.textEncoding?"base64":"quoted-printable":/^(multipart|message)\//i.test(b)||(a=a||"base64"))),a}buildHeaders(){let a=this.getTransferEncoding(),b=[];if(a&&this.setHeader("Content-Transfer-Encoding",a),this.filename&&!this.getHeader("Content-Disposition")&&this.setHeader("Content-Disposition","attachment"),this.rootNode===this){this.getHeader("Date")||this.setHeader("Date",this.date.toUTCString().replace(/GMT/,"+0000")),this.messageId(),this.getHeader("MIME-Version")||this.setHeader("MIME-Version","1.0");for(let a=this._headers.length-2;a>=0;a--){let b=this._headers[a];"Content-Type"===b.key&&(this._headers.splice(a,1),this._headers.push(b))}}return this._headers.forEach(a=>{let c,d,e=a.key,f=a.value,g={};if(!f||"object"!=typeof f||["From","Sender","To","Cc","Bcc","Reply-To","Date","References"].includes(e)||(Object.keys(f).forEach(a=>{"value"!==a&&(g[a]=f[a])}),(f=(f.value||"").toString()).trim())){if(g.prepared)return void(g.foldLines?b.push(i.foldLines(e+": "+f)):b.push(e+": "+f));switch(a.key){case"Content-Disposition":c=i.parseHeaderValue(f),this.filename&&(c.params.filename=this.filename),f=i.buildHeaderValue(c);break;case"Content-Type":c=i.parseHeaderValue(f),this._handleContentType(c),c.value.match(/^text\/plain\b/)&&"string"==typeof this.content&&/[\u0080-\uFFFF]/.test(this.content)&&(c.params.charset="utf-8"),f=i.buildHeaderValue(c),this.filename&&(((d=this._encodeWords(this.filename))!==this.filename||/[\s'"\\;:/=(),<>@[\]?]|^-/.test(d))&&(d='"'+d+'"'),f+="; name="+d);break;case"Bcc":if(!this.keepBcc)return}if(((f=this._encodeHeaderValue(e,f))||"").toString().trim()){if("function"==typeof this.normalizeHeaderKey){let a=this.normalizeHeaderKey(e,f);a&&"string"==typeof a&&a.length&&(e=a)}b.push(i.foldLines(e+": "+f,76))}}}),b.join("\r\n")}createReadStream(a){let b,c=new g(a=a||{}),d=c;this.stream(c,a,a=>{if(a)return void d.emit("error",a);c.end()});for(let a=0,c=this._transforms.length;a<c;a++)b="function"==typeof this._transforms[a]?this._transforms[a]():this._transforms[a],d.once("error",a=>{b.emit("error",a)}),d=d.pipe(b);b=new n,d.once("error",a=>{b.emit("error",a)}),d=d.pipe(b);for(let a=0,c=this._processFuncs.length;a<c;a++)d=(b=this._processFuncs[a])(d);if(this.newline){let a=["win","windows","dos","\r\n"].includes(this.newline.toString().toLowerCase())?new o:new p,b=d.pipe(a);return d.on("error",a=>b.emit("error",a)),b}return d}transform(a){this._transforms.push(a)}processFunc(a){this._processFuncs.push(a)}stream(a,b,c){let d,e,f=this.getTransferEncoding(),g=!1,h=a=>{g||(g=!0,c(a))},i=()=>{let c=0,d=()=>{if(c>=this.childNodes.length)return a.write("\r\n--"+this.boundary+"--\r\n"),h();let e=this.childNodes[c++];a.write((c>1?"\r\n":"")+"--"+this.boundary+"\r\n"),e.stream(a,b,a=>{if(a)return h(a);setImmediate(d)})};if(!this.multipart)return h();setImmediate(d)},l=()=>{if(!this.content)return setImmediate(i);{if("[object Error]"===Object.prototype.toString.call(this.content))return h(this.content);"function"==typeof this.content.pipe&&(this.content.removeListener("error",this._contentErrorHandler),this._contentErrorHandler=a=>h(a),this.content.once("error",this._contentErrorHandler));let c=()=>{["quoted-printable","base64"].includes(f)?((d=new("base64"===f?k:j).Encoder(b)).pipe(a,{end:!1}),d.once("end",i),d.once("error",a=>h(a)),(e=this._getStream(this.content)).pipe(d)):((e=this._getStream(this.content)).pipe(a,{end:!1}),e.once("end",i)),e.once("error",a=>h(a))};if(this.content._resolve){let a=[],b=0,d=!1,e=this._getStream(this.content);e.on("error",a=>{d||(d=!0,h(a))}),e.on("readable",()=>{let c;for(;null!==(c=e.read());)a.push(c),b+=c.length}),e.on("end",()=>{d||(d=!0,this.content._resolve=!1,this.content._resolvedValue=Buffer.concat(a,b),setImmediate(c))})}else setImmediate(c);return}};this._raw?setImmediate(()=>{if("[object Error]"===Object.prototype.toString.call(this._raw))return h(this._raw);"function"==typeof this._raw.pipe&&this._raw.removeListener("error",this._contentErrorHandler);let b=this._getStream(this._raw);b.pipe(a,{end:!1}),b.on("error",b=>a.emit("error",b)),b.on("end",i)}):(a.write(this.buildHeaders()+"\r\n\r\n"),setImmediate(l))}setEnvelope(a){let b;this._envelope={from:!1,to:[]},a.from&&(b=[],this._convertAddresses(this._parseAddresses(a.from),b),(b=b.filter(a=>a&&a.address)).length&&b[0]&&(this._envelope.from=b[0].address)),["to","cc","bcc"].forEach(b=>{a[b]&&this._convertAddresses(this._parseAddresses(a[b]),this._envelope.to)}),this._envelope.to=this._envelope.to.map(a=>a.address).filter(a=>a);let c=["to","cc","bcc","from"];return Object.keys(a).forEach(b=>{c.includes(b)||(this._envelope[b]=a[b])}),this}getAddresses(){let a={};return this._headers.forEach(b=>{let c=b.key.toLowerCase();["from","sender","reply-to","to","cc","bcc"].includes(c)&&(Array.isArray(a[c])||(a[c]=[]),this._convertAddresses(this._parseAddresses(b.value),a[c]))}),a}getEnvelope(){if(this._envelope)return this._envelope;let a={from:!1,to:[]};return this._headers.forEach(b=>{let c=[];"From"===b.key||!a.from&&["Reply-To","Sender"].includes(b.key)?(this._convertAddresses(this._parseAddresses(b.value),c),c.length&&c[0]&&(a.from=c[0].address)):["To","Cc","Bcc"].includes(b.key)&&this._convertAddresses(this._parseAddresses(b.value),a.to)}),a.to=a.to.map(a=>a.address),a}messageId(){let a=this.getHeader("Message-ID");return a||(a=this._generateMessageId(),this.setHeader("Message-ID",a)),a}setRaw(a){return this._raw=a,this._raw&&"function"==typeof this._raw.pipe&&(this._contentErrorHandler=a=>{this._raw.removeListener("error",this._contentErrorHandler),this._raw=a},this._raw.once("error",this._contentErrorHandler)),this}_getStream(a){let b;return a._resolvedValue?(b=new g,setImmediate(()=>{try{b.end(a._resolvedValue)}catch(a){b.emit("error",a)}}),b):"function"==typeof a.pipe?a:a&&"string"==typeof a.path&&!a.href?this.disableFileAccess?(b=new g,setImmediate(()=>b.emit("error",Error("File access rejected for "+a.path))),b):e.createReadStream(a.path):a&&"string"==typeof a.href?this.disableUrlAccess?(b=new g,setImmediate(()=>b.emit("error",Error("Url access rejected for "+a.href))),b):m(a.href,{headers:a.httpHeaders}):(b=new g,setImmediate(()=>{try{b.end(a||"")}catch(a){b.emit("error",a)}}),b)}_parseAddresses(a){return[].concat.apply([],[].concat(a).map(a=>a&&a.address?(a.address=this._normalizeAddress(a.address),a.name=a.name||"",[a]):l(a)))}_normalizeHeaderKey(a){return a=(a||"").toString().replace(/\r?\n|\r/g," ").trim().toLowerCase().replace(/^X-SMTPAPI$|^(MIME|DKIM|ARC|BIMI)\b|^[a-z]|-(SPF|FBL|ID|MD5)$|-[a-z]/gi,a=>a.toUpperCase()).replace(/^Content-Features$/i,"Content-features")}_handleContentType(a){this.contentType=a.value.trim().toLowerCase(),this.multipart=!!/^multipart\//i.test(this.contentType)&&this.contentType.substr(this.contentType.indexOf("/")+1),this.multipart?this.boundary=a.params.boundary=a.params.boundary||this.boundary||this._generateBoundary():this.boundary=!1}_generateBoundary(){return this.rootNode.boundaryPrefix+"-"+this.rootNode.baseBoundary+"-Part_"+this._nodeId}_encodeHeaderValue(a,b){switch(a=this._normalizeHeaderKey(a)){case"From":case"Sender":case"To":case"Cc":case"Bcc":case"Reply-To":return this._convertAddresses(this._parseAddresses(b));case"Message-ID":case"In-Reply-To":case"Content-Id":return"<"!==(b=(b||"").toString().replace(/\r?\n|\r/g," ")).charAt(0)&&(b="<"+b),">"!==b.charAt(b.length-1)&&(b+=">"),b;case"References":return(b=[].concat.apply([],[].concat(b||"").map(a=>(a=(a||"").toString().replace(/\r?\n|\r/g," ").trim()).replace(/<[^>]*>/g,a=>a.replace(/\s/g,"")).split(/\s+/))).map(a=>("<"!==a.charAt(0)&&(a="<"+a),">"!==a.charAt(a.length-1)&&(a+=">"),a))).join(" ").trim();case"Date":if("[object Date]"===Object.prototype.toString.call(b))return b.toUTCString().replace(/GMT/,"+0000");return b=(b||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(b);case"Content-Type":case"Content-Disposition":return(b||"").toString().replace(/\r?\n|\r/g," ");default:return b=(b||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(b)}}_convertAddresses(a,b){let c=[];return b=b||[],[].concat(a||[]).forEach(a=>{if(a.address)a.address=this._normalizeAddress(a.address),a.name?a.name&&c.push(`${this._encodeAddressName(a.name)} <${a.address}>`):c.push(a.address.indexOf(" ")>=0?`<${a.address}>`:`${a.address}`),a.address&&!b.filter(b=>b.address===a.address).length&&b.push(a);else if(a.group){let d=(a.group.length?this._convertAddresses(a.group,b):"").trim();c.push(`${this._encodeAddressName(a.name)}:${d};`)}}),c.join(", ")}_normalizeAddress(a){let b,c=(a=(a||"").toString().replace(/[\x00-\x1F<>]+/g," ").trim()).lastIndexOf("@");if(c<0)return a;let d=a.substr(0,c),e=a.substr(c+1);try{b=f.toASCII(e.toLowerCase())}catch(a){}return d.indexOf(" ")>=0&&('"'!==d.charAt(0)&&(d='"'+d),'"'!==d.substr(-1)&&(d+='"')),`${d}@${b}`}_encodeAddressName(a){if(!/^[\w ]*$/.test(a))if(/^[\x20-\x7e]*$/.test(a))return'"'+a.replace(/([\\"])/g,"\\$1")+'"';else return i.encodeWord(a,this._getTextEncoding(a),52);return a}_encodeWords(a){return i.encodeWords(a,this._getTextEncoding(a),52,!0)}_getTextEncoding(a){a=(a||"").toString();let b=this.textEncoding;return b||(b=(a.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\u0080-\uFFFF]/g)||[]).length<(a.match(/[a-z]/gi)||[]).length?"Q":"B"),b}_generateMessageId(){return"<"+[2,2,2,6].reduce((a,b)=>a+"-"+d.randomBytes(b).toString("hex"),d.randomBytes(4).toString("hex"))+"@"+(this.getEnvelope().from||this.hostname||"localhost").split("@").pop()+">"}}a.exports=q},7575:(a,b,c)=>{"use strict";let d=c(91645),e=c(34631),f=c(79551);a.exports=function a(b,c,g,h){let i,j,k,l=f.parse(b);i={host:l.hostname,port:Number(l.port)?Number(l.port):"https:"===l.protocol?443:80},"https:"===l.protocol?(i.rejectUnauthorized=!1,j=e.connect.bind(e)):j=d.connect.bind(d);let m=!1,n=a=>{if(!m){m=!0;try{k.destroy()}catch(a){}h(a)}},o=()=>{let a=Error("Proxy socket timed out");a.code="ETIMEDOUT",n(a)};(k=j(i,()=>{if(m)return;let a={Host:g+":"+c,Connection:"close"};l.auth&&(a["Proxy-Authorization"]="Basic "+Buffer.from(l.auth).toString("base64")),k.write("CONNECT "+g+":"+c+" HTTP/1.1\r\n"+Object.keys(a).map(b=>b+": "+a[b]).join("\r\n")+"\r\n\r\n");let b="",d=a=>{let c,e;if(!m&&(b+=a.toString("binary"),c=b.match(/\r\n\r\n/))){if(k.removeListener("data",d),e=b.substr(c.index+c[0].length),b=b.substr(0,c.index),e&&k.unshift(Buffer.from(e,"binary")),m=!0,!(c=b.match(/^HTTP\/\d+\.\d+ (\d+)/i))||"2"!==(c[1]||"").charAt(0)){try{k.destroy()}catch(a){}return h(Error("Invalid response from proxy"+(c&&": "+c[1]||"")))}return k.removeListener("error",n),k.removeListener("timeout",o),k.setTimeout(0),h(null,k)}};k.on("data",d)})).setTimeout(a.timeout||3e4),k.on("timeout",o),k.once("error",n)}},8346:(a,b,c)=>{"use strict";let d=c(94735),e=c(89231),f=c(20068),g=c(16435),h=c(91292),i=c(49074);class j extends d{constructor(a){let b;super(),"string"==typeof(a=a||{})&&(a={url:a});let c=a.service;"function"==typeof a.getSocket&&(this.getSocket=a.getSocket),a.url&&(b=h.parseConnectionUrl(a.url),c=c||b.service),this.options=h.assign(!1,a,b,c&&g(c)),this.options.maxConnections=this.options.maxConnections||5,this.options.maxMessages=this.options.maxMessages||100,this.logger=h.getLogger(this.options,{component:this.options.component||"smtp-pool"});let d=new f(this.options);this.name="SMTP (pool)",this.version=i.version+"[client:"+d.version+"]",this._rateLimit={counter:0,timeout:null,waiting:[],checkpoint:!1,delta:Number(this.options.rateDelta)||1e3,limit:Number(this.options.rateLimit)||0},this._closed=!1,this._queue=[],this._connections=[],this._connectionCounter=0,this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}getSocket(a,b){return setImmediate(()=>b(null,!1))}send(a,b){return!this._closed&&(this._queue.push({mail:a,requeueAttempts:0,callback:b}),this.idling&&this._queue.length>=this.options.maxConnections&&(this.idling=!1),setImmediate(()=>this._processMessages()),!0)}close(){let a,b=this._connections.length;if(this._closed=!0,clearTimeout(this._rateLimit.timeout),!b&&!this._queue.length)return;for(let c=b-1;c>=0;c--)this._connections[c]&&this._connections[c].available&&((a=this._connections[c]).close(),this.logger.info({tnx:"connection",cid:a.id,action:"removed"},"Connection #%s removed",a.id));if(b&&!this._connections.length&&this.logger.debug({tnx:"connection"},"All connections removed"),!this._queue.length)return;let c=()=>{if(!this._queue.length)return void this.logger.debug({tnx:"connection"},"Pending queue entries cleared");let b=this._queue.shift();if(b&&"function"==typeof b.callback)try{b.callback(Error("Connection pool was closed"))}catch(b){this.logger.error({err:b,tnx:"callback",cid:a.id},"Callback error for #%s: %s",a.id,b.message)}setImmediate(c)};setImmediate(c)}_processMessages(){let a,b,c;if(this._closed)return;if(!this._queue.length){this.idling||(this.idling=!0,this.emit("idle"));return}for(b=0,c=this._connections.length;b<c;b++)if(this._connections[b].available){a=this._connections[b];break}if(!a&&this._connections.length<this.options.maxConnections&&(a=this._createConnection()),!a){this.idling=!1;return}!this.idling&&this._queue.length<this.options.maxConnections&&(this.idling=!0,this.emit("idle"));let d=a.queueEntry=this._queue.shift();d.messageId=(a.queueEntry.mail.message.getHeader("message-id")||"").replace(/[<>\s]/g,""),a.available=!1,this.logger.debug({tnx:"pool",cid:a.id,messageId:d.messageId,action:"assign"},"Assigned message <%s> to #%s (%s)",d.messageId,a.id,a.messages+1),this._rateLimit.limit&&(this._rateLimit.counter++,this._rateLimit.checkpoint||(this._rateLimit.checkpoint=Date.now())),a.send(d.mail,(b,c)=>{if(d===a.queueEntry){try{d.callback(b,c)}catch(b){this.logger.error({err:b,tnx:"callback",cid:a.id},"Callback error for #%s: %s",a.id,b.message)}a.queueEntry=!1}})}_createConnection(){let a=new e(this);return a.id=++this._connectionCounter,this.logger.info({tnx:"pool",cid:a.id,action:"conection"},"Created new pool resource #%s",a.id),a.on("available",()=>{this.logger.debug({tnx:"connection",cid:a.id,action:"available"},"Connection #%s became available",a.id),this._closed?this.close():this._processMessages()}),a.once("error",b=>{if("EMAXLIMIT"!==b.code?this.logger.error({err:b,tnx:"pool",cid:a.id},"Pool Error for #%s: %s",a.id,b.message):this.logger.debug({tnx:"pool",cid:a.id,action:"maxlimit"},"Max messages limit exchausted for #%s",a.id),a.queueEntry){try{a.queueEntry.callback(b)}catch(b){this.logger.error({err:b,tnx:"callback",cid:a.id},"Callback error for #%s: %s",a.id,b.message)}a.queueEntry=!1}this._removeConnection(a),this._continueProcessing()}),a.once("close",()=>{this.logger.info({tnx:"connection",cid:a.id,action:"closed"},"Connection #%s was closed",a.id),this._removeConnection(a),a.queueEntry?setTimeout(()=>{a.queueEntry&&(this._shouldRequeuOnConnectionClose(a.queueEntry)?this._requeueEntryOnConnectionClose(a):this._failDeliveryOnConnectionClose(a)),this._continueProcessing()},50):(this._closed||!this.idling||this._connections.length||this.emit("clear"),this._continueProcessing())}),this._connections.push(a),a}_shouldRequeuOnConnectionClose(a){return void 0===this.options.maxRequeues||this.options.maxRequeues<0||a.requeueAttempts<this.options.maxRequeues}_failDeliveryOnConnectionClose(a){if(a.queueEntry&&a.queueEntry.callback){try{a.queueEntry.callback(Error("Reached maximum number of retries after connection was closed"))}catch(b){this.logger.error({err:b,tnx:"callback",messageId:a.queueEntry.messageId,cid:a.id},"Callback error for #%s: %s",a.id,b.message)}a.queueEntry=!1}}_requeueEntryOnConnectionClose(a){a.queueEntry.requeueAttempts=a.queueEntry.requeueAttempts+1,this.logger.debug({tnx:"pool",cid:a.id,messageId:a.queueEntry.messageId,action:"requeue"},"Re-queued message <%s> for #%s. Attempt: #%s",a.queueEntry.messageId,a.id,a.queueEntry.requeueAttempts),this._queue.unshift(a.queueEntry),a.queueEntry=!1}_continueProcessing(){this._closed?this.close():setTimeout(()=>this._processMessages(),100)}_removeConnection(a){let b=this._connections.indexOf(a);-1!==b&&this._connections.splice(b,1)}_checkRateLimit(a){if(!this._rateLimit.limit)return a();let b=Date.now();return this._rateLimit.counter<this._rateLimit.limit?a():(this._rateLimit.waiting.push(a),this._rateLimit.checkpoint<=b-this._rateLimit.delta)?this._clearRateLimit():void(!this._rateLimit.timeout&&(this._rateLimit.timeout=setTimeout(()=>this._clearRateLimit(),this._rateLimit.delta-(b-this._rateLimit.checkpoint)),this._rateLimit.checkpoint=b))}_clearRateLimit(){for(clearTimeout(this._rateLimit.timeout),this._rateLimit.timeout=null,this._rateLimit.counter=0,this._rateLimit.checkpoint=!1;this._rateLimit.waiting.length;)setImmediate(this._rateLimit.waiting.shift())}isIdle(){return this.idling}verify(a){let b;a||(b=new Promise((b,c)=>{a=h.callbackPromise(b,c)}));let c=new e(this).auth;return this.getSocket(this.options,(b,d)=>{if(b)return a(b);let e=this.options;d&&d.connection&&(this.logger.info({tnx:"proxy",remoteAddress:d.connection.remoteAddress,remotePort:d.connection.remotePort,destHost:e.host||"",destPort:e.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",d.connection.remoteAddress,d.connection.remotePort,e.host||"",e.port||""),e=h.assign(!1,e),Object.keys(d).forEach(a=>{e[a]=d[a]}));let g=new f(e),i=!1;g.once("error",b=>{if(!i)return i=!0,g.close(),a(b)}),g.once("end",()=>{if(!i)return i=!0,a(Error("Connection closed"))});let j=()=>{if(!i)return i=!0,g.quit(),a(null,!0)};g.connect(()=>{if(!i)if(c&&(g.allowsAuth||e.forceAuth))g.login(c,b=>{if(!i){if(b)return i=!0,g.close(),a(b);j()}});else if(!c&&g.allowsAuth&&e.forceAuth){let b=Error("Authentication info was not provided");return b.code="NoAuth",i=!0,g.close(),a(b)}else j()})}),b}}a.exports=j},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:a=>{"use strict";a.exports=require("dns")},16435:(a,b,c)=>{"use strict";let d=c(58417),e={};function f(a){return a.replace(/[^a-zA-Z0-9.-]/g,"").toLowerCase()}function g(a){let b=["domains","aliases"],c={};return Object.keys(a).forEach(d=>{0>b.indexOf(d)&&(c[d]=a[d])}),c}Object.keys(d).forEach(a=>{let b=d[a];e[f(a)]=g(b),[].concat(b.aliases||[]).forEach(a=>{e[f(a)]=g(b)}),[].concat(b.domains||[]).forEach(a=>{e[f(a)]=g(b)})}),a.exports=function(a){return e[a=f(a.split("@").pop())]||!1}},17704:(a,b,c)=>{"use strict";let d=c(91292),e=c(7180),f=c(18199);class g{constructor(a,b){this.mailer=a,this.data={},this.message=null,b=b||{};let c=a.options||{},d=a._defaults||{};Object.keys(b).forEach(a=>{this.data[a]=b[a]}),this.data.headers=this.data.headers||{},Object.keys(d).forEach(a=>{a in this.data?"headers"===a&&Object.keys(d.headers).forEach(a=>{a in this.data.headers||(this.data.headers[a]=d.headers[a])}):this.data[a]=d[a]}),["disableFileAccess","disableUrlAccess","normalizeHeaderKey"].forEach(a=>{a in c&&(this.data[a]=c[a])})}resolveContent(...a){return d.resolveContent(...a)}resolveAll(a){let b=[[this.data,"html"],[this.data,"text"],[this.data,"watchHtml"],[this.data,"amp"],[this.data,"icalEvent"]];this.data.alternatives&&this.data.alternatives.length&&this.data.alternatives.forEach((a,c)=>{b.push([this.data.alternatives,c])}),this.data.attachments&&this.data.attachments.length&&this.data.attachments.forEach((a,c)=>{!a.filename&&(a.filename=(a.path||a.href||"").split("/").pop().split("?").shift()||"attachment-"+(c+1),0>a.filename.indexOf(".")&&(a.filename+="."+f.detectExtension(a.contentType))),a.contentType||(a.contentType=f.detectMimeType(a.filename||a.path||a.href||"bin")),b.push([this.data.attachments,c])});let c=new e;["from","to","cc","bcc","sender","replyTo"].forEach(a=>{let b;this.message?b=[].concat(c._parseAddresses(this.message.getHeader("replyTo"===a?"reply-to":a))||[]):this.data[a]&&(b=[].concat(c._parseAddresses(this.data[a])||[])),b&&b.length?this.data[a]=b:a in this.data&&(this.data[a]=null)}),["from","sender"].forEach(a=>{this.data[a]&&(this.data[a]=this.data[a].shift())});let g=0,h=()=>{if(g>=b.length)return a(null,this.data);let c=b[g++];if(!c[0]||!c[0][c[1]])return h();d.resolveContent(...c,(b,d)=>{if(b)return a(b);let e={content:d};c[0][c[1]]&&"object"==typeof c[0][c[1]]&&!Buffer.isBuffer(c[0][c[1]])&&Object.keys(c[0][c[1]]).forEach(a=>{a in e||["content","path","href","raw"].includes(a)||(e[a]=c[0][c[1]][a])}),c[0][c[1]]=e,h()})};setImmediate(()=>h())}normalize(a){let b=this.data.envelope||this.message.getEnvelope(),c=this.message.messageId();this.resolveAll((d,e)=>d?a(d):(e.envelope=b,e.messageId=c,["html","text","watchHtml","amp"].forEach(a=>{e[a]&&e[a].content&&("string"==typeof e[a].content?e[a]=e[a].content:Buffer.isBuffer(e[a].content)&&(e[a]=e[a].content.toString()))}),e.icalEvent&&Buffer.isBuffer(e.icalEvent.content)&&(e.icalEvent.content=e.icalEvent.content.toString("base64"),e.icalEvent.encoding="base64"),e.alternatives&&e.alternatives.length&&e.alternatives.forEach(a=>{a&&a.content&&Buffer.isBuffer(a.content)&&(a.content=a.content.toString("base64"),a.encoding="base64")}),e.attachments&&e.attachments.length&&e.attachments.forEach(a=>{a&&a.content&&Buffer.isBuffer(a.content)&&(a.content=a.content.toString("base64"),a.encoding="base64")}),e.normalizedHeaders={},Object.keys(e.headers||{}).forEach(a=>{let b=[].concat(e.headers[a]||[]).shift();(b=b&&b.value||b)&&(["references","in-reply-to","message-id","content-id"].includes(a)&&(b=this.message._encodeHeaderValue(a,b)),e.normalizedHeaders[a]=b)}),e.list&&"object"==typeof e.list&&this._getListHeaders(e.list).forEach(a=>{e.normalizedHeaders[a.key]=a.value.map(a=>a&&a.value||a).join(", ")}),e.references&&(e.normalizedHeaders.references=this.message._encodeHeaderValue("references",e.references)),e.inReplyTo&&(e.normalizedHeaders["in-reply-to"]=this.message._encodeHeaderValue("in-reply-to",e.inReplyTo)),a(null,e)))}setMailerHeader(){this.message&&this.data.xMailer&&this.message.setHeader("X-Mailer",this.data.xMailer)}setPriorityHeaders(){if(this.message&&this.data.priority)switch((this.data.priority||"").toString().toLowerCase()){case"high":this.message.setHeader("X-Priority","1 (Highest)"),this.message.setHeader("X-MSMail-Priority","High"),this.message.setHeader("Importance","High");break;case"low":this.message.setHeader("X-Priority","5 (Lowest)"),this.message.setHeader("X-MSMail-Priority","Low"),this.message.setHeader("Importance","Low")}}setListHeaders(){this.message&&this.data.list&&"object"==typeof this.data.list&&this.data.list&&"object"==typeof this.data.list&&this._getListHeaders(this.data.list).forEach(a=>{a.value.forEach(b=>{this.message.addHeader(a.key,b)})})}_getListHeaders(a){return Object.keys(a).map(b=>({key:"list-"+b.toLowerCase().trim(),value:[].concat(a[b]||[]).map(a=>({prepared:!0,foldLines:!0,value:[].concat(a||[]).map(a=>{if("string"==typeof a&&(a={url:a}),a&&a.url){if("id"===b.toLowerCase().trim()){let b=a.comment||"";return b=f.isPlainText(b)?'"'+b+'"':f.encodeWord(b),(a.comment?b+" ":"")+this._formatListUrl(a.url).replace(/^<[^:]+\/{,2}/,"")}let c=a.comment||"";return f.isPlainText(c)||(c=f.encodeWord(c)),this._formatListUrl(a.url)+(a.comment?" ("+c+")":"")}return""}).filter(a=>a).join(", ")}))}))}_formatListUrl(a){return(a=a.replace(/[\s<]+|[\s>]+/g,""),/^(https?|mailto|ftp):/.test(a))?"<"+a+">":/^[^@]+@[^@]+$/.test(a)?"<mailto:"+a+">":"<http://"+a+">"}}a.exports=g},18199:(a,b,c)=>{"use strict";let d=c(89066),e=c(63356),f=c(50143);a.exports={isPlainText:(a,b)=>!("string"!=typeof a||(b?/[\x00-\x08\x0b\x0c\x0e-\x1f"\u0080-\uFFFF]/:/[\x00-\x08\x0b\x0c\x0e-\x1f\u0080-\uFFFF]/).test(a)),hasLongerLines:(a,b)=>a.length>131072||RegExp("^.{"+(b+1)+",}","m").test(a),encodeWord(a,b,c){let f;b=(b||"Q").toString().toUpperCase().trim().charAt(0);let g="UTF-8";if((c=c||0)&&c>7+g.length&&(c-=7+g.length),"Q"===b?f=e.encode(a).replace(/[^a-z0-9!*+\-/=]/gi,a=>{let b=a.charCodeAt(0).toString(16).toUpperCase();return" "===a?"_":"="+(1===b.length?"0"+b:b)}):"B"===b&&(f="string"==typeof a?a:d.encode(a),c=c?Math.max(3,(c-c%4)/4*3):0),c&&("B"!==b?f:d.encode(a)).length>c)if("Q"===b)f=this.splitMimeEncodedString(f,c).join("?= =?"+g+"?"+b+"?");else{let a=[],e="";for(let b=0,g=f.length;b<g;b++){let h=f.charAt(b);/[\ud83c\ud83d\ud83e]/.test(h)&&b<g-1&&(h+=f.charAt(++b)),Buffer.byteLength(e+h)<=c||0===b?e+=h:(a.push(d.encode(e)),e=h)}e&&a.push(d.encode(e)),f=a.length>1?a.join("?= =?"+g+"?"+b+"?"):a.join("")}else"B"===b&&(f=d.encode(a));return"=?"+g+"?"+b+"?"+f+("?="===f.substr(-2)?"":"?=")},encodeWords(a,b,c,d){c=c||0;let e=a.match(/(?:^|\s)([^\s]*["\u0080-\uFFFF])/);if(!e)return a;if(d)return this.encodeWord(a,b,c);let f=a.match(/(["\u0080-\uFFFF][^\s]*)[^"\u0080-\uFFFF]*$/);if(!f)return a;let g=e.index+(e[0].match(/[^\s]/)||{index:0}).index,h=f.index+(f[1]||"").length;return(g?a.substr(0,g):"")+this.encodeWord(a.substring(g,h),b||"Q",c)+(h<a.length?a.substr(h):"")},buildHeaderValue(a){let b=[];return Object.keys(a.params||{}).forEach(c=>{let d=a.params[c];!this.isPlainText(d,!0)||d.length>=75?this.buildHeaderParam(c,d,50).forEach(a=>{/[\s"\\;:/=(),<>@[\]?]|^[-']|'$/.test(a.value)&&"*"!==a.key.substr(-1)?b.push(a.key+"="+JSON.stringify(a.value)):b.push(a.key+"="+a.value)}):/[\s'"\\;:/=(),<>@[\]?]|^-/.test(d)?b.push(c+"="+JSON.stringify(d)):b.push(c+"="+d)}),a.value+(b.length?"; "+b.join("; "):"")},buildHeaderParam(a,b,c){let d,e,f,g,h,i,j=[],k="string"==typeof b?b:(b||"").toString(),l=0;if(c=c||50,this.isPlainText(b,!0)){if(k.length<=c)return[{key:a,value:k}];(k=k.replace(RegExp(".{"+c+"}","g"),a=>(j.push({line:a}),"")))&&j.push({line:k})}else{if(/[\uD800-\uDBFF]/.test(k)){for(h=0,d=[],i=k.length;h<i;h++)(f=(e=k.charAt(h)).charCodeAt(0))>=55296&&f<=56319&&h<i-1?(e+=k.charAt(h+1),d.push(e),h++):d.push(e);k=d}g="utf-8''";let a=!0;for(h=0,l=0,i=k.length;h<i;h++){if(e=k[h],a)e=this.safeEncodeURIComponent(e);else if((e=" "===e?e:this.safeEncodeURIComponent(e))!==k[h])if((this.safeEncodeURIComponent(g)+e).length>=c)j.push({line:g,encoded:a}),g="",l=h-1;else{a=!0,h=l,g="";continue}(g+e).length>=c?(j.push({line:g,encoded:a}),g=e=" "===k[h]?" ":this.safeEncodeURIComponent(k[h]),e===k[h]?(a=!1,l=h-1):a=!0):g+=e}g&&j.push({line:g,encoded:a})}return j.map((b,c)=>({key:a+"*"+c+(b.encoded?"*":""),value:b.line}))},parseHeaderValue(a){let b,c={value:!1,params:{}},d=!1,e="",f="value",g=!1,h=!1;for(let i=0,j=a.length;i<j;i++)if(b=a.charAt(i),"key"===f){if("="===b){d=e.trim().toLowerCase(),f="value",e="";continue}e+=b}else{if(h)e+=b;else if("\\"===b){h=!0;continue}else g&&b===g?g=!1:g||'"'!==b?g||";"!==b?e+=b:(!1===d?c.value=e.trim():c.params[d]=e.trim(),f="key",e=""):g=b;h=!1}return"value"===f?!1===d?c.value=e.trim():c.params[d]=e.trim():e.trim()&&(c.params[e.trim().toLowerCase()]=""),Object.keys(c.params).forEach(a=>{let b,d,e,f;(e=a.match(/(\*(\d+)|\*(\d+)\*|\*)$/))&&(b=a.substr(0,e.index),d=Number(e[2]||e[3])||0,c.params[b]&&"object"==typeof c.params[b]||(c.params[b]={charset:!1,values:[]}),f=c.params[a],0===d&&"*"===e[0].substr(-1)&&(e=f.match(/^([^']*)'[^']*'(.*)$/))&&(c.params[b].charset=e[1]||"iso-8859-1",f=e[2]),c.params[b].values[d]=f,delete c.params[a])}),Object.keys(c.params).forEach(a=>{let b;c.params[a]&&Array.isArray(c.params[a].values)&&(b=c.params[a].values.map(a=>a||"").join(""),c.params[a].charset?c.params[a]="=?"+c.params[a].charset+"?Q?"+b.replace(/[=?_\s]/g,a=>{let b=a.charCodeAt(0).toString(16);return" "===a?"_":"%"+(b.length<2?"0":"")+b}).replace(/%/g,"=")+"?=":c.params[a]=b)}),c},detectExtension:a=>f.detectExtension(a),detectMimeType:a=>f.detectMimeType(a),foldLines(a,b,c){a=(a||"").toString(),b=b||76;let d=0,e=a.length,f="",g,h;for(;d<e;){if((g=a.substr(d,b)).length<b){f+=g;break}if(h=g.match(/^[^\n\r]*(\r?\n|\r)/)){f+=g=h[0],d+=g.length;continue}(h=g.match(/(\s+)[^\s]*$/))&&h[0].length-(c?(h[1]||"").length:0)<g.length?g=g.substr(0,g.length-(h[0].length-(c?(h[1]||"").length:0))):(h=a.substr(d+g.length).match(/^[^\s]+(\s*)/))&&(g+=h[0].substr(0,h[0].length-(c?0:(h[1]||"").length))),f+=g,(d+=g.length)<e&&(f+="\r\n")}return f},splitMimeEncodedString:(a,b)=>{let c,d,e,f,g=[];for(b=Math.max(b||0,12);a.length;){for((d=(c=a.substr(0,b)).match(/[=][0-9A-F]?$/i))&&(c=c.substr(0,d.index)),f=!1;!f;)f=!0,(d=a.substr(c.length).match(/^[=]([0-9A-F]{2})/i))&&(e=parseInt(d[1],16))<194&&e>127&&(c=c.substr(0,c.length-3),f=!1);c.length&&g.push(c),a=a.substr(c.length)}return g},encodeURICharComponent:a=>{let b="",c=a.charCodeAt(0).toString(16).toUpperCase();if(c.length%2&&(c="0"+c),c.length>2)for(let a=0,d=c.length/2;a<d;a++)b+="%"+c.substr(a,2);else b+="%"+c;return b},safeEncodeURIComponent(a){a=(a||"").toString();try{a=encodeURIComponent(a)}catch(b){return a.replace(/[^\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]+/g,"")}return a.replace(/[\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]/g,a=>this.encodeURICharComponent(a))}}},20068:(a,b,c)=>{"use strict";let d=c(49074),e=c(94735).EventEmitter,f=c(91645),g=c(34631),h=c(21820),i=c(55511),j=c(43519),k=c(27910).PassThrough,l=c(91292);class m extends e{constructor(a){super(a),this.id=i.randomBytes(8).toString("base64").replace(/\W/g,""),this.stage="init",this.options=a||{},this.secureConnection=!!this.options.secure,this.alreadySecured=!!this.options.secured,this.port=Number(this.options.port)||(this.secureConnection?465:587),this.host=this.options.host||"localhost",this.servername=this.options.servername?this.options.servername:!f.isIP(this.host)&&this.host,this.allowInternalNetworkInterfaces=this.options.allowInternalNetworkInterfaces||!1,void 0===this.options.secure&&465===this.port&&(this.secureConnection=!0),this.name=this.options.name||this._getHostname(),this.logger=l.getLogger(this.options,{component:this.options.component||"smtp-connection",sid:this.id}),this.customAuth=new Map,Object.keys(this.options.customAuth||{}).forEach(a=>{let b=(a||"").toString().trim().toUpperCase();b&&this.customAuth.set(b,this.options.customAuth[a])}),this.version=d.version,this.authenticated=!1,this.destroyed=!1,this.secure=!!this.secureConnection,this._remainder="",this._responseQueue=[],this.lastServerResponse=!1,this._socket=!1,this._supportedAuth=[],this.allowsAuth=!1,this._envelope=!1,this._supportedExtensions=[],this._maxAllowedSize=0,this._responseActions=[],this._recipientQueue=[],this._greetingTimeout=!1,this._connectionTimeout=!1,this._destroyed=!1,this._closing=!1,this._onSocketData=a=>this._onData(a),this._onSocketError=a=>this._onError(a,"ESOCKET",!1,"CONN"),this._onSocketClose=()=>this._onClose(),this._onSocketEnd=()=>this._onEnd(),this._onSocketTimeout=()=>this._onTimeout()}connect(a){if("function"==typeof a){this.once("connect",()=>{this.logger.debug({tnx:"smtp"},"SMTP handshake finished"),a()});let b=this._isDestroyedMessage("connect");if(b)return a(this._formatError(b,"ECONNECTION",!1,"CONN"))}let b={port:this.port,host:this.host,allowInternalNetworkInterfaces:this.allowInternalNetworkInterfaces,timeout:this.options.dnsTimeout||3e4};this.options.localAddress&&(b.localAddress=this.options.localAddress);let c=()=>{this._connectionTimeout=setTimeout(()=>{this._onError("Connection timeout","ETIMEDOUT",!1,"CONN")},this.options.connectionTimeout||12e4),this._socket.on("error",this._onSocketError)};if(this.options.connection){this._socket=this.options.connection,c(),this.secureConnection&&!this.alreadySecured?setImmediate(()=>this._upgradeConnection(a=>{if(a)return void this._onError(Error("Error initiating TLS - "+(a.message||a)),"ETLS",!1,"CONN");this._onConnect()})):setImmediate(()=>this._onConnect());return}return this.options.socket?(this._socket=this.options.socket,l.resolveHostname(b,(a,d)=>{if(a)return setImmediate(()=>this._onError(a,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:b.host,resolved:d.host,cached:!!d.cached},"Resolved %s as %s [cache %s]",b.host,d.host,d.cached?"hit":"miss"),Object.keys(d).forEach(a=>{"_"!==a.charAt(0)&&d[a]&&(b[a]=d[a])});try{this._socket.connect(this.port,this.host,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),c()}catch(a){return setImmediate(()=>this._onError(a,"ECONNECTION",!1,"CONN"))}})):this.secureConnection?(this.options.tls&&Object.keys(this.options.tls).forEach(a=>{b[a]=this.options.tls[a]}),this.servername&&!b.servername&&(b.servername=this.servername),l.resolveHostname(b,(a,d)=>{if(a)return setImmediate(()=>this._onError(a,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:b.host,resolved:d.host,cached:!!d.cached},"Resolved %s as %s [cache %s]",b.host,d.host,d.cached?"hit":"miss"),Object.keys(d).forEach(a=>{"_"!==a.charAt(0)&&d[a]&&(b[a]=d[a])});try{this._socket=g.connect(b,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),c()}catch(a){return setImmediate(()=>this._onError(a,"ECONNECTION",!1,"CONN"))}})):l.resolveHostname(b,(a,d)=>{if(a)return setImmediate(()=>this._onError(a,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:b.host,resolved:d.host,cached:!!d.cached},"Resolved %s as %s [cache %s]",b.host,d.host,d.cached?"hit":"miss"),Object.keys(d).forEach(a=>{"_"!==a.charAt(0)&&d[a]&&(b[a]=d[a])});try{this._socket=f.connect(b,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),c()}catch(a){return setImmediate(()=>this._onError(a,"ECONNECTION",!1,"CONN"))}})}quit(){this._sendCommand("QUIT"),this._responseActions.push(this.close)}close(){if(clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._responseActions=[],this._closing)return;this._closing=!0;let a="end";"init"===this.stage&&(a="destroy"),this.logger.debug({tnx:"smtp"},'Closing connection to the server using "%s"',a);let b=this._socket&&this._socket.socket||this._socket;if(b&&!b.destroyed)try{b[a]()}catch(a){}this._destroy()}login(a,b){let c=this._isDestroyedMessage("login");if(c)return b(this._formatError(c,"ECONNECTION",!1,"API"));if(this._auth=a||{},this._authMethod=(this._auth.method||"").toString().trim().toUpperCase()||!1,this._authMethod||!this._auth.oauth2||this._auth.credentials?this._authMethod&&("XOAUTH2"!==this._authMethod||this._auth.oauth2)||(this._authMethod=(this._supportedAuth[0]||"PLAIN").toUpperCase().trim()):this._authMethod="XOAUTH2","XOAUTH2"!==this._authMethod&&(!this._auth.credentials||!this._auth.credentials.user||!this._auth.credentials.pass))if(!(this._auth.user&&this._auth.pass||this.customAuth.has(this._authMethod)))return b(this._formatError('Missing credentials for "'+this._authMethod+'"',"EAUTH",!1,"API"));else this._auth.credentials={user:this._auth.user,pass:this._auth.pass,options:this._auth.options};if(this.customAuth.has(this._authMethod)){let a,c=this.customAuth.get(this._authMethod),d=!1,e=()=>{d||(d=!0,this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,b(null,!0))},f=c=>{d||(d=!0,b(this._formatError(c,"EAUTH",a,"AUTH "+this._authMethod)))},g=c({auth:this._auth,method:this._authMethod,extensions:[].concat(this._supportedExtensions),authMethods:[].concat(this._supportedAuth),maxAllowedSize:this._maxAllowedSize||!1,sendCommand:(b,c)=>{let d;return c||(d=new Promise((a,b)=>{c=l.callbackPromise(a,b)})),this._responseActions.push(d=>{a=d;let e=d.match(/^(\d+)(?:\s(\d+\.\d+\.\d+))?\s/),f={command:b,response:d};e?(f.status=Number(e[1])||0,e[2]&&(f.code=e[2]),f.text=d.substr(e[0].length)):(f.text=d,f.status=0),c(null,f)}),setImmediate(()=>this._sendCommand(b)),d},resolve:e,reject:f});g&&"function"==typeof g.catch&&g.then(e).catch(f);return}switch(this._authMethod){case"XOAUTH2":this._handleXOauth2Token(!1,b);return;case"LOGIN":this._responseActions.push(a=>{this._actionAUTH_LOGIN_USER(a,b)}),this._sendCommand("AUTH LOGIN");return;case"PLAIN":this._responseActions.push(a=>{this._actionAUTHComplete(a,b)}),this._sendCommand("AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0"+this._auth.credentials.pass,"utf-8").toString("base64"),"AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0/* secret */","utf-8").toString("base64"));return;case"CRAM-MD5":this._responseActions.push(a=>{this._actionAUTH_CRAM_MD5(a,b)}),this._sendCommand("AUTH CRAM-MD5");return}return b(this._formatError('Unknown authentication method "'+this._authMethod+'"',"EAUTH",!1,"API"))}send(a,b,c){if(!b)return c(this._formatError("Empty message","EMESSAGE",!1,"API"));let d=this._isDestroyedMessage("send message");if(d)return c(this._formatError(d,"ECONNECTION",!1,"API"));if(this._maxAllowedSize&&a.size>this._maxAllowedSize)return setImmediate(()=>{c(this._formatError("Message size larger than allowed "+this._maxAllowedSize,"EMESSAGE",!1,"MAIL FROM"))});let e=!1,f=function(){e||(e=!0,c(...arguments))};"function"==typeof b.on&&b.on("error",a=>f(this._formatError(a,"ESTREAM",!1,"API")));let g=Date.now();this._setEnvelope(a,(a,c)=>{if(a){let c=new k;return"function"==typeof b.pipe?b.pipe(c):(c.write(b),c.end()),f(a)}let d=Date.now(),e=this._createSendStream((a,b)=>a?f(a):(c.envelopeTime=d-g,c.messageTime=Date.now()-d,c.messageSize=e.outByteCount,c.response=b,f(null,c)));"function"==typeof b.pipe?b.pipe(e):(e.write(b),e.end())})}reset(a){this._sendCommand("RSET"),this._responseActions.push(b=>"2"!==b.charAt(0)?a(this._formatError("Could not reset session state. response="+b,"EPROTOCOL",b,"RSET")):(this._envelope=!1,a(null,!0)))}_onConnect(){if(clearTimeout(this._connectionTimeout),this.logger.info({tnx:"network",localAddress:this._socket.localAddress,localPort:this._socket.localPort,remoteAddress:this._socket.remoteAddress,remotePort:this._socket.remotePort},"%s established to %s:%s",this.secure?"Secure connection":"Connection",this._socket.remoteAddress,this._socket.remotePort),this._destroyed)return void this.close();this.stage="connected",this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout),this._socket.removeListener("close",this._onSocketClose),this._socket.removeListener("end",this._onSocketEnd),this._socket.on("data",this._onSocketData),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),this._greetingTimeout=setTimeout(()=>{this._socket&&!this._destroyed&&this._responseActions[0]===this._actionGreeting&&this._onError("Greeting never received","ETIMEDOUT",!1,"CONN")},this.options.greetingTimeout||3e4),this._responseActions.push(this._actionGreeting),this._socket.resume()}_onData(a){let b;if(this._destroyed||!a||!a.length)return;let c=(a||"").toString("binary"),d=(this._remainder+c).split(/\r?\n/);this._remainder=d.pop();for(let a=0,c=d.length;a<c;a++){if(this._responseQueue.length&&(b=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(b.split("\n").pop()))){this._responseQueue[this._responseQueue.length-1]+="\n"+d[a];continue}this._responseQueue.push(d[a])}this._responseQueue.length&&(b=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(b.split("\n").pop()))||this._processResponse()}_onError(a,b,c,d){clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._destroyed||(a=this._formatError(a,b,c,d),this.logger.error(c,a.message),this.emit("error",a),this.close())}_formatError(a,b,c,d){let e;e=/Error\]$/i.test(Object.prototype.toString.call(a))?a:Error(a),b&&"Error"!==b&&(e.code=b),c&&(e.response=c,e.message+=": "+c);let f="string"==typeof c&&Number((c.match(/^\d+/)||[])[0])||!1;return f&&(e.responseCode=f),d&&(e.command=d),e}_onClose(){let a=!1;return(this._remainder&&this._remainder.trim()&&((this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},this._remainder.replace(/\r?\n$/,"")),this.lastServerResponse=a=this._remainder.trim()),this.logger.info({tnx:"network"},"Connection closed"),this.upgrading&&!this._destroyed)?this._onError(Error("Connection closed unexpectedly"),"ETLS",a,"CONN"):![this._actionGreeting,this.close].includes(this._responseActions[0])&&!this._destroyed||/^[45]\d{2}\b/.test(a)?this._onError(Error("Connection closed unexpectedly"),"ECONNECTION",a,"CONN"):void this._destroy()}_onEnd(){this._socket&&!this._socket.destroyed&&this._socket.destroy()}_onTimeout(){return this._onError(Error("Timeout"),"ETIMEDOUT",!1,"CONN")}_destroy(){this._destroyed||(this._destroyed=!0,this.emit("end"))}_upgradeConnection(a){this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout);let b=this._socket,c={socket:this._socket,host:this.host};Object.keys(this.options.tls||{}).forEach(a=>{c[a]=this.options.tls[a]}),this.servername&&!c.servername&&(c.servername=this.servername),this.upgrading=!0;try{this._socket=g.connect(c,()=>(this.secure=!0,this.upgrading=!1,this._socket.on("data",this._onSocketData),b.removeListener("close",this._onSocketClose),b.removeListener("end",this._onSocketEnd),a(null,!0)))}catch(b){return a(b)}this._socket.on("error",this._onSocketError),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),b.resume()}_processResponse(){if(!this._responseQueue.length)return!1;let a=this.lastServerResponse=(this._responseQueue.shift()||"").toString();if(/^\d+-/.test(a.split("\n").pop()))return;(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},a.replace(/\r?\n$/,"")),a.trim()||setImmediate(()=>this._processResponse());let b=this._responseActions.shift();if("function"!=typeof b)return this._onError(Error("Unexpected Response"),"EPROTOCOL",a,"CONN");b.call(this,a),setImmediate(()=>this._processResponse())}_sendCommand(a,b){if(!this._destroyed){if(this._socket.destroyed)return this.close();(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"client"},(b||a||"").toString().replace(/\r?\n$/,"")),this._socket.write(Buffer.from(a+"\r\n","utf-8"))}}_setEnvelope(a,b){let c=[],d=!1;if(this._envelope=a||{},this._envelope.from=(this._envelope.from&&this._envelope.from.address||this._envelope.from||"").toString().trim(),this._envelope.to=[].concat(this._envelope.to||[]).map(a=>(a&&a.address||a||"").toString().trim()),!this._envelope.to.length)return b(this._formatError("No recipients defined","EENVELOPE",!1,"API"));if(this._envelope.from&&/[\r\n<>]/.test(this._envelope.from))return b(this._formatError("Invalid sender "+JSON.stringify(this._envelope.from),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.from)&&(d=!0);for(let a=0,c=this._envelope.to.length;a<c;a++){if(!this._envelope.to[a]||/[\r\n<>]/.test(this._envelope.to[a]))return b(this._formatError("Invalid recipient "+JSON.stringify(this._envelope.to[a]),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.to[a])&&(d=!0)}if(this._envelope.rcptQueue=JSON.parse(JSON.stringify(this._envelope.to||[])),this._envelope.rejected=[],this._envelope.rejectedErrors=[],this._envelope.accepted=[],this._envelope.dsn)try{this._envelope.dsn=this._setDsnEnvelope(this._envelope.dsn)}catch(a){return b(this._formatError("Invalid DSN "+a.message,"EENVELOPE",!1,"API"))}this._responseActions.push(a=>{this._actionMAIL(a,b)}),d&&this._supportedExtensions.includes("SMTPUTF8")&&(c.push("SMTPUTF8"),this._usingSmtpUtf8=!0),this._envelope.use8BitMime&&this._supportedExtensions.includes("8BITMIME")&&(c.push("BODY=8BITMIME"),this._using8BitMime=!0),this._envelope.size&&this._supportedExtensions.includes("SIZE")&&c.push("SIZE="+this._envelope.size),this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.ret&&c.push("RET="+l.encodeXText(this._envelope.dsn.ret)),this._envelope.dsn.envid&&c.push("ENVID="+l.encodeXText(this._envelope.dsn.envid))),this._sendCommand("MAIL FROM:<"+this._envelope.from+">"+(c.length?" "+c.join(" "):""))}_setDsnEnvelope(a){let b=(a.ret||a.return||"").toString().toUpperCase()||null;if(b)switch(b){case"HDRS":case"HEADERS":b="HDRS";break;case"FULL":case"BODY":b="FULL"}if(b&&!["FULL","HDRS"].includes(b))throw Error("ret: "+JSON.stringify(b));let c=(a.envid||a.id||"").toString()||null,d=a.notify||null;if(d){"string"==typeof d&&(d=d.split(","));let a=["NEVER","SUCCESS","FAILURE","DELAY"];if((d=d.map(a=>a.trim().toUpperCase())).filter(b=>!a.includes(b)).length||d.length>1&&d.includes("NEVER"))throw Error("notify: "+JSON.stringify(d.join(",")));d=d.join(",")}let e=(a.recipient||a.orcpt||"").toString()||null;return e&&0>e.indexOf(";")&&(e="rfc822;"+e),{ret:b,envid:c,notify:d,orcpt:e}}_getDsnRcptToArgs(){let a=[];return this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.notify&&a.push("NOTIFY="+l.encodeXText(this._envelope.dsn.notify)),this._envelope.dsn.orcpt&&a.push("ORCPT="+l.encodeXText(this._envelope.dsn.orcpt))),a.length?" "+a.join(" "):""}_createSendStream(a){let b,c=new j;return this.options.lmtp?this._envelope.accepted.forEach((b,c)=>{let d=c===this._envelope.accepted.length-1;this._responseActions.push(c=>{this._actionLMTPStream(b,d,c,a)})}):this._responseActions.push(b=>{this._actionSMTPStream(b,a)}),c.pipe(this._socket,{end:!1}),this.options.debug&&((b=new k).on("readable",()=>{let a;for(;a=b.read();)this.logger.debug({tnx:"message"},a.toString("binary").replace(/\r?\n$/,""))}),c.pipe(b)),c.once("end",()=>{this.logger.info({tnx:"message",inByteCount:c.inByteCount,outByteCount:c.outByteCount},"<%s bytes encoded mime message (source size %s bytes)>",c.outByteCount,c.inByteCount)}),c}_actionGreeting(a){if(clearTimeout(this._greetingTimeout),"220"!==a.substr(0,3))return void this._onError(Error("Invalid greeting. response="+a),"EPROTOCOL",a,"CONN");this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name))}_actionLHLO(a){if("2"!==a.charAt(0))return void this._onError(Error("Invalid LHLO. response="+a),"EPROTOCOL",a,"LHLO");this._actionEHLO(a)}_actionEHLO(a){let b;if("421"===a.substr(0,3))return void this._onError(Error("Server terminates connection. response="+a),"ECONNECTION",a,"EHLO");if("2"!==a.charAt(0))return this.options.requireTLS?void this._onError(Error("EHLO failed but HELO does not support required STARTTLS. response="+a),"ECONNECTION",a,"EHLO"):(this._responseActions.push(this._actionHELO),void this._sendCommand("HELO "+this.name));if(this._ehloLines=a.split(/\r?\n/).map(a=>a.replace(/^\d+[ -]/,"").trim()).filter(a=>a).slice(1),!this.secure&&!this.options.ignoreTLS&&(/[ -]STARTTLS\b/im.test(a)||this.options.requireTLS)){this._sendCommand("STARTTLS"),this._responseActions.push(this._actionSTARTTLS);return}/[ -]SMTPUTF8\b/im.test(a)&&this._supportedExtensions.push("SMTPUTF8"),/[ -]DSN\b/im.test(a)&&this._supportedExtensions.push("DSN"),/[ -]8BITMIME\b/im.test(a)&&this._supportedExtensions.push("8BITMIME"),/[ -]PIPELINING\b/im.test(a)&&this._supportedExtensions.push("PIPELINING"),/[ -]AUTH\b/i.test(a)&&(this.allowsAuth=!0),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)PLAIN/i.test(a)&&this._supportedAuth.push("PLAIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)LOGIN/i.test(a)&&this._supportedAuth.push("LOGIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)CRAM-MD5/i.test(a)&&this._supportedAuth.push("CRAM-MD5"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)XOAUTH2/i.test(a)&&this._supportedAuth.push("XOAUTH2"),(b=a.match(/[ -]SIZE(?:[ \t]+(\d+))?/im))&&(this._supportedExtensions.push("SIZE"),this._maxAllowedSize=Number(b[1])||0),this.emit("connect")}_actionHELO(a){if("2"!==a.charAt(0))return void this._onError(Error("Invalid HELO. response="+a),"EPROTOCOL",a,"HELO");this.allowsAuth=!0,this.emit("connect")}_actionSTARTTLS(a){if("2"!==a.charAt(0))return this.options.opportunisticTLS?(this.logger.info({tnx:"smtp"},"Failed STARTTLS upgrade, continuing unencrypted"),this.emit("connect")):void this._onError(Error("Error upgrading connection with STARTTLS"),"ETLS",a,"STARTTLS");this._upgradeConnection((a,b)=>{if(a)return void this._onError(Error("Error initiating TLS - "+(a.message||a)),"ETLS",!1,"STARTTLS");this.logger.info({tnx:"smtp"},"Connection upgraded with STARTTLS"),b?this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name)):this.emit("connect")})}_actionAUTH_LOGIN_USER(a,b){if(!/^334[ -]/.test(a))return void b(this._formatError('Invalid login sequence while waiting for "334 VXNlcm5hbWU6"',"EAUTH",a,"AUTH LOGIN"));this._responseActions.push(a=>{this._actionAUTH_LOGIN_PASS(a,b)}),this._sendCommand(Buffer.from(this._auth.credentials.user+"","utf-8").toString("base64"))}_actionAUTH_CRAM_MD5(a,b){let c=a.match(/^334\s+(.+)$/),d="";if(!c)return b(this._formatError("Invalid login sequence while waiting for server challenge string","EAUTH",a,"AUTH CRAM-MD5"));d=c[1];let e=Buffer.from(d,"base64").toString("ascii"),f=i.createHmac("md5",this._auth.credentials.pass);f.update(e);let g=this._auth.credentials.user+" "+f.digest("hex");this._responseActions.push(a=>{this._actionAUTH_CRAM_MD5_PASS(a,b)}),this._sendCommand(Buffer.from(g).toString("base64"),Buffer.from(this._auth.credentials.user+" /* secret */").toString("base64"))}_actionAUTH_CRAM_MD5_PASS(a,b){if(!a.match(/^235\s+/))return b(this._formatError('Invalid login sequence while waiting for "235"',"EAUTH",a,"AUTH CRAM-MD5"));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,b(null,!0)}_actionAUTH_LOGIN_PASS(a,b){if(!/^334[ -]/.test(a))return b(this._formatError('Invalid login sequence while waiting for "334 UGFzc3dvcmQ6"',"EAUTH",a,"AUTH LOGIN"));this._responseActions.push(a=>{this._actionAUTHComplete(a,b)}),this._sendCommand(Buffer.from((this._auth.credentials.pass||"").toString(),"utf-8").toString("base64"),Buffer.from("/* secret */","utf-8").toString("base64"))}_actionAUTHComplete(a,b,c){if(c||"function"!=typeof b||(c=b,b=!1),"334"===a.substr(0,3)){this._responseActions.push(a=>{b||"XOAUTH2"!==this._authMethod?this._actionAUTHComplete(a,!0,c):setImmediate(()=>this._handleXOauth2Token(!0,c))}),this._sendCommand("");return}if("2"!==a.charAt(0))return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),c(this._formatError("Invalid login","EAUTH",a,"AUTH "+this._authMethod));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,c(null,!0)}_actionMAIL(a,b){let c,d;if(2!==Number(a.charAt(0)))return c=this._usingSmtpUtf8&&/^550 /.test(a)&&/[\x80-\uFFFF]/.test(this._envelope.from)?"Internationalized mailbox name not allowed":"Mail command failed",b(this._formatError(c,"EENVELOPE",a,"MAIL FROM"));if(!this._envelope.rcptQueue.length)return b(this._formatError("Can't send mail - no recipients defined","EENVELOPE",!1,"API"));if(this._recipientQueue=[],this._supportedExtensions.includes("PIPELINING"))for(;this._envelope.rcptQueue.length;)d=this._envelope.rcptQueue.shift(),this._recipientQueue.push(d),this._responseActions.push(a=>{this._actionRCPT(a,b)}),this._sendCommand("RCPT TO:<"+d+">"+this._getDsnRcptToArgs());else d=this._envelope.rcptQueue.shift(),this._recipientQueue.push(d),this._responseActions.push(a=>{this._actionRCPT(a,b)}),this._sendCommand("RCPT TO:<"+d+">"+this._getDsnRcptToArgs())}_actionRCPT(a,b){let c,d,e=this._recipientQueue.shift();if(2!==Number(a.charAt(0))?(c=this._usingSmtpUtf8&&/^553 /.test(a)&&/[\x80-\uFFFF]/.test(e)?"Internationalized mailbox name not allowed":"Recipient command failed",this._envelope.rejected.push(e),(d=this._formatError(c,"EENVELOPE",a,"RCPT TO")).recipient=e,this._envelope.rejectedErrors.push(d)):this._envelope.accepted.push(e),this._envelope.rcptQueue.length||this._recipientQueue.length)this._envelope.rcptQueue.length&&(e=this._envelope.rcptQueue.shift(),this._recipientQueue.push(e),this._responseActions.push(a=>{this._actionRCPT(a,b)}),this._sendCommand("RCPT TO:<"+e+">"+this._getDsnRcptToArgs()));else{if(!(this._envelope.rejected.length<this._envelope.to.length))return(d=this._formatError("Can't send mail - all recipients were rejected","EENVELOPE",a,"RCPT TO")).rejected=this._envelope.rejected,d.rejectedErrors=this._envelope.rejectedErrors,b(d);this._responseActions.push(a=>{this._actionDATA(a,b)}),this._sendCommand("DATA")}}_actionDATA(a,b){if(!/^[23]/.test(a))return b(this._formatError("Data command failed","EENVELOPE",a,"DATA"));let c={accepted:this._envelope.accepted,rejected:this._envelope.rejected};this._ehloLines&&this._ehloLines.length&&(c.ehlo=this._ehloLines),this._envelope.rejectedErrors.length&&(c.rejectedErrors=this._envelope.rejectedErrors),b(null,c)}_actionSMTPStream(a,b){return 2!==Number(a.charAt(0))?b(this._formatError("Message failed","EMESSAGE",a,"DATA")):b(null,a)}_actionLMTPStream(a,b,c,d){let e;if(2!==Number(c.charAt(0))){(e=this._formatError("Message failed for recipient "+a,"EMESSAGE",c,"DATA")).recipient=a,this._envelope.rejected.push(a),this._envelope.rejectedErrors.push(e);for(let b=0,c=this._envelope.accepted.length;b<c;b++)this._envelope.accepted[b]===a&&this._envelope.accepted.splice(b,1)}if(b)return d(null,c)}_handleXOauth2Token(a,b){this._auth.oauth2.getToken(a,(c,d)=>{if(c)return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),b(this._formatError(c,"EAUTH",!1,"AUTH XOAUTH2"));this._responseActions.push(c=>{this._actionAUTHComplete(c,a,b)}),this._sendCommand("AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token(d),"AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token("/* secret */"))})}_isDestroyedMessage(a){if(this._destroyed)return"Cannot "+a+" - smtp connection is already destroyed.";if(this._socket){if(this._socket.destroyed)return"Cannot "+a+" - smtp connection socket is already destroyed.";if(!this._socket.writable)return"Cannot "+a+" - smtp connection socket is already half-closed."}}_getHostname(){let a;try{a=h.hostname()||""}catch(b){a="localhost"}return(!a||0>a.indexOf("."))&&(a="[127.0.0.1]"),a.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)&&(a="["+a+"]"),a}}a.exports=m},21820:a=>{"use strict";a.exports=require("os")},21940:(a,b,c)=>{"use strict";let d=c(27910).Stream,e=c(38099),f=c(55511),g=c(91292);class h extends d{constructor(a,b){if(super(),this.options=a||{},a&&a.serviceClient){if(!a.privateKey||!a.user)return void setImmediate(()=>this.emit("error",Error('Options "privateKey" and "user" are required for service account!')));let b=Math.min(Math.max(Number(this.options.serviceRequestTimeout)||0,0),3600);this.options.serviceRequestTimeout=b||300}if(this.logger=g.getLogger({logger:b},{component:this.options.component||"OAuth2"}),this.provisionCallback="function"==typeof this.options.provisionCallback&&this.options.provisionCallback,this.options.accessUrl=this.options.accessUrl||"https://accounts.google.com/o/oauth2/token",this.options.customHeaders=this.options.customHeaders||{},this.options.customParams=this.options.customParams||{},this.accessToken=this.options.accessToken||!1,this.options.expires&&Number(this.options.expires))this.expires=this.options.expires;else{let a=Math.max(Number(this.options.timeout)||0,0);this.expires=a&&Date.now()+1e3*a||0}}getToken(a,b){if(!a&&this.accessToken&&(!this.expires||this.expires>Date.now()))return b(null,this.accessToken);let c=(...a)=>{a[0]?this.logger.error({err:a[0],tnx:"OAUTH2",user:this.options.user,action:"renew"},"Failed generating new Access Token for %s",this.options.user):this.logger.info({tnx:"OAUTH2",user:this.options.user,action:"renew"},"Generated new Access Token for %s",this.options.user),b(...a)};this.provisionCallback?this.provisionCallback(this.options.user,!!a,(a,b,d)=>{!a&&b&&(this.accessToken=b,this.expires=d||0),c(a,b)}):this.generateToken(c)}updateToken(a,b){this.accessToken=a,b=Math.max(Number(b)||0,0),this.expires=b&&Date.now()+1e3*b||0,this.emit("token",{user:this.options.user,accessToken:a||"",expires:this.expires})}generateToken(a){let b,c;if(this.options.serviceClient){let d,e=Math.floor(Date.now()/1e3),f={iss:this.options.serviceClient,scope:this.options.scope||"https://mail.google.com/",sub:this.options.user,aud:this.options.accessUrl,iat:e,exp:e+this.options.serviceRequestTimeout};try{d=this.jwtSignRS256(f)}catch(b){return a(Error("Can't generate token. Check your auth options"))}b={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:d},c={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:f}}else{if(!this.options.refreshToken)return a(Error("Can't create new access token for user"));b={client_id:this.options.clientId||"",client_secret:this.options.clientSecret||"",refresh_token:this.options.refreshToken,grant_type:"refresh_token"},c={client_id:this.options.clientId||"",client_secret:(this.options.clientSecret||"").substr(0,6)+"...",refresh_token:(this.options.refreshToken||"").substr(0,6)+"...",grant_type:"refresh_token"}}Object.keys(this.options.customParams).forEach(a=>{b[a]=this.options.customParams[a],c[a]=this.options.customParams[a]}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"generate"},"Requesting token using: %s",JSON.stringify(c)),this.postRequest(this.options.accessUrl,b,this.options,(b,c)=>{let d;if(b)return a(b);try{d=JSON.parse(c.toString())}catch(b){return a(b)}if(!d||"object"!=typeof d)return this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",(c||"").toString()),a(Error("Invalid authentication response"));let e={};if(Object.keys(d).forEach(a=>{"access_token"!==a?e[a]=d[a]:e[a]=(d[a]||"").toString().substr(0,6)+"..."}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",JSON.stringify(e)),d.error){let b=d.error;return d.error_description&&(b+=": "+d.error_description),d.error_uri&&(b+=" ("+d.error_uri+")"),a(Error(b))}return d.access_token?(this.updateToken(d.access_token,d.expires_in),a(null,this.accessToken)):a(Error("No access token"))})}buildXOAuth2Token(a){let b=["user="+(this.options.user||""),"auth=Bearer "+(a||this.accessToken),"",""];return Buffer.from(b.join("\x01"),"utf-8").toString("base64")}postRequest(a,b,c,d){let f=!1,g=[],h=0,i=e(a,{method:"post",headers:c.customHeaders,body:b,allowErrorResponse:!0});i.on("readable",()=>{let a;for(;null!==(a=i.read());)g.push(a),h+=a.length}),i.once("error",a=>{if(!f)return f=!0,d(a)}),i.once("end",()=>{if(!f)return f=!0,d(null,Buffer.concat(g,h))})}toBase64URL(a){return"string"==typeof a&&(a=Buffer.from(a)),a.toString("base64").replace(/[=]+/g,"").replace(/\+/g,"-").replace(/\//g,"_")}jwtSignRS256(a){a=['{"alg":"RS256","typ":"JWT"}',JSON.stringify(a)].map(a=>this.toBase64URL(a)).join(".");let b=f.createSign("RSA-SHA256").update(a).sign(this.options.privateKey);return a+"."+this.toBase64URL(b)}}a.exports=h},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31534:(a,b,c)=>{"use strict";let d=c(27910).Transform,e=c(55511);class f extends d{constructor(a){super(),a=a||{},this.chunkBuffer=[],this.chunkBufferLen=0,this.bodyHash=e.createHash(a.hashAlgo||"sha1"),this.remainder="",this.byteLength=0,this.debug=a.debug,this._debugBody=!!a.debug&&[]}updateHash(a){let b,c="",d="file";for(let b=a.length-1;b>=0;b--){let e=a[b];if("file"===d&&(10===e||13===e));else if("file"===d&&(9===e||32===e))d="line";else if("line"===d&&(9===e||32===e));else if(("file"===d||"line"===d)&&(d="body",b===a.length-1))break;if(0===b){if("file"===d&&(!this.remainder||/[\r\n]$/.test(this.remainder))||"line"===d&&(!this.remainder||/[ \t]$/.test(this.remainder))){this.remainder+=a.toString("binary");return}else if("line"===d||"file"===d){c=a.toString("binary"),a=!1;break}}if("body"===d){c=a.slice(b+1).toString("binary"),a=a.slice(0,b+1);break}}let e=!!this.remainder;if(a&&!e){for(let b=0,c=a.length;b<c;b++)if(b&&10===a[b]&&13!==a[b-1]){e=!0;break}else if(b&&13===a[b]&&32===a[b-1]){e=!0;break}else if(b&&32===a[b]&&32===a[b-1]){e=!0;break}else if(9===a[b]){e=!0;break}}e?(b=this.remainder+(a?a.toString("binary"):""),this.remainder=c,b=b.replace(/\r?\n/g,"\n").replace(/[ \t]*$/gm,"").replace(/[ \t]+/gm," ").replace(/\n/g,"\r\n"),a=Buffer.from(b,"binary")):c&&(this.remainder=c),this.debug&&this._debugBody.push(a),this.bodyHash.update(a)}_transform(a,b,c){if(!a||!a.length)return c();"string"==typeof a&&(a=Buffer.from(a,b)),this.updateHash(a),this.byteLength+=a.length,this.push(a),c()}_flush(a){/[\r\n]$/.test(this.remainder)&&this.byteLength>2&&this.bodyHash.update(Buffer.from("\r\n")),this.byteLength||this.push(Buffer.from("\r\n")),this.emit("hash",this.bodyHash.digest("base64"),!!this.debug&&Buffer.concat(this._debugBody)),a()}}a.exports=f},33873:a=>{"use strict";a.exports=require("path")},34041:(a,b,c)=>{"use strict";let d=c(27910).Transform;class e extends d{constructor(a){super(a),this.lastBytes=Buffer.alloc(4),this.headersParsed=!1,this.headerBytes=0,this.headerChunks=[],this.rawHeaders=!1,this.bodySize=0}updateLastBytes(a){let b=this.lastBytes.length,c=Math.min(a.length,b);for(let a=0,d=b-c;a<d;a++)this.lastBytes[a]=this.lastBytes[a+c];for(let d=1;d<=c;d++)this.lastBytes[b-d]=a[a.length-d]}checkHeaders(a){if(this.headersParsed)return!0;let b=this.lastBytes.length,c=0;this.curLinePos=0;for(let d=0,e=this.lastBytes.length+a.length;d<e;d++)if(10===(d<b?this.lastBytes[d]:a[d-b])&&d){let e=d-1<b?this.lastBytes[d-1]:a[d-1-b],f=d>1&&(d-2<b?this.lastBytes[d-2]:a[d-2-b]);if(10===e||13===e&&10===f){this.headersParsed=!0,c=d-b+1,this.headerBytes+=c;break}}if(this.headersParsed){if(this.headerChunks.push(a.slice(0,c)),this.rawHeaders=Buffer.concat(this.headerChunks,this.headerBytes),this.headerChunks=null,this.emit("headers",this.parseHeaders()),a.length-1>c){let b=a.slice(c);this.bodySize+=b.length,setImmediate(()=>this.push(b))}return!1}return this.headerBytes+=a.length,this.headerChunks.push(a),this.updateLastBytes(a),!1}_transform(a,b,c){let d;if(!a||!a.length)return c();"string"==typeof a&&(a=Buffer.from(a,b));try{d=this.checkHeaders(a)}catch(a){return c(a)}d&&(this.bodySize+=a.length,this.push(a)),setImmediate(c)}_flush(a){if(this.headerChunks){let a=Buffer.concat(this.headerChunks,this.headerBytes);this.bodySize+=a.length,this.push(a),this.headerChunks=null}a()}parseHeaders(){let a=(this.rawHeaders||"").toString().split(/\r?\n/);for(let b=a.length-1;b>0;b--)/^\s/.test(a[b])&&(a[b-1]+="\n"+a[b],a.splice(b,1));return a.filter(a=>a.trim()).map(a=>({key:a.substr(0,a.indexOf(":")).trim().toLowerCase(),line:a}))}}a.exports=e},34631:a=>{"use strict";a.exports=require("tls")},38099:(a,b,c)=>{"use strict";let d=c(81630),e=c(55591),f=c(79551),g=c(74075),h=c(27910).PassThrough,i=c(53752),j=c(49074),k=c(91645);a.exports=function(a,b){return function a(b,c){let l,m,n;(c=c||{}).fetchRes=c.fetchRes||new h,c.cookies=c.cookies||new i,c.redirects=c.redirects||0,c.maxRedirects=isNaN(c.maxRedirects)?5:c.maxRedirects,c.cookie&&([].concat(c.cookie||[]).forEach(a=>{c.cookies.set(a,b)}),c.cookie=!1);let o=c.fetchRes,p=f.parse(b),q=(c.method||"").toString().trim().toUpperCase()||"GET",r=!1,s="https:"===p.protocol?e:d,t={"accept-encoding":"gzip,deflate","user-agent":"nodemailer/"+j.version};if(Object.keys(c.headers||{}).forEach(a=>{t[a.toLowerCase().trim()]=c.headers[a]}),c.userAgent&&(t["user-agent"]=c.userAgent),p.auth&&(t.Authorization="Basic "+Buffer.from(p.auth).toString("base64")),(l=c.cookies.get(b))&&(t.cookie=l),c.body){if(!1!==c.contentType&&(t["Content-Type"]=c.contentType||"application/x-www-form-urlencoded"),"function"==typeof c.body.pipe)t["Transfer-Encoding"]="chunked",(m=c.body).on("error",a=>{r||(r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a))});else{if(c.body instanceof Buffer)m=c.body;else if("object"==typeof c.body)try{m=Buffer.from(Object.keys(c.body).map(a=>{let b=c.body[a].toString().trim();return encodeURIComponent(a)+"="+encodeURIComponent(b)}).join("&"))}catch(a){if(r)return;r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a);return}else m=Buffer.from(c.body.toString().trim());t["Content-Type"]=c.contentType||"application/x-www-form-urlencoded",t["Content-Length"]=m.length}q=(c.method||"").toString().trim().toUpperCase()||"POST"}let u={method:q,host:p.hostname,path:p.path,port:p.port?p.port:"https:"===p.protocol?443:80,headers:t,rejectUnauthorized:!1,agent:!1};c.tls&&Object.keys(c.tls).forEach(a=>{u[a]=c.tls[a]}),"https:"!==p.protocol||!p.hostname||p.hostname===u.host||k.isIP(p.hostname)||u.servername||(u.servername=p.hostname);try{n=s.request(u)}catch(a){return r=!0,setImmediate(()=>{a.type="FETCH",a.sourceUrl=b,o.emit("error",a)}),o}return c.timeout&&n.setTimeout(c.timeout,()=>{if(r)return;r=!0,n.abort();let a=Error("Request Timeout");a.type="FETCH",a.sourceUrl=b,o.emit("error",a)}),n.on("error",a=>{r||(r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a))}),n.on("response",d=>{let e;if(!r){switch(d.headers["content-encoding"]){case"gzip":case"deflate":e=g.createUnzip()}if(d.headers["set-cookie"]&&[].concat(d.headers["set-cookie"]||[]).forEach(a=>{c.cookies.set(a,b)}),[301,302,303,307,308].includes(d.statusCode)&&d.headers.location){if(c.redirects++,c.redirects>c.maxRedirects){r=!0;let a=Error("Maximum redirect count exceeded");a.type="FETCH",a.sourceUrl=b,o.emit("error",a),n.abort();return}return c.method="GET",c.body=!1,a(f.resolve(b,d.headers.location),c)}if(o.statusCode=d.statusCode,o.headers=d.headers,d.statusCode>=300&&!c.allowErrorResponse){r=!0;let a=Error("Invalid status code "+d.statusCode);a.type="FETCH",a.sourceUrl=b,o.emit("error",a),n.abort();return}d.on("error",a=>{r||(r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a),n.abort())}),e?(d.pipe(e).pipe(o),e.on("error",a=>{r||(r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a),n.abort())})):d.pipe(o)}}),setImmediate(()=>{if(m)try{if("function"==typeof m.pipe)return m.pipe(n);n.write(m)}catch(a){r=!0,a.type="FETCH",a.sourceUrl=b,o.emit("error",a);return}n.end()}),o}(a,b)},a.exports.Cookies=i},39449:(a,b,c)=>{"use strict";let d=c(27910).Transform;class e extends d{constructor(a){super(a),this.options=a||{},this.lastByte=!1}_transform(a,b,c){let d,e=0;for(let b=0,c=a.length;b<c;b++)10===a[b]&&(b&&13!==a[b-1]||!b&&13!==this.lastByte)&&(b>e&&(d=a.slice(e,b),this.push(d)),this.push(Buffer.from("\r\n")),e=b+1);e&&e<a.length?(d=a.slice(e),this.push(d)):e||this.push(a),this.lastByte=a[a.length-1],c()}}a.exports=e},41748:(a,b,c)=>{"use strict";let d=c(27910).Transform;class e extends d{constructor(a){super(a),this.options=a||{}}_transform(a,b,c){let d,e=0;for(let b=0,c=a.length;b<c;b++)13===a[b]&&(d=a.slice(e,b),e=b+1,this.push(d));e&&e<a.length?(d=a.slice(e),this.push(d)):e||this.push(a),c()}}a.exports=e},43519:(a,b,c)=>{"use strict";let d=c(27910).Transform;class e extends d{constructor(a){super(a),this.options=a||{},this._curLine="",this.inByteCount=0,this.outByteCount=0,this.lastByte=!1}_transform(a,b,c){let d,e=[],f=0,g,h,i=0;if(!a||!a.length)return c();for("string"==typeof a&&(a=Buffer.from(a)),this.inByteCount+=a.length,g=0,h=a.length;g<h;g++)46===a[g]?(!g||10!==a[g-1])&&(g||this.lastByte&&10!==this.lastByte)||(d=a.slice(i,g+1),e.push(d),e.push(Buffer.from(".")),f+=d.length+1,i=g+1):10===a[g]&&(g&&13!==a[g-1]||!g&&13!==this.lastByte)&&(g>i?(d=a.slice(i,g),e.push(d),f+=d.length+2):f+=2,e.push(Buffer.from("\r\n")),i=g+1);f?(i<a.length&&(d=a.slice(i),e.push(d),f+=d.length),this.outByteCount+=f,this.push(Buffer.concat(e,f))):(this.outByteCount+=a.length,this.push(a)),this.lastByte=a[a.length-1],c()}_flush(a){let b;b=10===this.lastByte?Buffer.from(".\r\n"):13===this.lastByte?Buffer.from("\n.\r\n"):Buffer.from("\r\n.\r\n"),this.outByteCount+=b.length,this.push(b),a()}}a.exports=e},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46945:(a,b,c)=>{"use strict";let d=c(94735),e=c(91292),f=c(50143),g=c(48359),h=c(91570),i=c(7575),j=c(28354),k=c(79551),l=c(49074),m=c(17704),n=c(91645),o=c(14985),p=c(55511);class q extends d{constructor(a,b,c){super(),this.options=b||{},this._defaults=c||{},this._defaultPlugins={compile:[(...a)=>this._convertDataImages(...a)],stream:[]},this._userPlugins={compile:[],stream:[]},this.meta=new Map,this.dkim=!!this.options.dkim&&new h(this.options.dkim),this.transporter=a,this.transporter.mailer=this,this.logger=e.getLogger(this.options,{component:this.options.component||"mail"}),this.logger.debug({tnx:"create"},"Creating transport: %s",this.getVersionString()),"function"==typeof this.transporter.on&&(this.transporter.on("log",a=>{this.logger.debug({tnx:"transport"},"%s: %s",a.type,a.message)}),this.transporter.on("error",a=>{this.logger.error({err:a,tnx:"transport"},"Transport Error: %s",a.message),this.emit("error",a)}),this.transporter.on("idle",(...a)=>{this.emit("idle",...a)}),this.transporter.on("clear",(...a)=>{this.emit("clear",...a)})),["close","isIdle","verify"].forEach(a=>{this[a]=(...b)=>"function"==typeof this.transporter[a]?("verify"===a&&"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1),this.transporter[a](...b)):(this.logger.warn({tnx:"transport",methodName:a},"Non existing method %s called for transport",a),!1)}),this.options.proxy&&"string"==typeof this.options.proxy&&this.setupProxy(this.options.proxy)}use(a,b){return a=(a||"").toString(),this._userPlugins.hasOwnProperty(a)?this._userPlugins[a].push(b):this._userPlugins[a]=[b],this}sendMail(a,b=null){let c;b||(c=new Promise((a,c)=>{b=e.callbackPromise(a,c)})),"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1);let d=new m(this,a);return this.logger.debug({tnx:"transport",name:this.transporter.name,version:this.transporter.version,action:"send"},"Sending mail using %s/%s",this.transporter.name,this.transporter.version),this._processPlugins("compile",d,a=>{if(a)return this.logger.error({err:a,tnx:"plugin",action:"compile"},"PluginCompile Error: %s",a.message),b(a);d.message=new g(d.data).compile(),d.setMailerHeader(),d.setPriorityHeaders(),d.setListHeaders(),this._processPlugins("stream",d,a=>{if(a)return this.logger.error({err:a,tnx:"plugin",action:"stream"},"PluginStream Error: %s",a.message),b(a);(d.data.dkim||this.dkim)&&d.message.processFunc(a=>{let b=d.data.dkim?new h(d.data.dkim):this.dkim;return this.logger.debug({tnx:"DKIM",messageId:d.message.messageId(),dkimDomains:b.keys.map(a=>a.keySelector+"."+a.domainName).join(", ")},"Signing outgoing message with %s keys",b.keys.length),b.sign(a,d.data._dkim)}),this.transporter.send(d,(...a)=>{a[0]&&this.logger.error({err:a[0],tnx:"transport",action:"send"},"Send Error: %s",a[0].message),b(...a)})})}),c}getVersionString(){return j.format("%s (%s; +%s; %s/%s)",l.name,l.version,l.homepage,this.transporter.name,this.transporter.version)}_processPlugins(a,b,c){if(a=(a||"").toString(),!this._userPlugins.hasOwnProperty(a))return c();let d=this._userPlugins[a]||[],e=this._defaultPlugins[a]||[];if(d.length&&this.logger.debug({tnx:"transaction",pluginCount:d.length,step:a},"Using %s plugins for %s",d.length,a),d.length+e.length===0)return c();let f=0,g="default",h=()=>{let a="default"===g?e:d;if(f>=a.length)if("default"!==g||!d.length)return c();else g="user",f=0,a=d;(0,a[f++])(b,a=>{if(a)return c(a);h()})};h()}setupProxy(a){let b=k.parse(a);this.getSocket=(a,c)=>{let d=b.protocol.replace(/:$/,"").toLowerCase();if(this.meta.has("proxy_handler_"+d))return this.meta.get("proxy_handler_"+d)(b,a,c);switch(d){case"http":case"https":i(b.href,a.port,a.host,(a,b)=>a?c(a):c(null,{connection:b}));return;case"socks":case"socks5":case"socks4":case"socks4a":{if(!this.meta.has("proxy_socks_module"))return c(Error("Socks module not loaded"));let d=d=>{let e=!!this.meta.get("proxy_socks_module").SocksClient,f=e?this.meta.get("proxy_socks_module").SocksClient:this.meta.get("proxy_socks_module"),g=Number(b.protocol.replace(/\D/g,""))||5,h={proxy:{ipaddress:d,port:Number(b.port),type:g},[e?"destination":"target"]:{host:a.host,port:a.port},command:"connect"};if(b.auth){let a=decodeURIComponent(b.auth.split(":").shift()),c=decodeURIComponent(b.auth.split(":").pop());e?(h.proxy.userId=a,h.proxy.password=c):4===g?h.userid=a:h.authentication={username:a,password:c}}f.createConnection(h,(a,b)=>a?c(a):c(null,{connection:b.socket||b}))};if(n.isIP(b.hostname))return d(b.hostname);return o.resolve(b.hostname,(a,b)=>{if(a)return c(a);d(Array.isArray(b)?b[0]:b)})}}c(Error("Unknown proxy configuration"))}}_convertDataImages(a,b){if(!this.options.attachDataUrls&&!a.data.attachDataUrls||!a.data.html)return b();a.resolveContent(a.data,"html",(c,d)=>{if(c)return b(c);let e=0;d=(d||"").toString().replace(/(<img\b[^<>]{0,1024} src\s{0,20}=[\s"']{0,20})(data:([^;]+);[^"'>\s]+)/gi,(b,c,d,g)=>{let h=p.randomBytes(10).toString("hex")+"@localhost";return a.data.attachments||(a.data.attachments=[]),Array.isArray(a.data.attachments)||(a.data.attachments=[].concat(a.data.attachments||[])),a.data.attachments.push({path:d,cid:h,filename:"image-"+ ++e+"."+f.detectExtension(g)}),c+"cid:"+h}),a.data.html=d,b()})}set(a,b){return this.meta.set(a,b)}get(a){return this.meta.get(a)}}a.exports=q},48178:a=>{"use strict";let b=/^xn--/,c=/[^\0-\x7F]/,d=/[\x2E\u3002\uFF0E\uFF61]/g,e={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},f=Math.floor,g=String.fromCharCode;function h(a){throw RangeError(e[a])}function i(a,b){let c=a.split("@"),e="";return c.length>1&&(e=c[0]+"@",a=c[1]),e+(function(a,b){let c=[],d=a.length;for(;d--;)c[d]=b(a[d]);return c})((a=a.replace(d,".")).split("."),b).join(".")}function j(a){let b=[],c=0,d=a.length;for(;c<d;){let e=a.charCodeAt(c++);if(e>=55296&&e<=56319&&c<d){let d=a.charCodeAt(c++);(64512&d)==56320?b.push(((1023&e)<<10)+(1023&d)+65536):(b.push(e),c--)}else b.push(e)}return b}let k=function(a,b){return a+22+75*(a<26)-((0!=b)<<5)},l=function(a,b,c){let d=0;for(a=c?f(a/700):a>>1,a+=f(a/b);a>455;d+=36)a=f(a/35);return f(d+36*a/(a+38))},m=function(a){let b=[],c=a.length,d=0,e=128,g=72,i=a.lastIndexOf("-");i<0&&(i=0);for(let c=0;c<i;++c)a.charCodeAt(c)>=128&&h("not-basic"),b.push(a.charCodeAt(c));for(let k=i>0?i+1:0;k<c;){let i=d;for(let b=1,e=36;;e+=36){var j;k>=c&&h("invalid-input");let i=(j=a.charCodeAt(k++))>=48&&j<58?26+(j-48):j>=65&&j<91?j-65:j>=97&&j<123?j-97:36;i>=36&&h("invalid-input"),i>f((0x7fffffff-d)/b)&&h("overflow"),d+=i*b;let l=e<=g?1:e>=g+26?26:e-g;if(i<l)break;let m=36-l;b>f(0x7fffffff/m)&&h("overflow"),b*=m}let m=b.length+1;g=l(d-i,m,0==i),f(d/m)>0x7fffffff-e&&h("overflow"),e+=f(d/m),d%=m,b.splice(d++,0,e)}return String.fromCodePoint(...b)},n=function(a){let b=[],c=(a=j(a)).length,d=128,e=0,i=72;for(let c of a)c<128&&b.push(g(c));let m=b.length,n=m;for(m&&b.push("-");n<c;){let c=0x7fffffff;for(let b of a)b>=d&&b<c&&(c=b);let j=n+1;for(let o of(c-d>f((0x7fffffff-e)/j)&&h("overflow"),e+=(c-d)*j,d=c,a))if(o<d&&++e>0x7fffffff&&h("overflow"),o===d){let a=e;for(let c=36;;c+=36){let d=c<=i?1:c>=i+26?26:c-i;if(a<d)break;let e=a-d,h=36-d;b.push(g(k(d+e%h,0))),a=f(e/h)}b.push(g(k(a,0))),i=l(e,j,n===m),e=0,++n}++e,++d}return b.join("")};a.exports={version:"2.3.1",ucs2:{decode:j,encode:a=>String.fromCodePoint(...a)},decode:m,encode:n,toASCII:function(a){return i(a,function(a){return c.test(a)?"xn--"+n(a):a})},toUnicode:function(a){return i(a,function(a){return b.test(a)?m(a.slice(4).toLowerCase()):a})}}},48359:(a,b,c)=>{"use strict";let d=c(7180),e=c(18199),f=c(91292).parseDataURI;class g{constructor(a){this.mail=a||{},this.message=!1}compile(){return this._alternatives=this.getAlternatives(),this._htmlNode=this._alternatives.filter(a=>/^text\/html\b/i.test(a.contentType)).pop(),this._attachments=this.getAttachments(!!this._htmlNode),this._useRelated=!!(this._htmlNode&&this._attachments.related.length),this._useAlternative=this._alternatives.length>1,this._useMixed=this._attachments.attached.length>1||this._alternatives.length&&1===this._attachments.attached.length,this.mail.raw?this.message=new d("message/rfc822",{newline:this.mail.newline}).setRaw(this.mail.raw):this._useMixed?this.message=this._createMixed():this._useAlternative?this.message=this._createAlternative():this._useRelated?this.message=this._createRelated():this.message=this._createContentNode(!1,[].concat(this._alternatives||[]).concat(this._attachments.attached||[]).shift()||{contentType:"text/plain",content:""}),this.mail.headers&&this.message.addHeader(this.mail.headers),["from","sender","to","cc","bcc","reply-to","in-reply-to","references","subject","message-id","date"].forEach(a=>{let b=a.replace(/-(\w)/g,(a,b)=>b.toUpperCase());this.mail[b]&&this.message.setHeader(a,this.mail[b])}),this.mail.envelope&&this.message.setEnvelope(this.mail.envelope),this.message.messageId(),this.message}getAttachments(a){let b,c,d=[].concat(this.mail.attachments||[]).map((a,b)=>{let c;/^data:/i.test(a.path||a.href)&&(a=this._processDataUrl(a));let d=a.contentType||e.detectMimeType(a.filename||a.path||a.href||"bin"),f=/^image\//i.test(d),g=/^message\//i.test(d);return c={contentType:d,contentDisposition:a.contentDisposition||(g||f&&a.cid?"inline":"attachment"),contentTransferEncoding:"contentTransferEncoding"in a?a.contentTransferEncoding:g?"7bit":"base64"},a.filename?c.filename=a.filename:!g&&!1!==a.filename&&(c.filename=(a.path||a.href||"").split("/").pop().split("?").shift()||"attachment-"+(b+1),0>c.filename.indexOf(".")&&(c.filename+="."+e.detectExtension(c.contentType))),/^https?:\/\//i.test(a.path)&&(a.href=a.path,a.path=void 0),a.cid&&(c.cid=a.cid),a.raw?c.raw=a.raw:a.path?c.content={path:a.path}:a.href?c.content={href:a.href,httpHeaders:a.httpHeaders}:c.content=a.content||"",a.encoding&&(c.encoding=a.encoding),a.headers&&(c.headers=a.headers),c});return(this.mail.icalEvent&&(b="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},c={},Object.keys(b).forEach(a=>{c[a]=b[a]}),c.contentType="application/ics",c.headers||(c.headers={}),c.filename=c.filename||"invite.ics",c.headers["Content-Disposition"]="attachment",c.headers["Content-Transfer-Encoding"]="base64"),a)?{attached:d.filter(a=>!a.cid).concat(c||[]),related:d.filter(a=>!!a.cid)}:{attached:d.concat(c||[]),related:[]}}getAlternatives(){let a=[],b,c,d,f,g,h;return this.mail.text&&((b="object"==typeof this.mail.text&&(this.mail.text.content||this.mail.text.path||this.mail.text.href||this.mail.text.raw)?this.mail.text:{content:this.mail.text}).contentType="text/plain; charset=utf-8"),this.mail.watchHtml&&((d="object"==typeof this.mail.watchHtml&&(this.mail.watchHtml.content||this.mail.watchHtml.path||this.mail.watchHtml.href||this.mail.watchHtml.raw)?this.mail.watchHtml:{content:this.mail.watchHtml}).contentType="text/watch-html; charset=utf-8"),this.mail.amp&&((f="object"==typeof this.mail.amp&&(this.mail.amp.content||this.mail.amp.path||this.mail.amp.href||this.mail.amp.raw)?this.mail.amp:{content:this.mail.amp}).contentType="text/x-amp-html; charset=utf-8"),this.mail.icalEvent&&(g="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},h={},Object.keys(g).forEach(a=>{h[a]=g[a]}),h.content&&"object"==typeof h.content&&(h.content._resolve=!0),h.filename=!1,h.contentType="text/calendar; charset=utf-8; method="+(h.method||"PUBLISH").toString().trim().toUpperCase(),h.headers||(h.headers={})),this.mail.html&&((c="object"==typeof this.mail.html&&(this.mail.html.content||this.mail.html.path||this.mail.html.href||this.mail.html.raw)?this.mail.html:{content:this.mail.html}).contentType="text/html; charset=utf-8"),[].concat(b||[]).concat(d||[]).concat(f||[]).concat(c||[]).concat(h||[]).concat(this.mail.alternatives||[]).forEach(b=>{let c;/^data:/i.test(b.path||b.href)&&(b=this._processDataUrl(b)),c={contentType:b.contentType||e.detectMimeType(b.filename||b.path||b.href||"txt"),contentTransferEncoding:b.contentTransferEncoding},b.filename&&(c.filename=b.filename),/^https?:\/\//i.test(b.path)&&(b.href=b.path,b.path=void 0),b.raw?c.raw=b.raw:b.path?c.content={path:b.path}:b.href?c.content={href:b.href}:c.content=b.content||"",b.encoding&&(c.encoding=b.encoding),b.headers&&(c.headers=b.headers),a.push(c)}),a}_createMixed(a){let b;return b=a?a.createChild("multipart/mixed",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new d("multipart/mixed",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._useAlternative?this._createAlternative(b):this._useRelated&&this._createRelated(b),[].concat(!this._useAlternative&&this._alternatives||[]).concat(this._attachments.attached||[]).forEach(a=>{this._useRelated&&a===this._htmlNode||this._createContentNode(b,a)}),b}_createAlternative(a){let b;return b=a?a.createChild("multipart/alternative",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new d("multipart/alternative",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._alternatives.forEach(a=>{this._useRelated&&this._htmlNode===a?this._createRelated(b):this._createContentNode(b,a)}),b}_createRelated(a){let b;return b=a?a.createChild('multipart/related; type="text/html"',{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new d('multipart/related; type="text/html"',{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._createContentNode(b,this._htmlNode),this._attachments.related.forEach(a=>this._createContentNode(b,a)),b}_createContentNode(a,b){let c;(b=b||{}).content=b.content||"";let e=(b.encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");return c=a?a.createChild(b.contentType,{filename:b.filename,textEncoding:this.mail.textEncoding,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new d(b.contentType,{filename:b.filename,baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),b.headers&&c.addHeader(b.headers),b.cid&&c.setHeader("Content-Id","<"+b.cid.replace(/[<>]/g,"")+">"),b.contentTransferEncoding?c.setHeader("Content-Transfer-Encoding",b.contentTransferEncoding):this.mail.encoding&&/^text\//i.test(b.contentType)&&c.setHeader("Content-Transfer-Encoding",this.mail.encoding),(!/^text\//i.test(b.contentType)||b.contentDisposition)&&c.setHeader("Content-Disposition",b.contentDisposition||(b.cid&&/^image\//i.test(b.contentType)?"inline":"attachment")),"string"!=typeof b.content||["utf8","usascii","ascii"].includes(e)||(b.content=Buffer.from(b.content,e)),b.raw?c.setRaw(b.raw):c.setContent(b.content),c}_processDataUrl(a){let b;return(a.path||a.href).match(/^data:/)&&(b=f(a.path||a.href)),b&&(a.content=b.data,a.contentType=a.contentType||b.contentType,"path"in a&&(a.path=!1),"href"in a&&(a.href=!1)),a}}a.exports=g},49074:a=>{"use strict";a.exports=JSON.parse('{"name":"nodemailer","version":"7.0.5","description":"Easy as cake e-mail sending from your Node.js applications","main":"lib/nodemailer.js","scripts":{"test":"node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","test:coverage":"c8 node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","lint":"eslint .","update":"rm -rf node_modules/ package-lock.json && ncu -u && npm install"},"repository":{"type":"git","url":"https://github.com/nodemailer/nodemailer.git"},"keywords":["Nodemailer"],"author":"Andris Reinman","license":"MIT-0","bugs":{"url":"https://github.com/nodemailer/nodemailer/issues"},"homepage":"https://nodemailer.com/","devDependencies":{"@aws-sdk/client-sesv2":"3.839.0","bunyan":"1.8.15","c8":"10.1.3","eslint":"8.57.0","eslint-config-nodemailer":"1.2.0","eslint-config-prettier":"9.1.0","libbase64":"1.3.0","libmime":"5.3.7","libqp":"2.1.1","nodemailer-ntlm-auth":"1.0.4","proxy":"1.0.2","proxy-test-server":"1.0.0","smtp-server":"3.14.0"},"engines":{"node":">=6.0.0"}}')},49526:(a,b,c)=>{"use strict";let d=c(46945),e=c(91292),f=c(8346),g=c(84681),h=c(79524),i=c(77821),j=c(89767),k=c(97002),l=c(38099),m=c(49074),n=(process.env.ETHEREAL_API||"https://api.nodemailer.com").replace(/\/+$/,""),o=(process.env.ETHEREAL_WEB||"https://ethereal.email").replace(/\/+$/,""),p=(process.env.ETHEREAL_API_KEY||"").replace(/\s*/g,"")||null,q=["true","yes","y","1"].includes((process.env.ETHEREAL_CACHE||"yes").toString().trim().toLowerCase()),r=!1;a.exports.createTransport=function(a,b){let c,l;if("object"==typeof a&&"function"!=typeof a.send||"string"==typeof a&&/^(smtps?|direct):/i.test(a))if((l=(c="string"==typeof a?a:a.url)?e.parseConnectionUrl(c):a).pool)a=new f(l);else if(l.sendmail)a=new h(l);else if(l.streamTransport)a=new i(l);else if(l.jsonTransport)a=new j(l);else if(l.SES){if(l.SES.ses&&l.SES.aws){let a=Error("Using legacy SES configuration, expecting @aws-sdk/client-sesv2, see https://nodemailer.com/transports/ses/");throw a.code="LegacyConfig",a}a=new k(l)}else a=new g(l);return new d(a,l,b)},a.exports.createTestAccount=function(a,b){let c;if(b||"function"!=typeof a||(b=a,a=!1),b||(c=new Promise((a,c)=>{b=e.callbackPromise(a,c)})),q&&r)return setImmediate(()=>b(null,r)),c;a=a||n;let d=[],f=0,g={},h={requestor:m.name,version:m.version};p&&(g.Authorization="Bearer "+p);let i=l(a+"/user",{contentType:"application/json",method:"POST",headers:g,body:Buffer.from(JSON.stringify(h))});return i.on("readable",()=>{let a;for(;null!==(a=i.read());)d.push(a),f+=a.length}),i.once("error",a=>b(a)),i.once("end",()=>{let a,c,e=Buffer.concat(d,f);try{a=JSON.parse(e.toString())}catch(a){c=a}return c?b(c):"success"!==a.status||a.error?b(Error(a.error||"Request failed")):void(delete a.status,b(null,r=a))}),c},a.exports.getTestMessageUrl=function(a){if(!a||!a.response)return!1;let b=new Map;return a.response.replace(/\[([^\]]+)\]$/,(a,c)=>{c.replace(/\b([A-Z0-9]+)=([^\s]+)/g,(a,c,d)=>{b.set(c,d)})}),!!(b.has("STATUS")&&b.has("MSGID"))&&(r.web||o)+"/message/"+b.get("MSGID")}},50143:(a,b,c)=>{"use strict";let d=c(33873),e="application/octet-stream",f=new Map([["application/acad","dwg"],["application/applixware","aw"],["application/arj","arj"],["application/atom+xml","xml"],["application/atomcat+xml","atomcat"],["application/atomsvc+xml","atomsvc"],["application/base64",["mm","mme"]],["application/binhex","hqx"],["application/binhex4","hqx"],["application/book",["book","boo"]],["application/ccxml+xml,","ccxml"],["application/cdf","cdf"],["application/cdmi-capability","cdmia"],["application/cdmi-container","cdmic"],["application/cdmi-domain","cdmid"],["application/cdmi-object","cdmio"],["application/cdmi-queue","cdmiq"],["application/clariscad","ccad"],["application/commonground","dp"],["application/cu-seeme","cu"],["application/davmount+xml","davmount"],["application/drafting","drw"],["application/dsptype","tsp"],["application/dssc+der","dssc"],["application/dssc+xml","xdssc"],["application/dxf","dxf"],["application/ecmascript",["js","es"]],["application/emma+xml","emma"],["application/envoy","evy"],["application/epub+zip","epub"],["application/excel",["xls","xl","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/exi","exi"],["application/font-tdpfr","pfr"],["application/fractals","fif"],["application/freeloader","frl"],["application/futuresplash","spl"],["application/geo+json","geojson"],["application/gnutar","tgz"],["application/groupwise","vew"],["application/hlp","hlp"],["application/hta","hta"],["application/hyperstudio","stk"],["application/i-deas","unv"],["application/iges",["iges","igs"]],["application/inf","inf"],["application/internet-property-stream","acx"],["application/ipfix","ipfix"],["application/java","class"],["application/java-archive","jar"],["application/java-byte-code","class"],["application/java-serialized-object","ser"],["application/java-vm","class"],["application/javascript","js"],["application/json","json"],["application/lha","lha"],["application/lzx","lzx"],["application/mac-binary","bin"],["application/mac-binhex","hqx"],["application/mac-binhex40","hqx"],["application/mac-compactpro","cpt"],["application/macbinary","bin"],["application/mads+xml","mads"],["application/marc","mrc"],["application/marcxml+xml","mrcx"],["application/mathematica","ma"],["application/mathml+xml","mathml"],["application/mbedlet","mbd"],["application/mbox","mbox"],["application/mcad","mcd"],["application/mediaservercontrol+xml","mscml"],["application/metalink4+xml","meta4"],["application/mets+xml","mets"],["application/mime","aps"],["application/mods+xml","mods"],["application/mp21","m21"],["application/mp4","mp4"],["application/mspowerpoint",["ppt","pot","pps","ppz"]],["application/msword",["doc","dot","w6w","wiz","word"]],["application/mswrite","wri"],["application/mxf","mxf"],["application/netmc","mcp"],["application/octet-stream",["*"]],["application/oda","oda"],["application/oebps-package+xml","opf"],["application/ogg","ogx"],["application/olescript","axs"],["application/onenote","onetoc"],["application/patch-ops-error+xml","xer"],["application/pdf","pdf"],["application/pgp-encrypted","asc"],["application/pgp-signature","pgp"],["application/pics-rules","prf"],["application/pkcs-12","p12"],["application/pkcs-crl","crl"],["application/pkcs10","p10"],["application/pkcs7-mime",["p7c","p7m"]],["application/pkcs7-signature","p7s"],["application/pkcs8","p8"],["application/pkix-attr-cert","ac"],["application/pkix-cert",["cer","crt"]],["application/pkix-crl","crl"],["application/pkix-pkipath","pkipath"],["application/pkixcmp","pki"],["application/plain","text"],["application/pls+xml","pls"],["application/postscript",["ps","ai","eps"]],["application/powerpoint","ppt"],["application/pro_eng",["part","prt"]],["application/prs.cww","cww"],["application/pskc+xml","pskcxml"],["application/rdf+xml","rdf"],["application/reginfo+xml","rif"],["application/relax-ng-compact-syntax","rnc"],["application/resource-lists+xml","rl"],["application/resource-lists-diff+xml","rld"],["application/ringing-tones","rng"],["application/rls-services+xml","rs"],["application/rsd+xml","rsd"],["application/rss+xml","xml"],["application/rtf",["rtf","rtx"]],["application/sbml+xml","sbml"],["application/scvp-cv-request","scq"],["application/scvp-cv-response","scs"],["application/scvp-vp-request","spq"],["application/scvp-vp-response","spp"],["application/sdp","sdp"],["application/sea","sea"],["application/set","set"],["application/set-payment-initiation","setpay"],["application/set-registration-initiation","setreg"],["application/shf+xml","shf"],["application/sla","stl"],["application/smil",["smi","smil"]],["application/smil+xml","smi"],["application/solids","sol"],["application/sounder","sdr"],["application/sparql-query","rq"],["application/sparql-results+xml","srx"],["application/srgs","gram"],["application/srgs+xml","grxml"],["application/sru+xml","sru"],["application/ssml+xml","ssml"],["application/step",["step","stp"]],["application/streamingmedia","ssm"],["application/tei+xml","tei"],["application/thraud+xml","tfi"],["application/timestamped-data","tsd"],["application/toolbook","tbk"],["application/vda","vda"],["application/vnd.3gpp.pic-bw-large","plb"],["application/vnd.3gpp.pic-bw-small","psb"],["application/vnd.3gpp.pic-bw-var","pvb"],["application/vnd.3gpp2.tcap","tcap"],["application/vnd.3m.post-it-notes","pwn"],["application/vnd.accpac.simply.aso","aso"],["application/vnd.accpac.simply.imp","imp"],["application/vnd.acucobol","acu"],["application/vnd.acucorp","atc"],["application/vnd.adobe.air-application-installer-package+zip","air"],["application/vnd.adobe.fxp","fxp"],["application/vnd.adobe.xdp+xml","xdp"],["application/vnd.adobe.xfdf","xfdf"],["application/vnd.ahead.space","ahead"],["application/vnd.airzip.filesecure.azf","azf"],["application/vnd.airzip.filesecure.azs","azs"],["application/vnd.amazon.ebook","azw"],["application/vnd.americandynamics.acc","acc"],["application/vnd.amiga.ami","ami"],["application/vnd.android.package-archive","apk"],["application/vnd.anser-web-certificate-issue-initiation","cii"],["application/vnd.anser-web-funds-transfer-initiation","fti"],["application/vnd.antix.game-component","atx"],["application/vnd.apple.installer+xml","mpkg"],["application/vnd.apple.mpegurl","m3u8"],["application/vnd.aristanetworks.swi","swi"],["application/vnd.audiograph","aep"],["application/vnd.blueice.multipass","mpm"],["application/vnd.bmi","bmi"],["application/vnd.businessobjects","rep"],["application/vnd.chemdraw+xml","cdxml"],["application/vnd.chipnuts.karaoke-mmd","mmd"],["application/vnd.cinderella","cdy"],["application/vnd.claymore","cla"],["application/vnd.cloanto.rp9","rp9"],["application/vnd.clonk.c4group","c4g"],["application/vnd.cluetrust.cartomobile-config","c11amc"],["application/vnd.cluetrust.cartomobile-config-pkg","c11amz"],["application/vnd.commonspace","csp"],["application/vnd.contact.cmsg","cdbcmsg"],["application/vnd.cosmocaller","cmc"],["application/vnd.crick.clicker","clkx"],["application/vnd.crick.clicker.keyboard","clkk"],["application/vnd.crick.clicker.palette","clkp"],["application/vnd.crick.clicker.template","clkt"],["application/vnd.crick.clicker.wordbank","clkw"],["application/vnd.criticaltools.wbs+xml","wbs"],["application/vnd.ctc-posml","pml"],["application/vnd.cups-ppd","ppd"],["application/vnd.curl.car","car"],["application/vnd.curl.pcurl","pcurl"],["application/vnd.data-vision.rdz","rdz"],["application/vnd.denovo.fcselayout-link","fe_launch"],["application/vnd.dna","dna"],["application/vnd.dolby.mlp","mlp"],["application/vnd.dpgraph","dpg"],["application/vnd.dreamfactory","dfac"],["application/vnd.dvb.ait","ait"],["application/vnd.dvb.service","svc"],["application/vnd.dynageo","geo"],["application/vnd.ecowin.chart","mag"],["application/vnd.enliven","nml"],["application/vnd.epson.esf","esf"],["application/vnd.epson.msf","msf"],["application/vnd.epson.quickanime","qam"],["application/vnd.epson.salt","slt"],["application/vnd.epson.ssf","ssf"],["application/vnd.eszigno3+xml","es3"],["application/vnd.ezpix-album","ez2"],["application/vnd.ezpix-package","ez3"],["application/vnd.fdf","fdf"],["application/vnd.fdsn.seed","seed"],["application/vnd.flographit","gph"],["application/vnd.fluxtime.clip","ftc"],["application/vnd.framemaker","fm"],["application/vnd.frogans.fnc","fnc"],["application/vnd.frogans.ltf","ltf"],["application/vnd.fsc.weblaunch","fsc"],["application/vnd.fujitsu.oasys","oas"],["application/vnd.fujitsu.oasys2","oa2"],["application/vnd.fujitsu.oasys3","oa3"],["application/vnd.fujitsu.oasysgp","fg5"],["application/vnd.fujitsu.oasysprs","bh2"],["application/vnd.fujixerox.ddd","ddd"],["application/vnd.fujixerox.docuworks","xdw"],["application/vnd.fujixerox.docuworks.binder","xbd"],["application/vnd.fuzzysheet","fzs"],["application/vnd.genomatix.tuxedo","txd"],["application/vnd.geogebra.file","ggb"],["application/vnd.geogebra.tool","ggt"],["application/vnd.geometry-explorer","gex"],["application/vnd.geonext","gxt"],["application/vnd.geoplan","g2w"],["application/vnd.geospace","g3w"],["application/vnd.gmx","gmx"],["application/vnd.google-earth.kml+xml","kml"],["application/vnd.google-earth.kmz","kmz"],["application/vnd.grafeq","gqf"],["application/vnd.groove-account","gac"],["application/vnd.groove-help","ghf"],["application/vnd.groove-identity-message","gim"],["application/vnd.groove-injector","grv"],["application/vnd.groove-tool-message","gtm"],["application/vnd.groove-tool-template","tpl"],["application/vnd.groove-vcard","vcg"],["application/vnd.hal+xml","hal"],["application/vnd.handheld-entertainment+xml","zmm"],["application/vnd.hbci","hbci"],["application/vnd.hhe.lesson-player","les"],["application/vnd.hp-hpgl",["hgl","hpg","hpgl"]],["application/vnd.hp-hpid","hpid"],["application/vnd.hp-hps","hps"],["application/vnd.hp-jlyt","jlt"],["application/vnd.hp-pcl","pcl"],["application/vnd.hp-pclxl","pclxl"],["application/vnd.hydrostatix.sof-data","sfd-hdstx"],["application/vnd.hzn-3d-crossword","x3d"],["application/vnd.ibm.minipay","mpy"],["application/vnd.ibm.modcap","afp"],["application/vnd.ibm.rights-management","irm"],["application/vnd.ibm.secure-container","sc"],["application/vnd.iccprofile","icc"],["application/vnd.igloader","igl"],["application/vnd.immervision-ivp","ivp"],["application/vnd.immervision-ivu","ivu"],["application/vnd.insors.igm","igm"],["application/vnd.intercon.formnet","xpw"],["application/vnd.intergeo","i2g"],["application/vnd.intu.qbo","qbo"],["application/vnd.intu.qfx","qfx"],["application/vnd.ipunplugged.rcprofile","rcprofile"],["application/vnd.irepository.package+xml","irp"],["application/vnd.is-xpr","xpr"],["application/vnd.isac.fcs","fcs"],["application/vnd.jam","jam"],["application/vnd.jcp.javame.midlet-rms","rms"],["application/vnd.jisp","jisp"],["application/vnd.joost.joda-archive","joda"],["application/vnd.kahootz","ktz"],["application/vnd.kde.karbon","karbon"],["application/vnd.kde.kchart","chrt"],["application/vnd.kde.kformula","kfo"],["application/vnd.kde.kivio","flw"],["application/vnd.kde.kontour","kon"],["application/vnd.kde.kpresenter","kpr"],["application/vnd.kde.kspread","ksp"],["application/vnd.kde.kword","kwd"],["application/vnd.kenameaapp","htke"],["application/vnd.kidspiration","kia"],["application/vnd.kinar","kne"],["application/vnd.koan","skp"],["application/vnd.kodak-descriptor","sse"],["application/vnd.las.las+xml","lasxml"],["application/vnd.llamagraphics.life-balance.desktop","lbd"],["application/vnd.llamagraphics.life-balance.exchange+xml","lbe"],["application/vnd.lotus-1-2-3","123"],["application/vnd.lotus-approach","apr"],["application/vnd.lotus-freelance","pre"],["application/vnd.lotus-notes","nsf"],["application/vnd.lotus-organizer","org"],["application/vnd.lotus-screencam","scm"],["application/vnd.lotus-wordpro","lwp"],["application/vnd.macports.portpkg","portpkg"],["application/vnd.mcd","mcd"],["application/vnd.medcalcdata","mc1"],["application/vnd.mediastation.cdkey","cdkey"],["application/vnd.mfer","mwf"],["application/vnd.mfmp","mfm"],["application/vnd.micrografx.flo","flo"],["application/vnd.micrografx.igx","igx"],["application/vnd.mif","mif"],["application/vnd.mobius.daf","daf"],["application/vnd.mobius.dis","dis"],["application/vnd.mobius.mbk","mbk"],["application/vnd.mobius.mqy","mqy"],["application/vnd.mobius.msl","msl"],["application/vnd.mobius.plc","plc"],["application/vnd.mobius.txf","txf"],["application/vnd.mophun.application","mpn"],["application/vnd.mophun.certificate","mpc"],["application/vnd.mozilla.xul+xml","xul"],["application/vnd.ms-artgalry","cil"],["application/vnd.ms-cab-compressed","cab"],["application/vnd.ms-excel",["xls","xla","xlc","xlm","xlt","xlw","xlb","xll"]],["application/vnd.ms-excel.addin.macroenabled.12","xlam"],["application/vnd.ms-excel.sheet.binary.macroenabled.12","xlsb"],["application/vnd.ms-excel.sheet.macroenabled.12","xlsm"],["application/vnd.ms-excel.template.macroenabled.12","xltm"],["application/vnd.ms-fontobject","eot"],["application/vnd.ms-htmlhelp","chm"],["application/vnd.ms-ims","ims"],["application/vnd.ms-lrm","lrm"],["application/vnd.ms-officetheme","thmx"],["application/vnd.ms-outlook","msg"],["application/vnd.ms-pki.certstore","sst"],["application/vnd.ms-pki.pko","pko"],["application/vnd.ms-pki.seccat","cat"],["application/vnd.ms-pki.stl","stl"],["application/vnd.ms-pkicertstore","sst"],["application/vnd.ms-pkiseccat","cat"],["application/vnd.ms-pkistl","stl"],["application/vnd.ms-powerpoint",["ppt","pot","pps","ppa","pwz"]],["application/vnd.ms-powerpoint.addin.macroenabled.12","ppam"],["application/vnd.ms-powerpoint.presentation.macroenabled.12","pptm"],["application/vnd.ms-powerpoint.slide.macroenabled.12","sldm"],["application/vnd.ms-powerpoint.slideshow.macroenabled.12","ppsm"],["application/vnd.ms-powerpoint.template.macroenabled.12","potm"],["application/vnd.ms-project","mpp"],["application/vnd.ms-word.document.macroenabled.12","docm"],["application/vnd.ms-word.template.macroenabled.12","dotm"],["application/vnd.ms-works",["wks","wcm","wdb","wps"]],["application/vnd.ms-wpl","wpl"],["application/vnd.ms-xpsdocument","xps"],["application/vnd.mseq","mseq"],["application/vnd.musician","mus"],["application/vnd.muvee.style","msty"],["application/vnd.neurolanguage.nlu","nlu"],["application/vnd.noblenet-directory","nnd"],["application/vnd.noblenet-sealer","nns"],["application/vnd.noblenet-web","nnw"],["application/vnd.nokia.configuration-message","ncm"],["application/vnd.nokia.n-gage.data","ngdat"],["application/vnd.nokia.n-gage.symbian.install","n-gage"],["application/vnd.nokia.radio-preset","rpst"],["application/vnd.nokia.radio-presets","rpss"],["application/vnd.nokia.ringing-tone","rng"],["application/vnd.novadigm.edm","edm"],["application/vnd.novadigm.edx","edx"],["application/vnd.novadigm.ext","ext"],["application/vnd.oasis.opendocument.chart","odc"],["application/vnd.oasis.opendocument.chart-template","otc"],["application/vnd.oasis.opendocument.database","odb"],["application/vnd.oasis.opendocument.formula","odf"],["application/vnd.oasis.opendocument.formula-template","odft"],["application/vnd.oasis.opendocument.graphics","odg"],["application/vnd.oasis.opendocument.graphics-template","otg"],["application/vnd.oasis.opendocument.image","odi"],["application/vnd.oasis.opendocument.image-template","oti"],["application/vnd.oasis.opendocument.presentation","odp"],["application/vnd.oasis.opendocument.presentation-template","otp"],["application/vnd.oasis.opendocument.spreadsheet","ods"],["application/vnd.oasis.opendocument.spreadsheet-template","ots"],["application/vnd.oasis.opendocument.text","odt"],["application/vnd.oasis.opendocument.text-master","odm"],["application/vnd.oasis.opendocument.text-template","ott"],["application/vnd.oasis.opendocument.text-web","oth"],["application/vnd.olpc-sugar","xo"],["application/vnd.oma.dd2+xml","dd2"],["application/vnd.openofficeorg.extension","oxt"],["application/vnd.openxmlformats-officedocument.presentationml.presentation","pptx"],["application/vnd.openxmlformats-officedocument.presentationml.slide","sldx"],["application/vnd.openxmlformats-officedocument.presentationml.slideshow","ppsx"],["application/vnd.openxmlformats-officedocument.presentationml.template","potx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlsx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.template","xltx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.document","docx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.template","dotx"],["application/vnd.osgeo.mapguide.package","mgp"],["application/vnd.osgi.dp","dp"],["application/vnd.palm","pdb"],["application/vnd.pawaafile","paw"],["application/vnd.pg.format","str"],["application/vnd.pg.osasli","ei6"],["application/vnd.picsel","efif"],["application/vnd.pmi.widget","wg"],["application/vnd.pocketlearn","plf"],["application/vnd.powerbuilder6","pbd"],["application/vnd.previewsystems.box","box"],["application/vnd.proteus.magazine","mgz"],["application/vnd.publishare-delta-tree","qps"],["application/vnd.pvi.ptid1","ptid"],["application/vnd.quark.quarkxpress","qxd"],["application/vnd.realvnc.bed","bed"],["application/vnd.recordare.musicxml","mxl"],["application/vnd.recordare.musicxml+xml","musicxml"],["application/vnd.rig.cryptonote","cryptonote"],["application/vnd.rim.cod","cod"],["application/vnd.rn-realmedia","rm"],["application/vnd.rn-realplayer","rnx"],["application/vnd.route66.link66+xml","link66"],["application/vnd.sailingtracker.track","st"],["application/vnd.seemail","see"],["application/vnd.sema","sema"],["application/vnd.semd","semd"],["application/vnd.semf","semf"],["application/vnd.shana.informed.formdata","ifm"],["application/vnd.shana.informed.formtemplate","itp"],["application/vnd.shana.informed.interchange","iif"],["application/vnd.shana.informed.package","ipk"],["application/vnd.simtech-mindmapper","twd"],["application/vnd.smaf","mmf"],["application/vnd.smart.teacher","teacher"],["application/vnd.solent.sdkm+xml","sdkm"],["application/vnd.spotfire.dxp","dxp"],["application/vnd.spotfire.sfs","sfs"],["application/vnd.stardivision.calc","sdc"],["application/vnd.stardivision.draw","sda"],["application/vnd.stardivision.impress","sdd"],["application/vnd.stardivision.math","smf"],["application/vnd.stardivision.writer","sdw"],["application/vnd.stardivision.writer-global","sgl"],["application/vnd.stepmania.stepchart","sm"],["application/vnd.sun.xml.calc","sxc"],["application/vnd.sun.xml.calc.template","stc"],["application/vnd.sun.xml.draw","sxd"],["application/vnd.sun.xml.draw.template","std"],["application/vnd.sun.xml.impress","sxi"],["application/vnd.sun.xml.impress.template","sti"],["application/vnd.sun.xml.math","sxm"],["application/vnd.sun.xml.writer","sxw"],["application/vnd.sun.xml.writer.global","sxg"],["application/vnd.sun.xml.writer.template","stw"],["application/vnd.sus-calendar","sus"],["application/vnd.svd","svd"],["application/vnd.symbian.install","sis"],["application/vnd.syncml+xml","xsm"],["application/vnd.syncml.dm+wbxml","bdm"],["application/vnd.syncml.dm+xml","xdm"],["application/vnd.tao.intent-module-archive","tao"],["application/vnd.tmobile-livetv","tmo"],["application/vnd.trid.tpt","tpt"],["application/vnd.triscape.mxs","mxs"],["application/vnd.trueapp","tra"],["application/vnd.ufdl","ufd"],["application/vnd.uiq.theme","utz"],["application/vnd.umajin","umj"],["application/vnd.unity","unityweb"],["application/vnd.uoml+xml","uoml"],["application/vnd.vcx","vcx"],["application/vnd.visio","vsd"],["application/vnd.visionary","vis"],["application/vnd.vsf","vsf"],["application/vnd.wap.wbxml","wbxml"],["application/vnd.wap.wmlc","wmlc"],["application/vnd.wap.wmlscriptc","wmlsc"],["application/vnd.webturbo","wtb"],["application/vnd.wolfram.player","nbp"],["application/vnd.wordperfect","wpd"],["application/vnd.wqd","wqd"],["application/vnd.wt.stf","stf"],["application/vnd.xara",["web","xar"]],["application/vnd.xfdl","xfdl"],["application/vnd.yamaha.hv-dic","hvd"],["application/vnd.yamaha.hv-script","hvs"],["application/vnd.yamaha.hv-voice","hvp"],["application/vnd.yamaha.openscoreformat","osf"],["application/vnd.yamaha.openscoreformat.osfpvg+xml","osfpvg"],["application/vnd.yamaha.smaf-audio","saf"],["application/vnd.yamaha.smaf-phrase","spf"],["application/vnd.yellowriver-custom-menu","cmp"],["application/vnd.zul","zir"],["application/vnd.zzazz.deck+xml","zaz"],["application/vocaltec-media-desc","vmd"],["application/vocaltec-media-file","vmf"],["application/voicexml+xml","vxml"],["application/widget","wgt"],["application/winhlp","hlp"],["application/wordperfect",["wp","wp5","wp6","wpd"]],["application/wordperfect6.0",["w60","wp5"]],["application/wordperfect6.1","w61"],["application/wsdl+xml","wsdl"],["application/wspolicy+xml","wspolicy"],["application/x-123","wk1"],["application/x-7z-compressed","7z"],["application/x-abiword","abw"],["application/x-ace-compressed","ace"],["application/x-aim","aim"],["application/x-authorware-bin","aab"],["application/x-authorware-map","aam"],["application/x-authorware-seg","aas"],["application/x-bcpio","bcpio"],["application/x-binary","bin"],["application/x-binhex40","hqx"],["application/x-bittorrent","torrent"],["application/x-bsh",["bsh","sh","shar"]],["application/x-bytecode.elisp","elc"],["application/x-bytecode.python","pyc"],["application/x-bzip","bz"],["application/x-bzip2",["boz","bz2"]],["application/x-cdf","cdf"],["application/x-cdlink","vcd"],["application/x-chat",["cha","chat"]],["application/x-chess-pgn","pgn"],["application/x-cmu-raster","ras"],["application/x-cocoa","cco"],["application/x-compactpro","cpt"],["application/x-compress","z"],["application/x-compressed",["tgz","gz","z","zip"]],["application/x-conference","nsc"],["application/x-cpio","cpio"],["application/x-cpt","cpt"],["application/x-csh","csh"],["application/x-debian-package","deb"],["application/x-deepv","deepv"],["application/x-director",["dir","dcr","dxr"]],["application/x-doom","wad"],["application/x-dtbncx+xml","ncx"],["application/x-dtbook+xml","dtb"],["application/x-dtbresource+xml","res"],["application/x-dvi","dvi"],["application/x-elc","elc"],["application/x-envoy",["env","evy"]],["application/x-esrehber","es"],["application/x-excel",["xls","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/x-font-bdf","bdf"],["application/x-font-ghostscript","gsf"],["application/x-font-linux-psf","psf"],["application/x-font-otf","otf"],["application/x-font-pcf","pcf"],["application/x-font-snf","snf"],["application/x-font-ttf","ttf"],["application/x-font-type1","pfa"],["application/x-font-woff","woff"],["application/x-frame","mif"],["application/x-freelance","pre"],["application/x-futuresplash","spl"],["application/x-gnumeric","gnumeric"],["application/x-gsp","gsp"],["application/x-gss","gss"],["application/x-gtar","gtar"],["application/x-gzip",["gz","gzip"]],["application/x-hdf","hdf"],["application/x-helpfile",["help","hlp"]],["application/x-httpd-imap","imap"],["application/x-ima","ima"],["application/x-internet-signup",["ins","isp"]],["application/x-internett-signup","ins"],["application/x-inventor","iv"],["application/x-ip2","ip"],["application/x-iphone","iii"],["application/x-java-class","class"],["application/x-java-commerce","jcm"],["application/x-java-jnlp-file","jnlp"],["application/x-javascript","js"],["application/x-koan",["skd","skm","skp","skt"]],["application/x-ksh","ksh"],["application/x-latex",["latex","ltx"]],["application/x-lha","lha"],["application/x-lisp","lsp"],["application/x-livescreen","ivy"],["application/x-lotus","wq1"],["application/x-lotusscreencam","scm"],["application/x-lzh","lzh"],["application/x-lzx","lzx"],["application/x-mac-binhex40","hqx"],["application/x-macbinary","bin"],["application/x-magic-cap-package-1.0","mc$"],["application/x-mathcad","mcd"],["application/x-meme","mm"],["application/x-midi",["mid","midi"]],["application/x-mif","mif"],["application/x-mix-transfer","nix"],["application/x-mobipocket-ebook","prc"],["application/x-mplayer2","asx"],["application/x-ms-application","application"],["application/x-ms-wmd","wmd"],["application/x-ms-wmz","wmz"],["application/x-ms-xbap","xbap"],["application/x-msaccess","mdb"],["application/x-msbinder","obd"],["application/x-mscardfile","crd"],["application/x-msclip","clp"],["application/x-msdownload",["exe","dll"]],["application/x-msexcel",["xls","xla","xlw"]],["application/x-msmediaview",["mvb","m13","m14"]],["application/x-msmetafile","wmf"],["application/x-msmoney","mny"],["application/x-mspowerpoint","ppt"],["application/x-mspublisher","pub"],["application/x-msschedule","scd"],["application/x-msterminal","trm"],["application/x-mswrite","wri"],["application/x-navi-animation","ani"],["application/x-navidoc","nvd"],["application/x-navimap","map"],["application/x-navistyle","stl"],["application/x-netcdf",["cdf","nc"]],["application/x-newton-compatible-pkg","pkg"],["application/x-nokia-9000-communicator-add-on-software","aos"],["application/x-omc","omc"],["application/x-omcdatamaker","omcd"],["application/x-omcregerator","omcr"],["application/x-pagemaker",["pm4","pm5"]],["application/x-pcl","pcl"],["application/x-perfmon",["pma","pmc","pml","pmr","pmw"]],["application/x-pixclscript","plx"],["application/x-pkcs10","p10"],["application/x-pkcs12",["p12","pfx"]],["application/x-pkcs7-certificates",["p7b","spc"]],["application/x-pkcs7-certreqresp","p7r"],["application/x-pkcs7-mime",["p7m","p7c"]],["application/x-pkcs7-signature",["p7s","p7a"]],["application/x-pointplus","css"],["application/x-portable-anymap","pnm"],["application/x-project",["mpc","mpt","mpv","mpx"]],["application/x-qpro","wb1"],["application/x-rar-compressed","rar"],["application/x-rtf","rtf"],["application/x-sdp","sdp"],["application/x-sea","sea"],["application/x-seelogo","sl"],["application/x-sh","sh"],["application/x-shar",["shar","sh"]],["application/x-shockwave-flash","swf"],["application/x-silverlight-app","xap"],["application/x-sit","sit"],["application/x-sprite",["spr","sprite"]],["application/x-stuffit","sit"],["application/x-stuffitx","sitx"],["application/x-sv4cpio","sv4cpio"],["application/x-sv4crc","sv4crc"],["application/x-tar","tar"],["application/x-tbook",["sbk","tbk"]],["application/x-tcl","tcl"],["application/x-tex","tex"],["application/x-tex-tfm","tfm"],["application/x-texinfo",["texi","texinfo"]],["application/x-troff",["roff","t","tr"]],["application/x-troff-man","man"],["application/x-troff-me","me"],["application/x-troff-ms","ms"],["application/x-troff-msvideo","avi"],["application/x-ustar","ustar"],["application/x-visio",["vsd","vst","vsw"]],["application/x-vnd.audioexplosion.mzz","mzz"],["application/x-vnd.ls-xpix","xpix"],["application/x-vrml","vrml"],["application/x-wais-source",["src","wsrc"]],["application/x-winhelp","hlp"],["application/x-wintalk","wtk"],["application/x-world",["wrl","svr"]],["application/x-wpwin","wpd"],["application/x-wri","wri"],["application/x-x509-ca-cert",["cer","crt","der"]],["application/x-x509-user-cert","crt"],["application/x-xfig","fig"],["application/x-xpinstall","xpi"],["application/x-zip-compressed","zip"],["application/xcap-diff+xml","xdf"],["application/xenc+xml","xenc"],["application/xhtml+xml","xhtml"],["application/xml","xml"],["application/xml-dtd","dtd"],["application/xop+xml","xop"],["application/xslt+xml","xslt"],["application/xspf+xml","xspf"],["application/xv+xml","mxml"],["application/yang","yang"],["application/yin+xml","yin"],["application/ynd.ms-pkipko","pko"],["application/zip","zip"],["audio/adpcm","adp"],["audio/aiff",["aiff","aif","aifc"]],["audio/basic",["snd","au"]],["audio/it","it"],["audio/make",["funk","my","pfunk"]],["audio/make.my.funk","pfunk"],["audio/mid",["mid","rmi"]],["audio/midi",["midi","kar","mid"]],["audio/mod","mod"],["audio/mp4","mp4a"],["audio/mpeg",["mpga","mp3","m2a","mp2","mpa","mpg"]],["audio/mpeg3","mp3"],["audio/nspaudio",["la","lma"]],["audio/ogg","oga"],["audio/s3m","s3m"],["audio/tsp-audio","tsi"],["audio/tsplayer","tsp"],["audio/vnd.dece.audio","uva"],["audio/vnd.digital-winds","eol"],["audio/vnd.dra","dra"],["audio/vnd.dts","dts"],["audio/vnd.dts.hd","dtshd"],["audio/vnd.lucent.voice","lvp"],["audio/vnd.ms-playready.media.pya","pya"],["audio/vnd.nuera.ecelp4800","ecelp4800"],["audio/vnd.nuera.ecelp7470","ecelp7470"],["audio/vnd.nuera.ecelp9600","ecelp9600"],["audio/vnd.qcelp","qcp"],["audio/vnd.rip","rip"],["audio/voc","voc"],["audio/voxware","vox"],["audio/wav","wav"],["audio/webm","weba"],["audio/x-aac","aac"],["audio/x-adpcm","snd"],["audio/x-aiff",["aiff","aif","aifc"]],["audio/x-au","au"],["audio/x-gsm",["gsd","gsm"]],["audio/x-jam","jam"],["audio/x-liveaudio","lam"],["audio/x-mid",["mid","midi"]],["audio/x-midi",["midi","mid"]],["audio/x-mod","mod"],["audio/x-mpeg","mp2"],["audio/x-mpeg-3","mp3"],["audio/x-mpegurl","m3u"],["audio/x-mpequrl","m3u"],["audio/x-ms-wax","wax"],["audio/x-ms-wma","wma"],["audio/x-nspaudio",["la","lma"]],["audio/x-pn-realaudio",["ra","ram","rm","rmm","rmp"]],["audio/x-pn-realaudio-plugin",["ra","rmp","rpm"]],["audio/x-psid","sid"],["audio/x-realaudio","ra"],["audio/x-twinvq","vqf"],["audio/x-twinvq-plugin",["vqe","vql"]],["audio/x-vnd.audioexplosion.mjuicemediafile","mjf"],["audio/x-voc","voc"],["audio/x-wav","wav"],["audio/xm","xm"],["chemical/x-cdx","cdx"],["chemical/x-cif","cif"],["chemical/x-cmdf","cmdf"],["chemical/x-cml","cml"],["chemical/x-csml","csml"],["chemical/x-pdb",["pdb","xyz"]],["chemical/x-xyz","xyz"],["drawing/x-dwf","dwf"],["i-world/i-vrml","ivr"],["image/bmp",["bmp","bm"]],["image/cgm","cgm"],["image/cis-cod","cod"],["image/cmu-raster",["ras","rast"]],["image/fif","fif"],["image/florian",["flo","turbot"]],["image/g3fax","g3"],["image/gif","gif"],["image/ief",["ief","iefs"]],["image/jpeg",["jpeg","jpe","jpg","jfif","jfif-tbnl"]],["image/jutvision","jut"],["image/ktx","ktx"],["image/naplps",["nap","naplps"]],["image/pict",["pic","pict"]],["image/pipeg","jfif"],["image/pjpeg",["jfif","jpe","jpeg","jpg"]],["image/png",["png","x-png"]],["image/prs.btif","btif"],["image/svg+xml","svg"],["image/tiff",["tif","tiff"]],["image/vasa","mcf"],["image/vnd.adobe.photoshop","psd"],["image/vnd.dece.graphic","uvi"],["image/vnd.djvu","djvu"],["image/vnd.dvb.subtitle","sub"],["image/vnd.dwg",["dwg","dxf","svf"]],["image/vnd.dxf","dxf"],["image/vnd.fastbidsheet","fbs"],["image/vnd.fpx","fpx"],["image/vnd.fst","fst"],["image/vnd.fujixerox.edmics-mmr","mmr"],["image/vnd.fujixerox.edmics-rlc","rlc"],["image/vnd.ms-modi","mdi"],["image/vnd.net-fpx",["fpx","npx"]],["image/vnd.rn-realflash","rf"],["image/vnd.rn-realpix","rp"],["image/vnd.wap.wbmp","wbmp"],["image/vnd.xiff","xif"],["image/webp","webp"],["image/x-cmu-raster","ras"],["image/x-cmx","cmx"],["image/x-dwg",["dwg","dxf","svf"]],["image/x-freehand","fh"],["image/x-icon","ico"],["image/x-jg","art"],["image/x-jps","jps"],["image/x-niff",["niff","nif"]],["image/x-pcx","pcx"],["image/x-pict",["pct","pic"]],["image/x-portable-anymap","pnm"],["image/x-portable-bitmap","pbm"],["image/x-portable-graymap","pgm"],["image/x-portable-greymap","pgm"],["image/x-portable-pixmap","ppm"],["image/x-quicktime",["qif","qti","qtif"]],["image/x-rgb","rgb"],["image/x-tiff",["tif","tiff"]],["image/x-windows-bmp","bmp"],["image/x-xbitmap","xbm"],["image/x-xbm","xbm"],["image/x-xpixmap",["xpm","pm"]],["image/x-xwd","xwd"],["image/x-xwindowdump","xwd"],["image/xbm","xbm"],["image/xpm","xpm"],["message/rfc822",["eml","mht","mhtml","nws","mime"]],["model/iges",["iges","igs"]],["model/mesh","msh"],["model/vnd.collada+xml","dae"],["model/vnd.dwf","dwf"],["model/vnd.gdl","gdl"],["model/vnd.gtw","gtw"],["model/vnd.mts","mts"],["model/vnd.vtu","vtu"],["model/vrml",["vrml","wrl","wrz"]],["model/x-pov","pov"],["multipart/x-gzip","gzip"],["multipart/x-ustar","ustar"],["multipart/x-zip","zip"],["music/crescendo",["mid","midi"]],["music/x-karaoke","kar"],["paleovu/x-pv","pvu"],["text/asp","asp"],["text/calendar","ics"],["text/css","css"],["text/csv","csv"],["text/ecmascript","js"],["text/h323","323"],["text/html",["html","htm","stm","acgi","htmls","htx","shtml"]],["text/iuls","uls"],["text/javascript","js"],["text/mcf","mcf"],["text/n3","n3"],["text/pascal","pas"],["text/plain",["txt","bas","c","h","c++","cc","com","conf","cxx","def","f","f90","for","g","hh","idc","jav","java","list","log","lst","m","mar","pl","sdml","text"]],["text/plain-bas","par"],["text/prs.lines.tag","dsc"],["text/richtext",["rtx","rt","rtf"]],["text/scriplet","wsc"],["text/scriptlet","sct"],["text/sgml",["sgm","sgml"]],["text/tab-separated-values","tsv"],["text/troff","t"],["text/turtle","ttl"],["text/uri-list",["uni","unis","uri","uris"]],["text/vnd.abc","abc"],["text/vnd.curl","curl"],["text/vnd.curl.dcurl","dcurl"],["text/vnd.curl.mcurl","mcurl"],["text/vnd.curl.scurl","scurl"],["text/vnd.fly","fly"],["text/vnd.fmi.flexstor","flx"],["text/vnd.graphviz","gv"],["text/vnd.in3d.3dml","3dml"],["text/vnd.in3d.spot","spot"],["text/vnd.rn-realtext","rt"],["text/vnd.sun.j2me.app-descriptor","jad"],["text/vnd.wap.wml","wml"],["text/vnd.wap.wmlscript","wmls"],["text/webviewhtml","htt"],["text/x-asm",["asm","s"]],["text/x-audiosoft-intra","aip"],["text/x-c",["c","cc","cpp"]],["text/x-component","htc"],["text/x-fortran",["for","f","f77","f90"]],["text/x-h",["h","hh"]],["text/x-java-source",["java","jav"]],["text/x-java-source,java","java"],["text/x-la-asf","lsx"],["text/x-m","m"],["text/x-pascal","p"],["text/x-script","hlb"],["text/x-script.csh","csh"],["text/x-script.elisp","el"],["text/x-script.guile","scm"],["text/x-script.ksh","ksh"],["text/x-script.lisp","lsp"],["text/x-script.perl","pl"],["text/x-script.perl-module","pm"],["text/x-script.phyton","py"],["text/x-script.rexx","rexx"],["text/x-script.scheme","scm"],["text/x-script.sh","sh"],["text/x-script.tcl","tcl"],["text/x-script.tcsh","tcsh"],["text/x-script.zsh","zsh"],["text/x-server-parsed-html",["shtml","ssi"]],["text/x-setext","etx"],["text/x-sgml",["sgm","sgml"]],["text/x-speech",["spc","talk"]],["text/x-uil","uil"],["text/x-uuencode",["uu","uue"]],["text/x-vcalendar","vcs"],["text/x-vcard","vcf"],["text/xml","xml"],["video/3gpp","3gp"],["video/3gpp2","3g2"],["video/animaflex","afl"],["video/avi","avi"],["video/avs-video","avs"],["video/dl","dl"],["video/fli","fli"],["video/gl","gl"],["video/h261","h261"],["video/h263","h263"],["video/h264","h264"],["video/jpeg","jpgv"],["video/jpm","jpm"],["video/mj2","mj2"],["video/mp4","mp4"],["video/mpeg",["mpeg","mp2","mpa","mpe","mpg","mpv2","m1v","m2v","mp3"]],["video/msvideo","avi"],["video/ogg","ogv"],["video/quicktime",["mov","qt","moov"]],["video/vdo","vdo"],["video/vivo",["viv","vivo"]],["video/vnd.dece.hd","uvh"],["video/vnd.dece.mobile","uvm"],["video/vnd.dece.pd","uvp"],["video/vnd.dece.sd","uvs"],["video/vnd.dece.video","uvv"],["video/vnd.fvt","fvt"],["video/vnd.mpegurl","mxu"],["video/vnd.ms-playready.media.pyv","pyv"],["video/vnd.rn-realvideo","rv"],["video/vnd.uvvu.mp4","uvu"],["video/vnd.vivo",["viv","vivo"]],["video/vosaic","vos"],["video/webm","webm"],["video/x-amt-demorun","xdr"],["video/x-amt-showrun","xsr"],["video/x-atomic3d-feature","fmf"],["video/x-dl","dl"],["video/x-dv",["dif","dv"]],["video/x-f4v","f4v"],["video/x-fli","fli"],["video/x-flv","flv"],["video/x-gl","gl"],["video/x-isvideo","isu"],["video/x-la-asf",["lsf","lsx"]],["video/x-m4v","m4v"],["video/x-motion-jpeg","mjpg"],["video/x-mpeg",["mp3","mp2"]],["video/x-mpeq2a","mp2"],["video/x-ms-asf",["asf","asr","asx"]],["video/x-ms-asf-plugin","asx"],["video/x-ms-wm","wm"],["video/x-ms-wmv","wmv"],["video/x-ms-wmx","wmx"],["video/x-ms-wvx","wvx"],["video/x-msvideo","avi"],["video/x-qtc","qtc"],["video/x-scm","scm"],["video/x-sgi-movie",["movie","mv"]],["windows/metafile","wmf"],["www/mime","mime"],["x-conference/x-cooltalk","ice"],["x-music/x-midi",["mid","midi"]],["x-world/x-3dmf",["3dm","3dmf","qd3","qd3d"]],["x-world/x-svr","svr"],["x-world/x-vrml",["flr","vrml","wrl","wrz","xaf","xof"]],["x-world/x-vrt","vrt"],["xgl/drawing","xgz"],["xgl/movie","xmz"]]),g=new Map([["123","application/vnd.lotus-1-2-3"],["323","text/h323"],["*","application/octet-stream"],["3dm","x-world/x-3dmf"],["3dmf","x-world/x-3dmf"],["3dml","text/vnd.in3d.3dml"],["3g2","video/3gpp2"],["3gp","video/3gpp"],["7z","application/x-7z-compressed"],["a","application/octet-stream"],["aab","application/x-authorware-bin"],["aac","audio/x-aac"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abc","text/vnd.abc"],["abw","application/x-abiword"],["ac","application/pkix-attr-cert"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acgi","text/html"],["acu","application/vnd.acucobol"],["acx","application/internet-property-stream"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afl","video/animaflex"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/postscript"],["aif",["audio/aiff","audio/x-aiff"]],["aifc",["audio/aiff","audio/x-aiff"]],["aiff",["audio/aiff","audio/x-aiff"]],["aim","application/x-aim"],["aip","text/x-audiosoft-intra"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["ani","application/x-navi-animation"],["aos","application/x-nokia-9000-communicator-add-on-software"],["apk","application/vnd.android.package-archive"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["aps","application/mime"],["arc","application/octet-stream"],["arj",["application/arj","application/octet-stream"]],["art","image/x-jg"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asp","text/asp"],["asr","video/x-ms-asf"],["asx",["video/x-ms-asf","application/x-mplayer2","video/x-ms-asf-plugin"]],["atc","application/vnd.acucorp"],["atomcat","application/atomcat+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au",["audio/basic","audio/x-au"]],["avi",["video/avi","video/msvideo","application/x-troff-msvideo","video/x-msvideo"]],["avs","video/avs-video"],["aw","application/applixware"],["axs","application/olescript"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azw","application/vnd.amazon.ebook"],["bas","text/plain"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin",["application/octet-stream","application/mac-binary","application/macbinary","application/x-macbinary","application/x-binary"]],["bm","image/bmp"],["bmi","application/vnd.bmi"],["bmp",["image/bmp","image/x-windows-bmp"]],["boo","application/book"],["book","application/book"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bsh","application/x-bsh"],["btif","image/prs.btif"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c",["text/plain","text/x-c"]],["c++","text/plain"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["c4g","application/vnd.clonk.c4group"],["cab","application/vnd.ms-cab-compressed"],["car","application/vnd.curl.car"],["cat",["application/vnd.ms-pkiseccat","application/vnd.ms-pki.seccat"]],["cc",["text/plain","text/x-c"]],["ccad","application/clariscad"],["cco","application/x-cocoa"],["ccxml","application/ccxml+xml,"],["cdbcmsg","application/vnd.contact.cmsg"],["cdf",["application/cdf","application/x-cdf","application/x-netcdf"]],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer",["application/pkix-cert","application/x-x509-ca-cert"]],["cgm","image/cgm"],["cha","application/x-chat"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cla","application/vnd.claymore"],["class",["application/octet-stream","application/java","application/java-byte-code","application/java-vm","application/x-java-class"]],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod",["image/cis-cod","application/vnd.rim.cod"]],["com",["application/octet-stream","text/plain"]],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt",["application/mac-compactpro","application/x-compactpro","application/x-cpt"]],["crd","application/x-mscardfile"],["crl",["application/pkix-crl","application/pkcs-crl"]],["crt",["application/pkix-cert","application/x-x509-user-cert","application/x-x509-ca-cert"]],["cryptonote","application/vnd.rig.cryptonote"],["csh",["text/x-script.csh","application/x-csh"]],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["css",["text/css","application/x-pointplus"]],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxx","text/plain"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["davmount","application/davmount+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["deb","application/x-debian-package"],["deepv","application/x-deepv"],["def","text/plain"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dif","video/x-dv"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["djvu","image/vnd.djvu"],["dl",["video/dl","video/x-dl"]],["dll","application/x-msdownload"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.document.macroenabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroenabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp",["application/commonground","application/vnd.osgi.dp"]],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drw","application/drafting"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dv","video/x-dv"],["dvi","application/x-dvi"],["dwf",["model/vnd.dwf","drawing/x-dwf"]],["dwg",["application/acad","image/vnd.dwg","image/x-dwg"]],["dxf",["application/dxf","image/vnd.dwg","image/vnd.dxf","image/x-dwg"]],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["el","text/x-script.elisp"],["elc",["application/x-elc","application/x-bytecode.elisp"]],["eml","message/rfc822"],["emma","application/emma+xml"],["env","application/x-envoy"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es",["application/ecmascript","application/x-esrehber"]],["es3","application/vnd.eszigno3+xml"],["esf","application/vnd.epson.esf"],["etx","text/x-setext"],["evy",["application/envoy","application/x-envoy"]],["exe",["application/octet-stream","application/x-msdownload"]],["exi","application/exi"],["ext","application/vnd.novadigm.ext"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f",["text/plain","text/x-fortran"]],["f4v","video/x-f4v"],["f77","text/x-fortran"],["f90",["text/plain","text/x-fortran"]],["fbs","image/vnd.fastbidsheet"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fh","image/x-freehand"],["fif",["application/fractals","image/fif"]],["fig","application/x-xfig"],["fli",["video/fli","video/x-fli"]],["flo",["image/florian","application/vnd.micrografx.flo"]],["flr","x-world/x-vrml"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fmf","video/x-atomic3d-feature"],["fnc","application/vnd.frogans.fnc"],["for",["text/plain","text/x-fortran"]],["fpx",["image/vnd.fpx","image/vnd.net-fpx"]],["frl","application/freeloader"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["funk","audio/make"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g","text/plain"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gdl","model/vnd.gdl"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["gl",["video/gl","video/x-gl"]],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gph","application/vnd.flographit"],["gqf","application/vnd.grafeq"],["gram","application/srgs"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsd","audio/x-gsm"],["gsf","application/x-font-ghostscript"],["gsm","audio/x-gsm"],["gsp","application/x-gsp"],["gss","application/x-gss"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxt","application/vnd.geonext"],["gz",["application/x-gzip","application/x-compressed"]],["gzip",["multipart/x-gzip","application/x-gzip"]],["h",["text/plain","text/x-h"]],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hdf","application/x-hdf"],["help","application/x-helpfile"],["hgl","application/vnd.hp-hpgl"],["hh",["text/plain","text/x-h"]],["hlb","text/x-script"],["hlp",["application/winhlp","application/hlp","application/x-helpfile","application/x-winhelp"]],["hpg","application/vnd.hp-hpgl"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx",["application/mac-binhex40","application/binhex","application/binhex4","application/mac-binhex","application/x-binhex40","application/x-mac-binhex40"]],["hta","application/hta"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["htmls","text/html"],["htt","text/webviewhtml"],["htx","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["ico","image/x-icon"],["ics","text/calendar"],["idc","text/plain"],["ief","image/ief"],["iefs","image/ief"],["ifm","application/vnd.shana.informed.formdata"],["iges",["application/iges","model/iges"]],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs",["application/iges","model/iges"]],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["iii","application/x-iphone"],["ima","application/x-ima"],["imap","application/x-httpd-imap"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["inf","application/inf"],["ins",["application/x-internet-signup","application/x-internett-signup"]],["ip","application/x-ip2"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["isp","application/x-internet-signup"],["isu","video/x-isvideo"],["it","audio/it"],["itp","application/vnd.shana.informed.formtemplate"],["iv","application/x-inventor"],["ivp","application/vnd.immervision-ivp"],["ivr","i-world/i-vrml"],["ivu","application/vnd.immervision-ivu"],["ivy","application/x-livescreen"],["jad","text/vnd.sun.j2me.app-descriptor"],["jam",["application/vnd.jam","audio/x-jam"]],["jar","application/java-archive"],["jav",["text/plain","text/x-java-source"]],["java",["text/plain","text/x-java-source,java","text/x-java-source"]],["jcm","application/x-java-commerce"],["jfif",["image/pipeg","image/jpeg","image/pjpeg"]],["jfif-tbnl","image/jpeg"],["jisp","application/vnd.jisp"],["jlt","application/vnd.hp-jlyt"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jpe",["image/jpeg","image/pjpeg"]],["jpeg",["image/jpeg","image/pjpeg"]],["jpg",["image/jpeg","image/pjpeg"]],["jpgv","video/jpeg"],["jpm","video/jpm"],["jps","image/x-jps"],["js",["application/javascript","application/ecmascript","text/javascript","text/ecmascript","application/x-javascript"]],["json","application/json"],["jut","image/jutvision"],["kar",["audio/midi","music/x-karaoke"]],["karbon","application/vnd.kde.karbon"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["ksh",["application/x-ksh","text/x-script.ksh"]],["ksp","application/vnd.kde.kspread"],["ktx","image/ktx"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["la",["audio/nspaudio","audio/x-nspaudio"]],["lam","audio/x-liveaudio"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["lha",["application/octet-stream","application/lha","application/x-lha"]],["lhx","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["lma",["audio/nspaudio","audio/x-nspaudio"]],["log","text/plain"],["lrm","application/vnd.ms-lrm"],["lsf","video/x-la-asf"],["lsp",["application/x-lisp","text/x-script.lisp"]],["lst","text/plain"],["lsx",["video/x-la-asf","text/x-la-asf"]],["ltf","application/vnd.frogans.ltf"],["ltx","application/x-latex"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh",["application/octet-stream","application/x-lzh"]],["lzx",["application/lzx","application/octet-stream","application/x-lzx"]],["m",["text/plain","text/x-m"]],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m1v","video/mpeg"],["m21","application/mp21"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3u",["audio/x-mpegurl","audio/x-mpequrl"]],["m3u8","application/vnd.apple.mpegurl"],["m4v","video/x-m4v"],["ma","application/mathematica"],["mads","application/mads+xml"],["mag","application/vnd.ecowin.chart"],["man","application/x-troff-man"],["map","application/x-navimap"],["mar","text/plain"],["mathml","application/mathml+xml"],["mbd","application/mbedlet"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc$","application/x-magic-cap-package-1.0"],["mc1","application/vnd.medcalcdata"],["mcd",["application/mcad","application/vnd.mcd","application/x-mathcad"]],["mcf",["image/vasa","text/mcf"]],["mcp","application/netmc"],["mcurl","text/vnd.curl.mcurl"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["me","application/x-troff-me"],["meta4","application/metalink4+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mht","message/rfc822"],["mhtml","message/rfc822"],["mid",["audio/mid","audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["midi",["audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["mif",["application/vnd.mif","application/x-mif","application/x-frame"]],["mime",["message/rfc822","www/mime"]],["mj2","video/mj2"],["mjf","audio/x-vnd.audioexplosion.mjuicemediafile"],["mjpg","video/x-motion-jpeg"],["mlp","application/vnd.dolby.mlp"],["mm",["application/base64","application/x-meme"]],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mme","application/base64"],["mmf","application/vnd.smaf"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mny","application/x-msmoney"],["mod",["audio/mod","audio/x-mod"]],["mods","application/mods+xml"],["moov","video/quicktime"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2",["video/mpeg","audio/mpeg","video/x-mpeg","audio/x-mpeg","video/x-mpeq2a"]],["mp3",["audio/mpeg","audio/mpeg3","video/mpeg","audio/x-mpeg-3","video/x-mpeg"]],["mp4",["video/mp4","application/mp4"]],["mp4a","audio/mp4"],["mpa",["video/mpeg","audio/mpeg"]],["mpc",["application/vnd.mophun.certificate","application/x-project"]],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg",["video/mpeg","audio/mpeg"]],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/x-project"],["mpv","application/x-project"],["mpv2","video/mpeg"],["mpx","application/x-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","application/x-troff-ms"],["mscml","application/mediaservercontrol+xml"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msl","application/vnd.mobius.msl"],["msty","application/vnd.muvee.style"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musicxml","application/vnd.recordare.musicxml+xml"],["mv","video/x-sgi-movie"],["mvb","application/x-msmediaview"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["my","audio/make"],["mzz","application/x-vnd.audioexplosion.mzz"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nap","image/naplps"],["naplps","image/naplps"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncm","application/vnd.nokia.configuration-message"],["ncx","application/x-dtbncx+xml"],["ngdat","application/vnd.nokia.n-gage.data"],["nif","image/x-niff"],["niff","image/x-niff"],["nix","application/x-mix-transfer"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nvd","application/x-navidoc"],["nws","message/rfc822"],["o","application/octet-stream"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omc","application/x-omc"],["omcd","application/x-omcdatamaker"],["omcr","application/x-omcregerator"],["onetoc","application/onenote"],["opf","application/oebps-package+xml"],["org","application/vnd.lotus-organizer"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","application/x-font-otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p10",["application/pkcs10","application/x-pkcs10"]],["p12",["application/pkcs-12","application/x-pkcs12"]],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7m",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7r","application/x-pkcs7-certreqresp"],["p7s",["application/pkcs7-signature","application/x-pkcs7-signature"]],["p8","application/pkcs8"],["par","text/plain-bas"],["part","application/pro_eng"],["pas","text/pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcf","application/x-font-pcf"],["pcl",["application/vnd.hp-pcl","application/x-pcl"]],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb",["application/vnd.palm","chemical/x-pdb"]],["pdf","application/pdf"],["pfa","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfunk",["audio/make","audio/make.my.funk"]],["pfx","application/x-pkcs12"],["pgm",["image/x-portable-graymap","image/x-portable-greymap"]],["pgn","application/x-chess-pgn"],["pgp","application/pgp-signature"],["pic",["image/pict","image/x-pict"]],["pict","image/pict"],["pkg","application/x-newton-compatible-pkg"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pko",["application/ynd.ms-pkipko","application/vnd.ms-pki.pko"]],["pl",["text/plain","text/x-script.perl"]],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["plx","application/x-pixclscript"],["pm",["text/x-script.perl-module","image/x-xpixmap"]],["pm4","application/x-pagemaker"],["pm5","application/x-pagemaker"],["pma","application/x-perfmon"],["pmc","application/x-perfmon"],["pml",["application/vnd.ctc-posml","application/x-perfmon"]],["pmr","application/x-perfmon"],["pmw","application/x-perfmon"],["png","image/png"],["pnm",["application/x-portable-anymap","image/x-portable-anymap"]],["portpkg","application/vnd.macports.portpkg"],["pot",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["potm","application/vnd.ms-powerpoint.template.macroenabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["pov","model/x-pov"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroenabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["ppsm","application/vnd.ms-powerpoint.slideshow.macroenabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt",["application/vnd.ms-powerpoint","application/mspowerpoint","application/powerpoint","application/x-mspowerpoint"]],["pptm","application/vnd.ms-powerpoint.presentation.macroenabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["ppz","application/mspowerpoint"],["prc","application/x-mobipocket-ebook"],["pre",["application/vnd.lotus-freelance","application/x-freelance"]],["prf","application/pics-rules"],["prt","application/pro_eng"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd",["application/octet-stream","image/vnd.adobe.photoshop"]],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pvu","paleovu/x-pv"],["pwn","application/vnd.3m.post-it-notes"],["pwz","application/vnd.ms-powerpoint"],["py","text/x-script.phyton"],["pya","audio/vnd.ms-playready.media.pya"],["pyc","application/x-bytecode.python"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qcp","audio/vnd.qcelp"],["qd3","x-world/x-3dmf"],["qd3d","x-world/x-3dmf"],["qfx","application/vnd.intu.qfx"],["qif","image/x-quicktime"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qtc","video/x-qtc"],["qti","image/x-quicktime"],["qtif","image/x-quicktime"],["qxd","application/vnd.quark.quarkxpress"],["ra",["audio/x-realaudio","audio/x-pn-realaudio","audio/x-pn-realaudio-plugin"]],["ram","audio/x-pn-realaudio"],["rar","application/x-rar-compressed"],["ras",["image/cmu-raster","application/x-cmu-raster","image/x-cmu-raster"]],["rast","image/cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rexx","text/x-script.rexx"],["rf","image/vnd.rn-realflash"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm",["application/vnd.rn-realmedia","audio/x-pn-realaudio"]],["rmi","audio/mid"],["rmm","audio/x-pn-realaudio"],["rmp",["audio/x-pn-realaudio-plugin","audio/x-pn-realaudio"]],["rms","application/vnd.jcp.javame.midlet-rms"],["rnc","application/relax-ng-compact-syntax"],["rng",["application/ringing-tones","application/vnd.nokia.ringing-tone"]],["rnx","application/vnd.rn-realplayer"],["roff","application/x-troff"],["rp","image/vnd.rn-realpix"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsd","application/rsd+xml"],["rt",["text/richtext","text/vnd.rn-realtext"]],["rtf",["application/rtf","text/richtext","application/x-rtf"]],["rtx",["text/richtext","application/rtf"]],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["saveme","application/octet-stream"],["sbk","application/x-tbook"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm",["application/vnd.lotus-screencam","video/x-scm","text/x-script.guile","application/x-lotusscreencam","text/x-script.scheme"]],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["sct","text/scriptlet"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkm","application/vnd.solent.sdkm+xml"],["sdml","text/plain"],["sdp",["application/sdp","application/x-sdp"]],["sdr","application/sounder"],["sdw","application/vnd.stardivision.writer"],["sea",["application/sea","application/x-sea"]],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["ser","application/java-serialized-object"],["set","application/set"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sgl","application/vnd.stardivision.writer-global"],["sgm",["text/sgml","text/x-sgml"]],["sgml",["text/sgml","text/x-sgml"]],["sh",["application/x-shar","application/x-bsh","application/x-sh","text/x-script.sh"]],["shar",["application/x-bsh","application/x-shar"]],["shf","application/shf+xml"],["shtml",["text/html","text/x-server-parsed-html"]],["sid","audio/x-psid"],["sis","application/vnd.symbian.install"],["sit",["application/x-stuffit","application/x-sit"]],["sitx","application/x-stuffitx"],["skd","application/x-koan"],["skm","application/x-koan"],["skp",["application/vnd.koan","application/x-koan"]],["skt","application/x-koan"],["sl","application/x-seelogo"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi",["application/smil","application/smil+xml"]],["smil","application/smil"],["snd",["audio/basic","audio/x-adpcm"]],["snf","application/x-font-snf"],["sol","application/solids"],["spc",["text/x-speech","application/x-pkcs7-certificates"]],["spf","application/vnd.yamaha.smaf-phrase"],["spl",["application/futuresplash","application/x-futuresplash"]],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spr","application/x-sprite"],["sprite","application/x-sprite"],["src","application/x-wais-source"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssi","text/x-server-parsed-html"],["ssm","application/streamingmedia"],["ssml","application/ssml+xml"],["sst",["application/vnd.ms-pkicertstore","application/vnd.ms-pki.certstore"]],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["step","application/step"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl",["application/vnd.ms-pkistl","application/sla","application/vnd.ms-pki.stl","application/x-navistyle"]],["stm","text/html"],["stp","application/step"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["sub","image/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svf",["image/vnd.dwg","image/x-dwg"]],["svg","image/svg+xml"],["svr",["x-world/x-svr","application/x-world"]],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t",["text/troff","application/x-troff"]],["talk","text/x-speech"],["tao","application/vnd.tao.intent-module-archive"],["tar","application/x-tar"],["tbk",["application/toolbook","application/x-tbook"]],["tcap","application/vnd.3gpp2.tcap"],["tcl",["text/x-script.tcl","application/x-tcl"]],["tcsh","text/x-script.tcsh"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text",["application/plain","text/plain"]],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tgz",["application/gnutar","application/x-compressed"]],["thmx","application/vnd.ms-officetheme"],["tif",["image/tiff","image/x-tiff"]],["tiff",["image/tiff","image/x-tiff"]],["tmo","application/vnd.tmobile-livetv"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","application/x-troff"],["tra","application/vnd.trueapp"],["trm","application/x-msterminal"],["tsd","application/timestamped-data"],["tsi","audio/tsp-audio"],["tsp",["application/dsptype","audio/tsplayer"]],["tsv","text/tab-separated-values"],["ttf","application/x-font-ttf"],["ttl","text/turtle"],["turbot","image/florian"],["twd","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["ufd","application/vnd.ufdl"],["uil","text/x-uil"],["uls","text/iuls"],["umj","application/vnd.umajin"],["uni","text/uri-list"],["unis","text/uri-list"],["unityweb","application/vnd.unity"],["unv","application/i-deas"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["ustar",["application/x-ustar","multipart/x-ustar"]],["utz","application/vnd.uiq.theme"],["uu",["application/octet-stream","text/x-uuencode"]],["uue","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vda","application/vda"],["vdo","video/vdo"],["vew","application/groupwise"],["vis","application/vnd.visionary"],["viv",["video/vivo","video/vnd.vivo"]],["vivo",["video/vivo","video/vnd.vivo"]],["vmd","application/vocaltec-media-desc"],["vmf","application/vocaltec-media-file"],["voc",["audio/voc","audio/x-voc"]],["vos","video/vosaic"],["vox","audio/voxware"],["vqe","audio/x-twinvq-plugin"],["vqf","audio/x-twinvq"],["vql","audio/x-twinvq-plugin"],["vrml",["model/vrml","x-world/x-vrml","application/x-vrml"]],["vrt","x-world/x-vrt"],["vsd",["application/vnd.visio","application/x-visio"]],["vsf","application/vnd.vsf"],["vst","application/x-visio"],["vsw","application/x-visio"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w60","application/wordperfect6.0"],["w61","application/wordperfect6.1"],["w6w","application/msword"],["wad","application/x-doom"],["wav",["audio/wav","audio/x-wav"]],["wax","audio/x-ms-wax"],["wb1","application/x-qpro"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/vnd.wap.wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["web","application/vnd.xara"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wiz","application/msword"],["wk1","application/x-123"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf",["windows/metafile","application/x-msmetafile"]],["wml","text/vnd.wap.wml"],["wmlc","application/vnd.wap.wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-ms-wmz"],["woff","application/x-font-woff"],["word","application/msword"],["wp","application/wordperfect"],["wp5",["application/wordperfect","application/wordperfect6.0"]],["wp6","application/wordperfect"],["wpd",["application/wordperfect","application/vnd.wordperfect","application/x-wpwin"]],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wq1","application/x-lotus"],["wqd","application/vnd.wqd"],["wri",["application/mswrite","application/x-wri","application/x-mswrite"]],["wrl",["model/vrml","x-world/x-vrml","application/x-world"]],["wrz",["model/vrml","x-world/x-vrml"]],["wsc","text/scriplet"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wsrc","application/x-wais-source"],["wtb","application/vnd.webturbo"],["wtk","application/x-wintalk"],["wvx","video/x-ms-wvx"],["x-png","image/png"],["x3d","application/vnd.hzn-3d-crossword"],["xaf","x-world/x-vrml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm",["image/xbm","image/x-xbm","image/x-xbitmap"]],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdr","video/x-amt-demorun"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xgz","xgl/drawing"],["xhtml","application/xhtml+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlam","application/vnd.ms-excel.addin.macroenabled.12"],["xlb",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlc",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xld",["application/excel","application/x-excel"]],["xlk",["application/excel","application/x-excel"]],["xll",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlm",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xls",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlsb","application/vnd.ms-excel.sheet.binary.macroenabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroenabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xltm","application/vnd.ms-excel.template.macroenabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlv",["application/excel","application/x-excel"]],["xlw",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xm","audio/xm"],["xml",["application/xml","text/xml","application/atom+xml","application/rss+xml"]],["xmz","xgl/movie"],["xo","application/vnd.olpc-sugar"],["xof","x-world/x-vrml"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpix","application/x-vnd.ls-xpix"],["xpm",["image/xpm","image/x-xpixmap"]],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xsr","video/x-amt-showrun"],["xul","application/vnd.mozilla.xul+xml"],["xwd",["image/x-xwd","image/x-xwindowdump"]],["xyz",["chemical/x-xyz","chemical/x-pdb"]],["yang","application/yang"],["yin","application/yin+xml"],["z",["application/x-compressed","application/x-compress"]],["zaz","application/vnd.zzazz.deck+xml"],["zip",["application/zip","multipart/x-zip","application/x-zip-compressed","application/x-compressed"]],["zir","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zoo","application/octet-stream"],["zsh","text/x-script.zsh"]]);a.exports={detectMimeType(a){if(!a)return e;let b=d.parse(a),c=(b.ext.substr(1)||b.name||"").split("?").shift().trim().toLowerCase(),f=e;return(g.has(c)&&(f=g.get(c)),Array.isArray(f))?f[0]:f},detectExtension(a){if(!a)return"bin";let b=(a||"").toLowerCase().trim().split("/"),c=b.shift().trim(),d=b.join("/").trim();if(f.has(c+"/"+d)){let a=f.get(c+"/"+d);return Array.isArray(a)?a[0]:a}return"text"===c?"txt":"bin"}}},53752:(a,b,c)=>{"use strict";let d=c(79551);class e{constructor(a){this.options=a||{},this.cookies=[]}set(a,b){let c,e=d.parse(b||""),f=this.parse(a);return f.domain?(c=f.domain.replace(/^\./,""),(e.hostname.length<c.length||("."+e.hostname).substr(-c.length+1)!=="."+c)&&(f.domain=e.hostname)):f.domain=e.hostname,f.path||(f.path=this.getPath(e.pathname)),f.expires||(f.expires=new Date(Date.now()+1e3*(Number(this.options.sessionTimeout||1800)||1800))),this.add(f)}get(a){return this.list(a).map(a=>a.name+"="+a.value).join("; ")}list(a){let b,c,d=[];for(b=this.cookies.length-1;b>=0;b--){if(c=this.cookies[b],this.isExpired(c)){this.cookies.splice(b,b);continue}this.match(c,a)&&d.unshift(c)}return d}parse(a){let b={};return(a||"").toString().split(";").forEach(a=>{let c,d=a.split("="),e=d.shift().trim().toLowerCase(),f=d.join("=").trim();if(e)switch(e){case"expires":"Invalid Date"!==(f=new Date(f)).toString()&&(b.expires=f);break;case"path":b.path=f;break;case"domain":(c=f.toLowerCase()).length&&"."!==c.charAt(0)&&(c="."+c),b.domain=c;break;case"max-age":b.expires=new Date(Date.now()+1e3*(Number(f)||0));break;case"secure":b.secure=!0;break;case"httponly":b.httponly=!0;break;default:b.name||(b.name=e,b.value=f)}}),b}match(a,b){let c=d.parse(b||"");return(c.hostname===a.domain||"."===a.domain.charAt(0)&&("."+c.hostname).substr(-a.domain.length)===a.domain)&&this.getPath(c.pathname).substr(0,a.path.length)===a.path&&(!a.secure||"https:"===c.protocol)}add(a){let b,c;if(!a||!a.name)return!1;for(b=0,c=this.cookies.length;b<c;b++)if(this.compare(this.cookies[b],a)){if(this.isExpired(a))return this.cookies.splice(b,1),!1;return this.cookies[b]=a,!0}return this.isExpired(a)||this.cookies.push(a),!0}compare(a,b){return a.name===b.name&&a.path===b.path&&a.domain===b.domain&&a.secure===b.secure&&a.httponly==a.httponly}isExpired(a){return a.expires&&a.expires<new Date||!a.value}getPath(a){let b=(a||"/").split("/");return b.pop(),"/"!==(b=b.join("/").trim()).charAt(0)&&(b="/"+b),"/"!==b.substr(-1)&&(b+="/"),b}}a.exports=e},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58417:a=>{"use strict";a.exports=JSON.parse('{"126":{"description":"126 Mail (NetEase)","host":"smtp.126.com","port":465,"secure":true},"163":{"description":"163 Mail (NetEase)","host":"smtp.163.com","port":465,"secure":true},"1und1":{"description":"1&1 Mail (German hosting provider)","host":"smtp.1und1.de","port":465,"secure":true,"authMethod":"LOGIN"},"Aliyun":{"description":"Alibaba Cloud Mail","domains":["aliyun.com"],"host":"smtp.aliyun.com","port":465,"secure":true},"AliyunQiye":{"description":"Alibaba Cloud Enterprise Mail","host":"smtp.qiye.aliyun.com","port":465,"secure":true},"AOL":{"description":"AOL Mail","domains":["aol.com"],"host":"smtp.aol.com","port":587},"Bluewin":{"description":"Bluewin (Swiss email provider)","host":"smtpauths.bluewin.ch","domains":["bluewin.ch"],"port":465},"DebugMail":{"description":"DebugMail (email testing service)","host":"debugmail.io","port":25},"DynectEmail":{"description":"Dyn Email Delivery","aliases":["Dynect"],"host":"smtp.dynect.net","port":25},"ElasticEmail":{"description":"Elastic Email","aliases":["Elastic Email"],"host":"smtp.elasticemail.com","port":465,"secure":true},"Ethereal":{"description":"Ethereal Email (email testing service)","aliases":["ethereal.email"],"host":"smtp.ethereal.email","port":587},"FastMail":{"description":"FastMail","domains":["fastmail.fm"],"host":"smtp.fastmail.com","port":465,"secure":true},"Feishu Mail":{"description":"Feishu Mail (Lark)","aliases":["Feishu","FeishuMail"],"domains":["www.feishu.cn"],"host":"smtp.feishu.cn","port":465,"secure":true},"Forward Email":{"description":"Forward Email (email forwarding service)","aliases":["FE","ForwardEmail"],"domains":["forwardemail.net"],"host":"smtp.forwardemail.net","port":465,"secure":true},"GandiMail":{"description":"Gandi Mail","aliases":["Gandi","Gandi Mail"],"host":"mail.gandi.net","port":587},"Gmail":{"description":"Gmail","aliases":["Google Mail"],"domains":["gmail.com","googlemail.com"],"host":"smtp.gmail.com","port":465,"secure":true},"GMX":{"description":"GMX Mail","domains":["gmx.com","gmx.net","gmx.de"],"host":"mail.gmx.com","port":587},"Godaddy":{"description":"GoDaddy Email (US)","host":"smtpout.secureserver.net","port":25},"GodaddyAsia":{"description":"GoDaddy Email (Asia)","host":"smtp.asia.secureserver.net","port":25},"GodaddyEurope":{"description":"GoDaddy Email (Europe)","host":"smtp.europe.secureserver.net","port":25},"hot.ee":{"description":"Hot.ee (Estonian email provider)","host":"mail.hot.ee"},"Hotmail":{"description":"Outlook.com / Hotmail","aliases":["Outlook","Outlook.com","Hotmail.com"],"domains":["hotmail.com","outlook.com"],"host":"smtp-mail.outlook.com","port":587},"iCloud":{"description":"iCloud Mail","aliases":["Me","Mac"],"domains":["me.com","mac.com"],"host":"smtp.mail.me.com","port":587},"Infomaniak":{"description":"Infomaniak Mail (Swiss hosting provider)","host":"mail.infomaniak.com","domains":["ik.me","ikmail.com","etik.com"],"port":587},"Loopia":{"description":"Loopia (Swedish hosting provider)","host":"mailcluster.loopia.se","port":465},"Loops":{"description":"Loops","host":"smtp.loops.so","port":587},"mail.ee":{"description":"Mail.ee (Estonian email provider)","host":"smtp.mail.ee"},"Mail.ru":{"description":"Mail.ru","host":"smtp.mail.ru","port":465,"secure":true},"Mailcatch.app":{"description":"Mailcatch (email testing service)","host":"sandbox-smtp.mailcatch.app","port":2525},"Maildev":{"description":"MailDev (local email testing)","port":1025,"ignoreTLS":true},"MailerSend":{"description":"MailerSend","host":"smtp.mailersend.net","port":587},"Mailgun":{"description":"Mailgun","host":"smtp.mailgun.org","port":465,"secure":true},"Mailjet":{"description":"Mailjet","host":"in.mailjet.com","port":587},"Mailosaur":{"description":"Mailosaur (email testing service)","host":"mailosaur.io","port":25},"Mailtrap":{"description":"Mailtrap","host":"live.smtp.mailtrap.io","port":587},"Mandrill":{"description":"Mandrill (by Mailchimp)","host":"smtp.mandrillapp.com","port":587},"Naver":{"description":"Naver Mail (Korean email provider)","host":"smtp.naver.com","port":587},"OhMySMTP":{"description":"OhMySMTP (email delivery service)","host":"smtp.ohmysmtp.com","port":587,"secure":false},"One":{"description":"One.com Email","host":"send.one.com","port":465,"secure":true},"OpenMailBox":{"description":"OpenMailBox","aliases":["OMB","openmailbox.org"],"host":"smtp.openmailbox.org","port":465,"secure":true},"Outlook365":{"description":"Microsoft 365 / Office 365","host":"smtp.office365.com","port":587,"secure":false},"Postmark":{"description":"Postmark","aliases":["PostmarkApp"],"host":"smtp.postmarkapp.com","port":2525},"Proton":{"description":"Proton Mail","aliases":["ProtonMail","Proton.me","Protonmail.com","Protonmail.ch"],"domains":["proton.me","protonmail.com","pm.me","protonmail.ch"],"host":"smtp.protonmail.ch","port":587,"requireTLS":true},"qiye.aliyun":{"description":"Alibaba Mail Enterprise Edition","host":"smtp.mxhichina.com","port":"465","secure":true},"QQ":{"description":"QQ Mail","domains":["qq.com"],"host":"smtp.qq.com","port":465,"secure":true},"QQex":{"description":"QQ Enterprise Mail","aliases":["QQ Enterprise"],"domains":["exmail.qq.com"],"host":"smtp.exmail.qq.com","port":465,"secure":true},"Resend":{"description":"Resend","host":"smtp.resend.com","port":465,"secure":true},"SendCloud":{"description":"SendCloud (Chinese email delivery)","host":"smtp.sendcloud.net","port":2525},"SendGrid":{"description":"SendGrid","host":"smtp.sendgrid.net","port":587},"SendinBlue":{"description":"Brevo (formerly Sendinblue)","aliases":["Brevo"],"host":"smtp-relay.brevo.com","port":587},"SendPulse":{"description":"SendPulse","host":"smtp-pulse.com","port":465,"secure":true},"SES":{"description":"AWS SES US East (N. Virginia)","host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-1":{"description":"AWS SES Asia Pacific (Tokyo)","host":"email-smtp.ap-northeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-2":{"description":"AWS SES Asia Pacific (Seoul)","host":"email-smtp.ap-northeast-2.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-3":{"description":"AWS SES Asia Pacific (Osaka)","host":"email-smtp.ap-northeast-3.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTH-1":{"description":"AWS SES Asia Pacific (Mumbai)","host":"email-smtp.ap-south-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-1":{"description":"AWS SES Asia Pacific (Singapore)","host":"email-smtp.ap-southeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-2":{"description":"AWS SES Asia Pacific (Sydney)","host":"email-smtp.ap-southeast-2.amazonaws.com","port":465,"secure":true},"SES-CA-CENTRAL-1":{"description":"AWS SES Canada (Central)","host":"email-smtp.ca-central-1.amazonaws.com","port":465,"secure":true},"SES-EU-CENTRAL-1":{"description":"AWS SES Europe (Frankfurt)","host":"email-smtp.eu-central-1.amazonaws.com","port":465,"secure":true},"SES-EU-NORTH-1":{"description":"AWS SES Europe (Stockholm)","host":"email-smtp.eu-north-1.amazonaws.com","port":465,"secure":true},"SES-EU-WEST-1":{"description":"AWS SES Europe (Ireland)","host":"email-smtp.eu-west-1.amazonaws.com","port":465,"secure":true},"SES-EU-WEST-2":{"description":"AWS SES Europe (London)","host":"email-smtp.eu-west-2.amazonaws.com","port":465,"secure":true},"SES-EU-WEST-3":{"description":"AWS SES Europe (Paris)","host":"email-smtp.eu-west-3.amazonaws.com","port":465,"secure":true},"SES-SA-EAST-1":{"description":"AWS SES South America (S\xe3o Paulo)","host":"email-smtp.sa-east-1.amazonaws.com","port":465,"secure":true},"SES-US-EAST-1":{"description":"AWS SES US East (N. Virginia)","host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-US-EAST-2":{"description":"AWS SES US East (Ohio)","host":"email-smtp.us-east-2.amazonaws.com","port":465,"secure":true},"SES-US-GOV-EAST-1":{"description":"AWS SES GovCloud (US-East)","host":"email-smtp.us-gov-east-1.amazonaws.com","port":465,"secure":true},"SES-US-GOV-WEST-1":{"description":"AWS SES GovCloud (US-West)","host":"email-smtp.us-gov-west-1.amazonaws.com","port":465,"secure":true},"SES-US-WEST-1":{"description":"AWS SES US West (N. California)","host":"email-smtp.us-west-1.amazonaws.com","port":465,"secure":true},"SES-US-WEST-2":{"description":"AWS SES US West (Oregon)","host":"email-smtp.us-west-2.amazonaws.com","port":465,"secure":true},"Seznam":{"description":"Seznam Email (Czech email provider)","aliases":["Seznam Email"],"domains":["seznam.cz","email.cz","post.cz","spoluzaci.cz"],"host":"smtp.seznam.cz","port":465,"secure":true},"SMTP2GO":{"description":"SMTP2GO","host":"mail.smtp2go.com","port":2525},"Sparkpost":{"description":"SparkPost","aliases":["SparkPost","SparkPost Mail"],"domains":["sparkpost.com"],"host":"smtp.sparkpostmail.com","port":587,"secure":false},"Tipimail":{"description":"Tipimail (email delivery service)","host":"smtp.tipimail.com","port":587},"Tutanota":{"description":"Tutanota (Tuta Mail)","domains":["tutanota.com","tuta.com","tutanota.de","tuta.io"],"host":"smtp.tutanota.com","port":465,"secure":true},"Yahoo":{"description":"Yahoo Mail","domains":["yahoo.com"],"host":"smtp.mail.yahoo.com","port":465,"secure":true},"Yandex":{"description":"Yandex Mail","domains":["yandex.ru"],"host":"smtp.yandex.ru","port":465,"secure":true},"Zoho":{"description":"Zoho Mail","host":"smtp.zoho.com","port":465,"secure":true,"authMethod":"LOGIN"}}')},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63356:(a,b,c)=>{"use strict";let d=c(27910).Transform;function e(a){let b;"string"==typeof a&&(a=Buffer.from(a,"utf-8"));let c=[[9],[10],[13],[32,60],[62,126]],d="";for(let e=0,f=a.length;e<f;e++){if(function(a,b){for(let c=b.length-1;c>=0;c--)if(b[c].length&&(1===b[c].length&&a===b[c][0]||2===b[c].length&&a>=b[c][0]&&a<=b[c][1]))return!0;return!1}(b=a[e],c)&&(32!==b&&9!==b||e!==f-1&&10!==a[e+1]&&13!==a[e+1])){d+=String.fromCharCode(b);continue}d+="="+(b<16?"0":"")+b.toString(16).toUpperCase()}return d}function f(a,b){let c,d,e;if(a=(a||"").toString(),b=b||76,a.length<=b)return a;let f=0,g=a.length,h=Math.floor(b/3),i="";for(;f<g;){if(c=(e=a.substr(f,b)).match(/\r\n/)){i+=e=e.substr(0,c.index+c[0].length),f+=e.length;continue}if("\n"===e.substr(-1)){i+=e,f+=e.length;continue}if(c=e.substr(-h).match(/\n.*?$/)){i+=e=e.substr(0,e.length-(c[0].length-1)),f+=e.length;continue}if(e.length>b-h&&(c=e.substr(-h).match(/[ \t.,!?][^ \t.,!?]*$/)))e=e.substr(0,e.length-(c[0].length-1));else if(e.match(/[=][\da-f]{0,2}$/i))for((c=e.match(/[=][\da-f]{0,1}$/i))&&(e=e.substr(0,e.length-c[0].length));e.length>3&&e.length<g-f&&!e.match(/^(?:=[\da-f]{2}){1,4}$/i)&&(c=e.match(/[=][\da-f]{2}$/gi))&&!((d=parseInt(c[0].substr(1,2),16))<128)&&(e=e.substr(0,e.length-3),!(d>=192)););f+e.length<g&&"\n"!==e.substr(-1)?(e.length===b&&e.match(/[=][\da-f]{2}$/i)?e=e.substr(0,e.length-3):e.length===b&&(e=e.substr(0,e.length-1)),f+=e.length,e+="=\r\n"):f+=e.length,i+=e}return i}class g extends d{constructor(a){super(),this.options=a||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this.inputBytes=0,this.outputBytes=0}_transform(a,b,c){let d;if("buffer"!==b&&(a=Buffer.from(a,b)),!a||!a.length)return c();this.inputBytes+=a.length,this.options.lineLength?(d=(d=f(d=this._curLine+e(a),this.options.lineLength)).replace(/(^|\n)([^\n]*)$/,(a,b,c)=>(this._curLine=c,b)))&&(this.outputBytes+=d.length,this.push(d)):(d=e(a),this.outputBytes+=d.length,this.push(d,"ascii")),c()}_flush(a){this._curLine&&(this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii")),a()}}a.exports={encode:e,wrap:f,Encoder:g}},65473:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>C,patchFetch:()=>B,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>z});var d={};c.r(d),c.d(d,{POST:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(49526);async function w(a){try{let{name:b,email:c,phone:d,subject:e,message:f}=await a.json(),g=v.createTransport({service:"gmail",auth:{user:process.env.EMAIL_USER,pass:process.env.EMAIL_PASS}}),h={from:process.env.EMAIL_USER,to:process.env.EMAIL_RECIPIENT,replyTo:c,subject:`New message from utsav footwear for: ${e}`,html:`
        <h3>Hello Utsav Footwear, you have a new message from: ${b}</h3>
        <p><strong>Person's email:</strong> ${c}</p>
        <p><strong>Person's phone:</strong> ${d||"Not provided"}</p>
        <p><strong>Why person is contacting:</strong> ${e}</p>
        <h3>Person Message:</h3>
        <blockquote style="
        margin: 10px 0; 
        padding: 10px 15px; 
        border-left: 4px solid #ccc; 
        background-color: #f9f9f9;
        font-style: bold;
        ">
        ${f.replace(/\n/g,"<br>")}
        </blockquote>
    `};return await g.sendMail(h),u.NextResponse.json({message:"Email sent successfully"},{status:200})}catch(a){return console.error("Error sending email:",a),u.NextResponse.json({message:"Failed to send email"},{status:500})}}let x=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\api\\contact\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:y,workUnitAsyncStorage:z,serverHooks:A}=x;function B(){return(0,g.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:z})}async function C(a,b,c){var d;let e="/api/contact/route";"/index"===e&&(e="/");let g=await x.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||x.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===x.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>x.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>x.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await x.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await x.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await x.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},74075:a=>{"use strict";a.exports=require("zlib")},74301:(a,b,c)=>{"use strict";let d=c(48178),e=c(18199),f=c(55511);function g(a,b,c){let d=new Set,e=new Set,f=new Map;(c||"").toLowerCase().split(":").forEach(a=>{e.add(a.trim())}),(b||"").toLowerCase().split(":").filter(a=>!e.has(a.trim())).forEach(a=>{d.add(a.trim())});for(let b=a.length-1;b>=0;b--){let c=a[b];d.has(c.key)&&!f.has(c.key)&&f.set(c.key,h(c.line))}let g=[],i=[];return d.forEach(a=>{f.has(a)&&(i.push(a),g.push(a+":"+f.get(a)))}),{headers:g.join("\r\n")+"\r\n",fieldNames:i.join(":")}}function h(a){return a.substr(a.indexOf(":")+1).replace(/\r?\n/g,"").replace(/\s+/g," ").trim()}a.exports=(a,b,c,i)=>{let j,k,l=g(a,(i=i||{}).headerFieldNames||"From:Sender:Reply-To:Subject:Date:Message-ID:To:Cc:MIME-Version:Content-Type:Content-Transfer-Encoding:Content-ID:Content-Description:Resent-Date:Resent-From:Resent-Sender:Resent-To:Resent-Cc:Resent-Message-ID:In-Reply-To:References:List-Id:List-Help:List-Unsubscribe:List-Subscribe:List-Post:List-Owner:List-Archive",i.skipFields),m=function(a,b,c,f,g){let h=["v=1","a=rsa-"+f,"c=relaxed/relaxed","d="+d.toASCII(a),"q=dns/txt","s="+b,"bh="+g,"h="+c].join("; ");return e.foldLines("DKIM-Signature: "+h,76)+";\r\n b="}(i.domainName,i.keySelector,l.fieldNames,b,c);l.headers+="dkim-signature:"+h(m),(j=f.createSign(("rsa-"+b).toUpperCase())).update(l.headers);try{k=j.sign(i.privateKey,"base64")}catch(a){return!1}return m+k.replace(/(^.{73}|.{75}(?!\r?\n|\r))/g,"$&\r\n ").trim()},a.exports.relaxedHeaders=g},74508:a=>{"use strict";class b{constructor(a){this.str=(a||"").toString(),this.operatorCurrent="",this.operatorExpecting="",this.node=null,this.escaped=!1,this.list=[],this.operators={'"':'"',"(":")","<":">",",":"",":":";",";":""}}tokenize(){let a=[];for(let a=0,b=this.str.length;a<b;a++){let c=this.str.charAt(a),d=a<b-1?this.str.charAt(a+1):null;this.checkChar(c,d)}return this.list.forEach(b=>{b.value=(b.value||"").toString().trim(),b.value&&a.push(b)}),a}checkChar(a,b){if(this.escaped);else if(a===this.operatorExpecting){this.node={type:"operator",value:a},b&&![" ","	","\r","\n",",",";"].includes(b)&&(this.node.noBreak=!0),this.list.push(this.node),this.node=null,this.operatorExpecting="",this.escaped=!1;return}else if(!this.operatorExpecting&&a in this.operators){this.node={type:"operator",value:a},this.list.push(this.node),this.node=null,this.operatorExpecting=this.operators[a],this.escaped=!1;return}else if(['"',"'"].includes(this.operatorExpecting)&&"\\"===a){this.escaped=!0;return}this.node||(this.node={type:"text",value:""},this.list.push(this.node)),"\n"===a&&(a=" "),(a.charCodeAt(0)>=33||[" ","	"].includes(a))&&(this.node.value+=a),this.escaped=!1}}a.exports=function a(c,d){d=d||{};let e=new b(c).tokenize(),f=[],g=[],h=[];if(e.forEach(a=>{"operator"===a.type&&(","===a.value||";"===a.value)?(g.length&&f.push(g),g=[]):g.push(a)}),g.length&&f.push(g),f.forEach(b=>{(b=function(b){let c,d,e,f=!1,g="text",h=[],i={address:[],comment:[],group:[],text:[]};for(d=0,e=b.length;d<e;d++){let a=b[d],c=d?b[d-1]:null;if("operator"===a.type)switch(a.value){case"<":g="address";break;case"(":g="comment";break;case":":g="group",f=!0;break;default:g="text"}else a.value&&("address"===g&&(a.value=a.value.replace(/^[^<]*<\s*/,"")),c&&c.noBreak&&i[g].length?i[g][i[g].length-1]+=a.value:i[g].push(a.value))}if(!i.text.length&&i.comment.length&&(i.text=i.comment,i.comment=[]),f)i.text=i.text.join(" "),h.push({name:i.text||c&&c.name,group:i.group.length?a(i.group.join(",")):[]});else{if(!i.address.length&&i.text.length){for(d=i.text.length-1;d>=0;d--)if(i.text[d].match(/^[^@\s]+@[^@\s]+$/)){i.address=i.text.splice(d,1);break}let a=function(a){return i.address.length?a:(i.address=[a.trim()]," ")};if(!i.address.length)for(d=i.text.length-1;d>=0&&(i.text[d]=i.text[d].replace(/\s*\b[^@\s]+@[^\s]+\b\s*/,a).trim(),!i.address.length);d--);}if(!i.text.length&&i.comment.length&&(i.text=i.comment,i.comment=[]),i.address.length>1&&(i.text=i.text.concat(i.address.splice(1))),i.text=i.text.join(" "),i.address=i.address.join(" "),!i.address&&f)return[];(c={address:i.address||i.text||"",name:i.text||i.address||""}).address===c.name&&((c.address||"").match(/@/)?c.name="":c.address=""),h.push(c)}return h}(b)).length&&(h=h.concat(b))}),d.flatten){let a=[],b=c=>{c.forEach(c=>{if(c.group)return b(c.group);a.push(c)})};return b(h),a}return h}},77821:(a,b,c)=>{"use strict";let d=c(49074),e=c(91292);class f{constructor(a){a=a||{},this.options=a||{},this.name="StreamTransport",this.version=d.version,this.logger=e.getLogger(this.options,{component:this.options.component||"stream-transport"}),this.winbreak=["win","windows","dos","\r\n"].includes((a.newline||"").toString().toLowerCase())}send(a,b){a.message.keepBcc=!0;let c=a.data.envelope||a.message.getEnvelope(),d=a.message.messageId(),e=[].concat(c.to||[]);e.length>3&&e.push("...and "+e.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:d},"Sending message %s to <%s> using %s line breaks",d,e.join(", "),this.winbreak?"<CR><LF>":"<LF>"),setImmediate(()=>{let c;try{c=a.message.createReadStream()}catch(a){return this.logger.error({err:a,tnx:"send",messageId:d},"Creating send stream failed for %s. %s",d,a.message),b(a)}if(!this.options.buffer)return c.once("error",a=>{this.logger.error({err:a,tnx:"send",messageId:d},"Failed creating message for %s. %s",d,a.message)}),b(null,{envelope:a.data.envelope||a.message.getEnvelope(),messageId:d,message:c});let e=[],f=0;c.on("readable",()=>{let a;for(;null!==(a=c.read());)e.push(a),f+=a.length}),c.once("error",a=>(this.logger.error({err:a,tnx:"send",messageId:d},"Failed creating message for %s. %s",d,a.message),b(a))),c.on("end",()=>b(null,{envelope:a.data.envelope||a.message.getEnvelope(),messageId:d,message:Buffer.concat(e,f)}))})}}a.exports=f},78335:()=>{},79524:(a,b,c)=>{"use strict";let d=c(79646).spawn,e=c(49074),f=c(91292);class g{constructor(a){a=a||{},this._spawn=d,this.options=a||{},this.name="Sendmail",this.version=e.version,this.path="sendmail",this.args=!1,this.winbreak=!1,this.logger=f.getLogger(this.options,{component:this.options.component||"sendmail"}),a&&("string"==typeof a?this.path=a:"object"==typeof a&&(a.path&&(this.path=a.path),Array.isArray(a.args)&&(this.args=a.args),this.winbreak=["win","windows","dos","\r\n"].includes((a.newline||"").toString().toLowerCase())))}send(a,b){let c,d,e;a.message.keepBcc=!0;let f=a.data.envelope||a.message.getEnvelope(),g=a.message.messageId();if([].concat(f.from||[]).concat(f.to||[]).some(a=>/^-/.test(a)))return b(Error("Can not send mail. Invalid envelope addresses."));c=this.args?["-i"].concat(this.args).concat(f.to):["-i"].concat(f.from?["-f",f.from]:[]).concat(f.to);let h=c=>{if(!e&&(e=!0,"function"==typeof b))if(c)return b(c);else return b(null,{envelope:a.data.envelope||a.message.getEnvelope(),messageId:g,response:"Messages queued for delivery"})};try{d=this._spawn(this.path,c)}catch(a){return this.logger.error({err:a,tnx:"spawn",messageId:g},"Error occurred while spawning sendmail. %s",a.message),h(a)}if(!d)return h(Error("sendmail was not found"));{d.on("error",a=>{this.logger.error({err:a,tnx:"spawn",messageId:g},"Error occurred when sending message %s. %s",g,a.message),h(a)}),d.once("exit",a=>{let b;if(!a)return h();b=127===a?Error("Sendmail command not found, process exited with code "+a):Error("Sendmail exited with code "+a),this.logger.error({err:b,tnx:"stdin",messageId:g},"Error sending message %s to sendmail. %s",g,b.message),h(b)}),d.once("close",h),d.stdin.on("error",a=>{this.logger.error({err:a,tnx:"stdin",messageId:g},"Error occurred when piping message %s to sendmail. %s",g,a.message),h(a)});let b=[].concat(f.to||[]);b.length>3&&b.push("...and "+b.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:g},"Sending message %s to <%s>",g,b.join(", "));let c=a.message.createReadStream();c.once("error",a=>{this.logger.error({err:a,tnx:"stdin",messageId:g},"Error occurred when generating message %s. %s",g,a.message),d.kill("SIGINT"),h(a)}),c.pipe(d.stdin)}}}a.exports=g},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},84681:(a,b,c)=>{"use strict";let d=c(94735),e=c(20068),f=c(16435),g=c(91292),h=c(21940),i=c(49074);class j extends d{constructor(a){let b;super(),"string"==typeof(a=a||{})&&(a={url:a});let c=a.service;"function"==typeof a.getSocket&&(this.getSocket=a.getSocket),a.url&&(b=g.parseConnectionUrl(a.url),c=c||b.service),this.options=g.assign(!1,a,b,c&&f(c)),this.logger=g.getLogger(this.options,{component:this.options.component||"smtp-transport"});let d=new e(this.options);this.name="SMTP",this.version=i.version+"[client:"+d.version+"]",this.options.auth&&(this.auth=this.getAuth({}))}getSocket(a,b){return setImmediate(()=>b(null,!1))}getAuth(a){if(!a)return this.auth;let b=!1,c={};if(this.options.auth&&"object"==typeof this.options.auth&&Object.keys(this.options.auth).forEach(a=>{b=!0,c[a]=this.options.auth[a]}),a&&"object"==typeof a&&Object.keys(a).forEach(d=>{b=!0,c[d]=a[d]}),!b)return!1;if("OAUTH2"!==(c.type||"").toString().toUpperCase())return{type:(c.type||"").toString().toUpperCase()||"LOGIN",user:c.user,credentials:{user:c.user||"",pass:c.pass,options:c.options},method:(c.method||"").trim().toUpperCase()||this.options.authMethod||!1};{if(!c.service&&!c.user)return!1;let a=new h(c,this.logger);return a.provisionCallback=this.mailer&&this.mailer.get("oauth2_provision_cb")||a.provisionCallback,a.on("token",a=>this.mailer.emit("token",a)),a.on("error",a=>this.emit("error",a)),{type:"OAUTH2",user:c.user,oauth2:a,method:"XOAUTH2"}}}send(a,b){this.getSocket(this.options,(c,d)=>{if(c)return b(c);let f=!1,h=this.options;d&&d.connection&&(this.logger.info({tnx:"proxy",remoteAddress:d.connection.remoteAddress,remotePort:d.connection.remotePort,destHost:h.host||"",destPort:h.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",d.connection.remoteAddress,d.connection.remotePort,h.host||"",h.port||""),h=g.assign(!1,h),Object.keys(d).forEach(a=>{h[a]=d[a]}));let i=new e(h);i.once("error",a=>{if(!f)return f=!0,i.close(),b(a)}),i.once("end",()=>{if(f)return;let a=setTimeout(()=>{if(f)return;f=!0;let a=Error("Unexpected socket close");i&&i._socket&&i._socket.upgrading&&(a.code="ETLS"),b(a)},1e3);try{a.unref()}catch(a){}});let j=()=>{let c=a.message.getEnvelope(),d=a.message.messageId(),e=[].concat(c.to||[]);e.length>3&&e.push("...and "+e.splice(2).length+" more"),a.data.dsn&&(c.dsn=a.data.dsn),this.logger.info({tnx:"send",messageId:d},"Sending message %s to <%s>",d,e.join(", ")),i.send(c,a.message.createReadStream(),(a,e)=>{if(f=!0,i.close(),a)return this.logger.error({err:a,tnx:"send"},"Send error for %s: %s",d,a.message),b(a);e.envelope={from:c.from,to:c.to},e.messageId=d;try{return b(null,e)}catch(a){this.logger.error({err:a,tnx:"callback"},"Callback error for %s: %s",d,a.message)}})};i.connect(()=>{if(f)return;let c=this.getAuth(a.data.auth);c&&(i.allowsAuth||h.forceAuth)?i.login(c,a=>{if(c&&c!==this.auth&&c.oauth2&&c.oauth2.removeAllListeners(),!f){if(a)return f=!0,i.close(),b(a);j()}}):j()})})}verify(a){let b;return a||(b=new Promise((b,c)=>{a=g.callbackPromise(b,c)})),this.getSocket(this.options,(b,c)=>{if(b)return a(b);let d=this.options;c&&c.connection&&(this.logger.info({tnx:"proxy",remoteAddress:c.connection.remoteAddress,remotePort:c.connection.remotePort,destHost:d.host||"",destPort:d.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",c.connection.remoteAddress,c.connection.remotePort,d.host||"",d.port||""),d=g.assign(!1,d),Object.keys(c).forEach(a=>{d[a]=c[a]}));let f=new e(d),h=!1;f.once("error",b=>{if(!h)return h=!0,f.close(),a(b)}),f.once("end",()=>{if(!h)return h=!0,a(Error("Connection closed"))});let i=()=>{if(!h)return h=!0,f.quit(),a(null,!0)};f.connect(()=>{if(h)return;let b=this.getAuth({});if(b&&(f.allowsAuth||d.forceAuth))f.login(b,b=>{if(!h){if(b)return h=!0,f.close(),a(b);i()}});else if(!b&&f.allowsAuth&&d.forceAuth){let b=Error("Authentication info was not provided");return b.code="NoAuth",h=!0,f.close(),a(b)}else i()})}),b}close(){this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.emit("close")}}a.exports=j},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89047:(a,b,c)=>{"use strict";let d=c(27910).Transform;class e extends d{constructor(){super(),this.lastByte=!1}_transform(a,b,c){a.length&&(this.lastByte=a[a.length-1]),this.push(a),c()}_flush(a){return 10===this.lastByte||(13===this.lastByte?this.push(Buffer.from("\n")):this.push(Buffer.from("\r\n"))),a()}}a.exports=e},89066:(a,b,c)=>{"use strict";let d=c(27910).Transform;function e(a){return"string"==typeof a&&(a=Buffer.from(a,"utf-8")),a.toString("base64")}function f(a,b){if(a=(a||"").toString(),b=b||76,a.length<=b)return a;let c=[],d=0,e=1024*b;for(;d<a.length;){let f=a.substr(d,e).replace(RegExp(".{"+b+"}","g"),"$&\r\n").trim();c.push(f),d+=e}return c.join("\r\n").trim()}class g extends d{constructor(a){super(),this.options=a||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this._remainingBytes=!1,this.inputBytes=0,this.outputBytes=0}_transform(a,b,c){if("buffer"!==b&&(a=Buffer.from(a,b)),!a||!a.length)return setImmediate(c);this.inputBytes+=a.length,this._remainingBytes&&this._remainingBytes.length&&(a=Buffer.concat([this._remainingBytes,a],this._remainingBytes.length+a.length),this._remainingBytes=!1),a.length%3?(this._remainingBytes=a.slice(a.length-a.length%3),a=a.slice(0,a.length-a.length%3)):this._remainingBytes=!1;let d=this._curLine+e(a);if(this.options.lineLength){let a=(d=f(d,this.options.lineLength)).lastIndexOf("\n");a<0?(this._curLine=d,d=""):a===d.length-1?this._curLine="":(this._curLine=d.substr(a+1),d=d.substr(0,a+1))}d&&(this.outputBytes+=d.length,this.push(Buffer.from(d,"ascii"))),setImmediate(c)}_flush(a){this._remainingBytes&&this._remainingBytes.length&&(this._curLine+=e(this._remainingBytes)),this._curLine&&(this._curLine=f(this._curLine,this.options.lineLength),this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii"),this._curLine=""),a()}}a.exports={encode:e,wrap:f,Encoder:g}},89231:(a,b,c)=>{"use strict";let d=c(20068),e=c(91292).assign,f=c(21940),g=c(94735);class h extends g{constructor(a){if(super(),this.pool=a,this.options=a.options,this.logger=this.pool.logger,this.options.auth)switch((this.options.auth.type||"").toString().toUpperCase()){case"OAUTH2":{let a=new f(this.options.auth,this.logger);a.provisionCallback=this.pool.mailer&&this.pool.mailer.get("oauth2_provision_cb")||a.provisionCallback,this.auth={type:"OAUTH2",user:this.options.auth.user,oauth2:a,method:"XOAUTH2"},a.on("token",a=>this.pool.mailer.emit("token",a)),a.on("error",a=>this.emit("error",a));break}default:if(!this.options.auth.user&&!this.options.auth.pass)break;this.auth={type:(this.options.auth.type||"").toString().toUpperCase()||"LOGIN",user:this.options.auth.user,credentials:{user:this.options.auth.user||"",pass:this.options.auth.pass,options:this.options.auth.options},method:(this.options.auth.method||"").trim().toUpperCase()||this.options.authMethod||!1}}this._connection=!1,this._connected=!1,this.messages=0,this.available=!0}connect(a){this.pool.getSocket(this.options,(b,c)=>{if(b)return a(b);let f=!1,g=this.options;c&&c.connection&&(this.logger.info({tnx:"proxy",remoteAddress:c.connection.remoteAddress,remotePort:c.connection.remotePort,destHost:g.host||"",destPort:g.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",c.connection.remoteAddress,c.connection.remotePort,g.host||"",g.port||""),g=e(!1,g),Object.keys(c).forEach(a=>{g[a]=c[a]})),this.connection=new d(g),this.connection.once("error",b=>{if(this.emit("error",b),!f)return f=!0,a(b)}),this.connection.once("end",()=>{if(this.close(),f)return;f=!0;let b=setTimeout(()=>{if(f)return;let b=Error("Unexpected socket close");this.connection&&this.connection._socket&&this.connection._socket.upgrading&&(b.code="ETLS"),a(b)},1e3);try{b.unref()}catch(a){}}),this.connection.connect(()=>{if(!f)if(!this.auth||!this.connection.allowsAuth&&!g.forceAuth)return f=!0,this._connected=!0,a(null,!0);else this.connection.login(this.auth,b=>{if(!f){if(f=!0,b)return this.connection.close(),this.emit("error",b),a(b);this._connected=!0,a(null,!0)}})})})}send(a,b){if(!this._connected)return this.connect(c=>c?b(c):this.send(a,b));let c=a.message.getEnvelope(),d=a.message.messageId(),e=[].concat(c.to||[]);e.length>3&&e.push("...and "+e.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:d,cid:this.id},"Sending message %s using #%s to <%s>",d,this.id,e.join(", ")),a.data.dsn&&(c.dsn=a.data.dsn),this.connection.send(c,a.message.createReadStream(),(a,e)=>{if(this.messages++,a)return this.connection.close(),this.emit("error",a),b(a);e.envelope={from:c.from,to:c.to},e.messageId=d,setImmediate(()=>{let a;this.messages>=this.options.maxMessages?((a=Error("Resource exhausted")).code="EMAXLIMIT",this.connection.close(),this.emit("error",a)):this.pool._checkRateLimit(()=>{this.available=!0,this.emit("available")})}),b(null,e)})}close(){this._connected=!1,this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.connection&&this.connection.close(),this.emit("close")}}a.exports=h},89767:(a,b,c)=>{"use strict";let d=c(49074),e=c(91292);class f{constructor(a){a=a||{},this.options=a||{},this.name="JSONTransport",this.version=d.version,this.logger=e.getLogger(this.options,{component:this.options.component||"json-transport"})}send(a,b){a.message.keepBcc=!0;let c=a.data.envelope||a.message.getEnvelope(),d=a.message.messageId(),e=[].concat(c.to||[]);e.length>3&&e.push("...and "+e.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:d},"Composing JSON structure of %s to <%s>",d,e.join(", ")),setImmediate(()=>{a.normalize((a,e)=>a?(this.logger.error({err:a,tnx:"send",messageId:d},"Failed building JSON structure for %s. %s",d,a.message),b(a)):(delete e.envelope,delete e.normalizedHeaders,b(null,{envelope:c,messageId:d,message:this.options.skipEncoding?e:JSON.stringify(e)})))})}}a.exports=f},91292:(a,b,c)=>{"use strict";let d,e=c(79551),f=c(28354),g=c(29021),h=c(38099),i=c(14985),j=c(91645),k=c(21820);try{d=k.networkInterfaces()}catch(a){}a.exports.networkInterfaces=d;let l=(b,c)=>{let d=a.exports.networkInterfaces;return!d||Object.keys(d).map(a=>d[a]).reduce((a,b)=>a.concat(b),[]).filter(a=>!a.internal||c).filter(a=>a.family==="IPv"+b||a.family===b).length>0},m=(a,b,c,d)=>{if(!l(a,(c=c||{}).allowInternalNetworkInterfaces))return d(null,[]);(i.Resolver?new i.Resolver(c):i)["resolve"+a](b,(a,b)=>{if(a){switch(a.code){case i.NODATA:case i.NOTFOUND:case i.NOTIMP:case i.SERVFAIL:case i.CONNREFUSED:case i.REFUSED:case"EAI_AGAIN":return d(null,[])}return d(a)}return d(null,Array.isArray(b)?b:[].concat(b||[]))})},n=a.exports.dnsCache=new Map,o=(a,b)=>a?Object.assign({servername:a.servername,host:a.addresses&&a.addresses.length?1===a.addresses.length?a.addresses[0]:a.addresses[Math.floor(Math.random()*a.addresses.length)]:null},b||{}):Object.assign({},b||{});function p(a,b){let c=!1,d=[],e=0;a.on("error",a=>{c||(c=!0,b(a))}),a.on("readable",()=>{let b;for(;null!==(b=a.read());)d.push(b),e+=b.length}),a.on("end",()=>{let a;if(!c){c=!0;try{a=Buffer.concat(d,e)}catch(a){return b(a)}b(null,a)}})}a.exports.resolveHostname=(a,b)=>{let c;return(!(a=a||{}).host&&a.servername&&(a.host=a.servername),!a.host||j.isIP(a.host))?b(null,o({addresses:[a.host],servername:a.servername||!1},{cached:!1})):n.has(a.host)&&(!(c=n.get(a.host)).expires||c.expires>=Date.now())?b(null,o(c.value,{cached:!0})):void m(4,a.host,a,(d,e)=>{if(d)return c?b(null,o(c.value,{cached:!0,error:d})):b(d);if(e&&e.length){let c={addresses:e,servername:a.servername||a.host};return n.set(a.host,{value:c,expires:Date.now()+(a.dnsTtl||3e5)}),b(null,o(c,{cached:!1}))}m(6,a.host,a,(d,e)=>{if(d)return c?b(null,o(c.value,{cached:!0,error:d})):b(d);if(e&&e.length){let c={addresses:e,servername:a.servername||a.host};return n.set(a.host,{value:c,expires:Date.now()+(a.dnsTtl||3e5)}),b(null,o(c,{cached:!1}))}try{i.lookup(a.host,{all:!0},(d,e)=>{if(d)return c?b(null,o(c.value,{cached:!0,error:d})):b(d);let f=!!e&&e.filter(a=>l(a.family)).map(a=>a.address).shift();if(e&&e.length&&!f&&console.warn(`Failed to resolve IPv${e[0].family} addresses with current network`),!f&&c)return b(null,o(c.value,{cached:!0}));let g={addresses:f?[f]:[a.host],servername:a.servername||a.host};return n.set(a.host,{value:g,expires:Date.now()+(a.dnsTtl||3e5)}),b(null,o(g,{cached:!1}))})}catch(a){if(c)return b(null,o(c.value,{cached:!0,error:a}));return b(a)}})})},a.exports.parseConnectionUrl=a=>{a=a||"";let b={};return[e.parse(a,!0)].forEach(a=>{let c;switch(a.protocol){case"smtp:":b.secure=!1;break;case"smtps:":b.secure=!0;break;case"direct:":b.direct=!0}!isNaN(a.port)&&Number(a.port)&&(b.port=Number(a.port)),a.hostname&&(b.host=a.hostname),a.auth&&(c=a.auth.split(":"),b.auth||(b.auth={}),b.auth.user=c.shift(),b.auth.pass=c.join(":")),Object.keys(a.query||{}).forEach(c=>{let d=b,e=c,f=a.query[c];switch(!isNaN(f)&&(f=Number(f)),f){case"true":f=!0;break;case"false":f=!1}if(0===c.indexOf("tls."))e=c.substr(4),b.tls||(b.tls={}),d=b.tls;else if(c.indexOf(".")>=0)return;e in d||(d[e]=f)})}),b},a.exports._logFunc=(a,b,c,d,e,...f)=>{let g={};Object.keys(c||{}).forEach(a=>{"level"!==a&&(g[a]=c[a])}),Object.keys(d||{}).forEach(a=>{"level"!==a&&(g[a]=d[a])}),a[b](g,e,...f)},a.exports.getLogger=(b,c)=>{let d={},e=["trace","debug","info","warn","error","fatal"];if(!(b=b||{}).logger)return e.forEach(a=>{d[a]=()=>!1}),d;let g=b.logger;return!0===b.logger&&(g=function(a){let b=0,c=new Map;a.forEach(a=>{a.length>b&&(b=a.length)}),a.forEach(a=>{let d=a.toUpperCase();d.length<b&&(d+=" ".repeat(b-d.length)),c.set(a,d)});let d=(a,b,d,...e)=>{let g="";b&&("server"===b.tnx?g="S: ":"client"===b.tnx&&(g="C: "),b.sid&&(g="["+b.sid+"] "+g),b.cid&&(g="[#"+b.cid+"] "+g)),(d=f.format(d,...e)).split(/\r?\n/).forEach(b=>{console.log("[%s] %s %s",new Date().toISOString().substr(0,19).replace(/T/," "),c.get(a),g+b)})},e={};return a.forEach(a=>{e[a]=d.bind(null,a)}),e}(e)),e.forEach(b=>{d[b]=(d,e,...f)=>{a.exports._logFunc(g,b,c,d,e,...f)}}),d},a.exports.callbackPromise=(a,b)=>function(){let c=Array.from(arguments),d=c.shift();d?b(d):a(...c)},a.exports.parseDataURI=a=>{let b,c=a.indexOf(",");if(!c)return a;let d=a.substring(c+1),e=a.substring(5,c).split(";"),f=e.length>1&&e[e.length-1];f&&0>f.indexOf("=")&&(b=f.toLowerCase(),e.pop());let g=e.shift()||"application/octet-stream",h={};for(let a of e){let b=a.indexOf("=");if(b>=0){let c=a.substring(0,b),d=a.substring(b+1);h[c]=d}}switch(b){case"base64":d=Buffer.from(d,"base64");break;case"utf8":d=Buffer.from(d);break;default:try{d=Buffer.from(decodeURIComponent(d))}catch(a){d=Buffer.from(d)}d=Buffer.from(d)}return{data:d,encoding:b,contentType:g,params:h}},a.exports.resolveContent=(b,c,d)=>{let e;d||(e=new Promise((b,c)=>{d=a.exports.callbackPromise(b,c)}));let f=b&&b[c]&&b[c].content||b[c],i=("object"==typeof b[c]&&b[c].encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");if(!f)return d(null,f);if("object"==typeof f){if("function"==typeof f.pipe)return p(f,(a,e)=>{if(a)return d(a);b[c].content?b[c].content=e:b[c]=e,d(null,e)});else if(/^https?:\/\//i.test(f.path||f.href))return p(h(f.path||f.href),d);else if(/^data:/i.test(f.path||f.href)){let b=a.exports.parseDataURI(f.path||f.href);return b&&b.data?d(null,b.data):d(null,Buffer.from(0))}else if(f.path)return p(g.createReadStream(f.path),d)}return"string"!=typeof b[c].content||["utf8","usascii","ascii"].includes(i)||(f=Buffer.from(b[c].content,i)),setImmediate(()=>d(null,f)),e},a.exports.assign=function(){let a=Array.from(arguments),b=a.shift()||{};return a.forEach(a=>{Object.keys(a||{}).forEach(c=>{["tls","auth"].includes(c)&&a[c]&&"object"==typeof a[c]?(b[c]||(b[c]={}),Object.keys(a[c]).forEach(d=>{b[c][d]=a[c][d]})):b[c]=a[c]})}),b},a.exports.encodeXText=a=>{if(!/[^\x21-\x2A\x2C-\x3C\x3E-\x7E]/.test(a))return a;let b=Buffer.from(a),c="";for(let a=0,d=b.length;a<d;a++){let d=b[a];d<33||d>126||43===d||61===d?c+="+"+(d<16?"0":"")+d.toString(16).toUpperCase():c+=String.fromCharCode(d)}return c}},91570:(a,b,c)=>{"use strict";let d=c(34041),e=c(31534),f=c(74301),g=c(27910).PassThrough,h=c(29021),i=c(33873),j=c(55511);class k{constructor(a,b,c,d){this.options=a||{},this.keys=b,this.cacheTreshold=Number(this.options.cacheTreshold)||2097152,this.hashAlgo=this.options.hashAlgo||"sha256",this.cacheDir=this.options.cacheDir||!1,this.chunks=[],this.chunklen=0,this.readPos=0,this.cachePath=!!this.cacheDir&&i.join(this.cacheDir,"message."+Date.now()+"-"+j.randomBytes(14).toString("hex")),this.cache=!1,this.headers=!1,this.bodyHash=!1,this.parser=!1,this.relaxedBody=!1,this.input=c,this.output=d,this.output.usingCache=!1,this.hasErrored=!1,this.input.on("error",a=>{this.hasErrored=!0,this.cleanup(),d.emit("error",a)})}cleanup(){this.cache&&this.cachePath&&h.unlink(this.cachePath,()=>!1)}createReadCache(){this.cache=h.createReadStream(this.cachePath),this.cache.once("error",a=>{this.cleanup(),this.output.emit("error",a)}),this.cache.once("close",()=>{this.cleanup()}),this.cache.pipe(this.output)}sendNextChunk(){if(this.hasErrored)return;if(this.readPos>=this.chunks.length)return this.cache?this.createReadCache():this.output.end();let a=this.chunks[this.readPos++];if(!1===this.output.write(a))return this.output.once("drain",()=>{this.sendNextChunk()});setImmediate(()=>this.sendNextChunk())}sendSignedOutput(){let a=0,b=()=>{if(a>=this.keys.length)return this.output.write(this.parser.rawHeaders),setImmediate(()=>this.sendNextChunk());let c=this.keys[a++],d=f(this.headers,this.hashAlgo,this.bodyHash,{domainName:c.domainName,keySelector:c.keySelector,privateKey:c.privateKey,headerFieldNames:this.options.headerFieldNames,skipFields:this.options.skipFields});return d&&this.output.write(Buffer.from(d+"\r\n")),setImmediate(b)};if(this.bodyHash&&this.headers)return b();this.output.write(this.parser.rawHeaders),this.sendNextChunk()}createWriteCache(){this.output.usingCache=!0,this.cache=h.createWriteStream(this.cachePath),this.cache.once("error",a=>{this.cleanup(),this.relaxedBody.unpipe(this.cache),this.relaxedBody.on("readable",()=>{for(;null!==this.relaxedBody.read(););}),this.hasErrored=!0,this.output.emit("error",a)}),this.cache.once("close",()=>{this.sendSignedOutput()}),this.relaxedBody.removeAllListeners("readable"),this.relaxedBody.pipe(this.cache)}signStream(){this.parser=new d,this.relaxedBody=new e({hashAlgo:this.hashAlgo}),this.parser.on("headers",a=>{this.headers=a}),this.relaxedBody.on("hash",a=>{this.bodyHash=a}),this.relaxedBody.on("readable",()=>{let a;if(!this.cache){for(;null!==(a=this.relaxedBody.read());)if(this.chunks.push(a),this.chunklen+=a.length,this.chunklen>=this.cacheTreshold&&this.cachePath)return this.createWriteCache()}}),this.relaxedBody.on("end",()=>{this.cache||this.sendSignedOutput()}),this.parser.pipe(this.relaxedBody),setImmediate(()=>this.input.pipe(this.parser))}}class l{constructor(a){this.options=a||{},this.keys=[].concat(this.options.keys||{domainName:a.domainName,keySelector:a.keySelector,privateKey:a.privateKey})}sign(a,b){let c=new g,d=a,e=!1;Buffer.isBuffer(a)?(e=a,d=new g):"string"==typeof a&&(e=Buffer.from(a),d=new g);let f=this.options;b&&Object.keys(b).length&&(f={},Object.keys(this.options||{}).forEach(a=>{f[a]=this.options[a]}),Object.keys(b||{}).forEach(a=>{a in f||(f[a]=b[a])}));let h=new k(f,this.keys,d,c);return setImmediate(()=>{h.signStream(),e&&setImmediate(()=>{d.end(e)})}),c}}a.exports=l},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{},97002:(a,b,c)=>{"use strict";let d=c(94735),e=c(49074),f=c(91292),g=c(39449),h=c(7180);class i extends d{constructor(a){super(),a=a||{},this.options=a||{},this.ses=this.options.SES,this.name="SESTransport",this.version=e.version,this.logger=f.getLogger(this.options,{component:this.options.component||"ses-transport"})}getRegion(a){return this.ses.sesClient.config&&"function"==typeof this.ses.sesClient.config.region?this.ses.sesClient.config.region().then(b=>a(null,b)).catch(b=>a(b)):a(null,!1)}send(a,b){let c={ts:Date.now(),pending:!0},d=a.message._headers.find(a=>/^from$/i.test(a.key));if(d){let a=new h("text/plain");d=a._convertAddresses(a._parseAddresses(d.value))}let e=a.data.envelope||a.message.getEnvelope(),f=a.message.messageId(),i=[].concat(e.to||[]);i.length>3&&i.push("...and "+i.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:f},"Sending message %s to <%s>",f,i.join(", ")),setImmediate(()=>{var h;let i,j,k,l;return h=(g,h)=>{if(g)return this.logger.error({err:g,tnx:"send",messageId:f},"Failed creating message for %s. %s",f,g.message),c.pending=!1,b(g);let i={Content:{Raw:{Data:h}},FromEmailAddress:d||e.from,Destination:{ToAddresses:e.to}};Object.keys(a.data.ses||{}).forEach(b=>{i[b]=a.data.ses[b]}),this.getRegion((a,d)=>{(a||!d)&&(d="us-east-1");let g=new this.ses.SendEmailCommand(i);this.ses.sesClient.send(g).then(a=>{"us-east-1"===d&&(d="email"),c.pending=!0,b(null,{envelope:{from:e.from,to:e.to},messageId:"<"+a.MessageId+(/@/.test(a.MessageId)?"":"@"+d+".amazonses.com")+">",response:a.MessageId,raw:h})}).catch(a=>{this.logger.error({err:a,tnx:"send"},"Send error for %s: %s",f,a.message),c.pending=!1,b(a)})})},a.data._dkim||(a.data._dkim={}),a.data._dkim.skipFields&&"string"==typeof a.data._dkim.skipFields?a.data._dkim.skipFields+=":date:message-id":a.data._dkim.skipFields="date:message-id",j=(i=a.message.createReadStream()).pipe(new g),k=[],l=0,void(j.on("readable",()=>{let a;for(;null!==(a=j.read());)k.push(a),l+=a.length}),i.once("error",a=>j.emit("error",a)),j.once("error",a=>{h(a)}),j.once("end",()=>h(null,Buffer.concat(k,l))))})}verify(a){let b;a||(b=new Promise((b,c)=>{a=f.callbackPromise(b,c)}));let c=b=>b&&!["InvalidParameterValue","MessageRejected"].includes(b.code||b.Code||b.name)?a(b):a(null,!0),d={Content:{Raw:{Data:Buffer.from("From: <invalid@invalid>\r\nTo: <invalid@invalid>\r\n Subject: Invalid\r\n\r\nInvalid")}},FromEmailAddress:"invalid@invalid",Destination:{ToAddresses:["invalid@invalid"]}};return this.getRegion((a,b)=>{(a||!b)&&(b="us-east-1");let e=new this.ses.SendEmailCommand(d);this.ses.sesClient.send(e).then(a=>c(null,a)).catch(a=>c(a))}),b}}a.exports=i}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=65473));module.exports=c})();