"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[595],{9655:(e,t,n)=>{let i,r,s,o,l,a,h,u,c,d;n.d(t,{C3:()=>iL,Jt:()=>iD,KR:()=>iS,TF:()=>ix,VC:()=>iN,hZ:()=>iR,yo:()=>iA});var _,p=n(1055),f=n(2881),g=n(1280),m=n(6702),y=n(1890);let v="@firebase/database",C="1.1.0",w="";class T{constructor(e){this.domStorage_=e,this.prefix_="firebase:"}set(e,t){null==t?this.domStorage_.removeItem(this.prefixedName_(e)):this.domStorage_.setItem(this.prefixedName_(e),(0,g.As)(t))}get(e){let t=this.domStorage_.getItem(this.prefixedName_(e));return null==t?null:(0,g.$L)(t)}remove(e){this.domStorage_.removeItem(this.prefixedName_(e))}prefixedName_(e){return this.prefix_+e}toString(){return this.domStorage_.toString()}}class I{constructor(){this.cache_={},this.isInMemoryStorage=!0}set(e,t){null==t?delete this.cache_[e]:this.cache_[e]=t}get(e){return(0,g.gR)(this.cache_,e)?this.cache_[e]:null}remove(e){delete this.cache_[e]}}let k=function(e){try{if("undefined"!=typeof window&&void 0!==window[e]){let t=window[e];return t.setItem("firebase:sentinel","cache"),t.removeItem("firebase:sentinel"),new T(t)}}catch(e){}return new I},b=k("localStorage"),E=k("sessionStorage"),S=new m.Vy("@firebase/database"),P=function(){let e=1;return function(){return e++}}(),N=function(e){let t=(0,g.kj)(e),n=new g.gz;n.update(t);let i=n.digest();return g.K3.encodeByteArray(i)},x=function(...e){let t="";for(let n=0;n<e.length;n++){let i=e[n];Array.isArray(i)||i&&"object"==typeof i&&"number"==typeof i.length?t+=x.apply(null,i):"object"==typeof i?t+=(0,g.As)(i):t+=i,t+=" "}return t},R=null,A=!0,D=function(e,t){(0,g.vA)(!t||!0===e||!1===e,"Can't turn on custom loggers persistently."),!0===e?(S.logLevel=m.$b.VERBOSE,R=S.log.bind(S),t&&E.set("logging_enabled",!0)):"function"==typeof e?R=e:(R=null,E.remove("logging_enabled"))},M=function(...e){if(!0===A&&(A=!1,null===R&&!0===E.get("logging_enabled")&&D(!0)),R){let t=x.apply(null,e);R(t)}},F=function(e){return function(...t){M(e,...t)}},O=function(...e){let t="FIREBASE INTERNAL ERROR: "+x(...e);S.error(t)},L=function(...e){let t=`FIREBASE FATAL ERROR: ${x(...e)}`;throw S.error(t),Error(t)},q=function(...e){let t="FIREBASE WARNING: "+x(...e);S.warn(t)},W=function(){"undefined"!=typeof window&&window.location&&window.location.protocol&&-1!==window.location.protocol.indexOf("https:")&&q("Insecure Firebase access from a secure page. Please use https in calls to new Firebase().")},U=function(e){return"number"==typeof e&&(e!=e||e===1/0||e===-1/0)},H=function(e){if((0,g.$g)()||"complete"===document.readyState)e();else{let t=!1,n=function(){if(!document.body)return void setTimeout(n,Math.floor(10));t||(t=!0,e())};document.addEventListener?(document.addEventListener("DOMContentLoaded",n,!1),window.addEventListener("load",n,!1)):document.attachEvent&&(document.attachEvent("onreadystatechange",()=>{"complete"===document.readyState&&n()}),window.attachEvent("onload",n))}},j="[MIN_NAME]",z="[MAX_NAME]",Y=function(e,t){if(e===t)return 0;{if(e===j||t===z)return -1;if(t===j||e===z)return 1;let n=J(e),i=J(t);if(null!==n)if(null!==i)return n-i==0?e.length-t.length:n-i;else return -1;return null!==i?1:e<t?-1:1}},K=function(e,t){return e===t?0:e<t?-1:1},V=function(e,t){if(t&&e in t)return t[e];throw Error("Missing required key ("+e+") in object: "+(0,g.As)(t))},B=function(e){if("object"!=typeof e||null===e)return(0,g.As)(e);let t=[];for(let n in e)t.push(n);t.sort();let n="{";for(let i=0;i<t.length;i++)0!==i&&(n+=","),n+=(0,g.As)(t[i]),n+=":",n+=B(e[t[i]]);return n+"}"},$=function(e,t){let n=e.length;if(n<=t)return[e];let i=[];for(let r=0;r<n;r+=t)r+t>n?i.push(e.substring(r,n)):i.push(e.substring(r,r+t));return i};function Q(e,t){for(let n in e)e.hasOwnProperty(n)&&t(n,e[n])}let G=function(e){let t,n,i,r,s;(0,g.vA)(!U(e),"Invalid JSON number");0===e?(n=0,i=0,t=+(1/e==-1/0)):(t=e<0,(e=Math.abs(e))>=22250738585072014e-324?(n=(r=Math.min(Math.floor(Math.log(e)/Math.LN2),1023))+1023,i=Math.round(e*Math.pow(2,52-r)-0x10000000000000)):(n=0,i=Math.round(e/5e-324)));let o=[];for(s=52;s;s-=1)o.push(i%2?1:0),i=Math.floor(i/2);for(s=11;s;s-=1)o.push(n%2?1:0),n=Math.floor(n/2);o.push(+!!t),o.reverse();let l=o.join(""),a="";for(s=0;s<64;s+=8){let e=parseInt(l.substr(s,8),2).toString(16);1===e.length&&(e="0"+e),a+=e}return a.toLowerCase()},X=RegExp("^-?(0*)\\d{1,10}$"),J=function(e){if(X.test(e)){let t=Number(e);if(t>=-0x80000000&&t<=0x7fffffff)return t}return null},Z=function(e){try{e()}catch(e){setTimeout(()=>{throw q("Exception was thrown by user callback.",e.stack||""),e},Math.floor(0))}},ee=function(e,t){let n=setTimeout(e,t);return"number"==typeof n&&"undefined"!=typeof Deno&&Deno.unrefTimer?Deno.unrefTimer(n):"object"==typeof n&&n.unref&&n.unref(),n};class et{constructor(e,t){this.appCheckProvider=t,this.appName=e.name,(0,p.xZ)(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.appCheck=t?.getImmediate({optional:!0}),this.appCheck||t?.get().then(e=>this.appCheck=e)}getToken(e){if(this.serverAppAppCheckToken){if(e)throw Error("Attempted reuse of `FirebaseServerApp.appCheckToken` after previous usage failed.");return Promise.resolve({token:this.serverAppAppCheckToken})}return this.appCheck?this.appCheck.getToken(e):new Promise((t,n)=>{setTimeout(()=>{this.appCheck?this.getToken(e).then(t,n):t(null)},0)})}addTokenChangeListener(e){this.appCheckProvider?.get().then(t=>t.addTokenListener(e))}notifyForInvalidToken(){q(`Provided AppCheck credentials for the app named "${this.appName}" are invalid. This usually indicates your app was not initialized correctly.`)}}class en{constructor(e,t,n){this.appName_=e,this.firebaseOptions_=t,this.authProvider_=n,this.auth_=null,this.auth_=n.getImmediate({optional:!0}),this.auth_||n.onInit(e=>this.auth_=e)}getToken(e){return this.auth_?this.auth_.getToken(e).catch(e=>e&&"auth/token-not-initialized"===e.code?(M("Got auth/token-not-initialized error.  Treating as null token."),null):Promise.reject(e)):new Promise((t,n)=>{setTimeout(()=>{this.auth_?this.getToken(e).then(t,n):t(null)},0)})}addTokenChangeListener(e){this.auth_?this.auth_.addAuthTokenListener(e):this.authProvider_.get().then(t=>t.addAuthTokenListener(e))}removeTokenChangeListener(e){this.authProvider_.get().then(t=>t.removeAuthTokenListener(e))}notifyForInvalidToken(){let e='Provided authentication credentials for the app named "'+this.appName_+'" are invalid. This usually indicates your app was not initialized correctly. ';"credential"in this.firebaseOptions_?e+='Make sure the "credential" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':"serviceAccount"in this.firebaseOptions_?e+='Make sure the "serviceAccount" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':e+='Make sure the "apiKey" and "databaseURL" properties provided to initializeApp() match the values provided for your app at https://console.firebase.google.com/.',q(e)}}class ei{constructor(e){this.accessToken=e}getToken(e){return Promise.resolve({accessToken:this.accessToken})}addTokenChangeListener(e){e(this.accessToken)}removeTokenChangeListener(e){}notifyForInvalidToken(){}}ei.OWNER="owner";let er=/(console\.firebase|firebase-console-\w+\.corp|firebase\.corp)\.google\.com/,es="websocket",eo="long_polling";class el{constructor(e,t,n,i,r=!1,s="",o=!1,l=!1,a=null){this.secure=t,this.namespace=n,this.webSocketOnly=i,this.nodeAdmin=r,this.persistenceKey=s,this.includeNamespaceInQueryParams=o,this.isUsingEmulator=l,this.emulatorOptions=a,this._host=e.toLowerCase(),this._domain=this._host.substr(this._host.indexOf(".")+1),this.internalHost=b.get("host:"+e)||this._host}isCacheableHost(){return"s-"===this.internalHost.substr(0,2)}isCustomHost(){return"firebaseio.com"!==this._domain&&"firebaseio-demo.com"!==this._domain}get host(){return this._host}set host(e){e!==this.internalHost&&(this.internalHost=e,this.isCacheableHost()&&b.set("host:"+this._host,this.internalHost))}toString(){let e=this.toURLString();return this.persistenceKey&&(e+="<"+this.persistenceKey+">"),e}toURLString(){let e=this.secure?"https://":"http://",t=this.includeNamespaceInQueryParams?`?ns=${this.namespace}`:"";return`${e}${this.host}/${t}`}}function ea(e,t,n){let i;if((0,g.vA)("string"==typeof t,"typeof type must == string"),(0,g.vA)("object"==typeof n,"typeof params must == object"),t===es)i=(e.secure?"wss://":"ws://")+e.internalHost+"/.ws?";else if(t===eo)i=(e.secure?"https://":"http://")+e.internalHost+"/.lp?";else throw Error("Unknown connection type: "+t);(e.host!==e.internalHost||e.isCustomHost()||e.includeNamespaceInQueryParams)&&(n.ns=e.namespace);let r=[];return Q(n,(e,t)=>{r.push(e+"="+t)}),i+r.join("&")}class eh{constructor(){this.counters_={}}incrementCounter(e,t=1){(0,g.gR)(this.counters_,e)||(this.counters_[e]=0),this.counters_[e]+=t}get(){return(0,g.A4)(this.counters_)}}let eu={},ec={};function ed(e){let t=e.toString();return eu[t]||(eu[t]=new eh),eu[t]}class e_{constructor(e){this.onMessage_=e,this.pendingResponses=[],this.currentResponseNum=0,this.closeAfterResponse=-1,this.onClose=null}closeAfter(e,t){this.closeAfterResponse=e,this.onClose=t,this.closeAfterResponse<this.currentResponseNum&&(this.onClose(),this.onClose=null)}handleResponse(e,t){for(this.pendingResponses[e]=t;this.pendingResponses[this.currentResponseNum];){let e=this.pendingResponses[this.currentResponseNum];delete this.pendingResponses[this.currentResponseNum];for(let t=0;t<e.length;++t)e[t]&&Z(()=>{this.onMessage_(e[t])});if(this.currentResponseNum===this.closeAfterResponse){this.onClose&&(this.onClose(),this.onClose=null);break}this.currentResponseNum++}}}let ep="start";class ef{constructor(e,t,n,i,r,s,o){this.connId=e,this.repoInfo=t,this.applicationId=n,this.appCheckToken=i,this.authToken=r,this.transportSessionId=s,this.lastSessionId=o,this.bytesSent=0,this.bytesReceived=0,this.everConnected_=!1,this.log_=F(e),this.stats_=ed(t),this.urlFn=e=>(this.appCheckToken&&(e.ac=this.appCheckToken),ea(t,eo,e))}open(e,t){this.curSegmentNum=0,this.onDisconnect_=t,this.myPacketOrderer=new e_(e),this.isClosed_=!1,this.connectTimeoutTimer_=setTimeout(()=>{this.log_("Timed out trying to connect."),this.onClosed_(),this.connectTimeoutTimer_=null},Math.floor(3e4)),H(()=>{if(this.isClosed_)return;this.scriptTagHolder=new eg((...e)=>{let[t,n,i,r,s]=e;if(this.incrementIncomingBytes_(e),this.scriptTagHolder)if(this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null),this.everConnected_=!0,t===ep)this.id=n,this.password=i;else if("close"===t)n?(this.scriptTagHolder.sendNewPolls=!1,this.myPacketOrderer.closeAfter(n,()=>{this.onClosed_()})):this.onClosed_();else throw Error("Unrecognized command received: "+t)},(...e)=>{let[t,n]=e;this.incrementIncomingBytes_(e),this.myPacketOrderer.handleResponse(t,n)},()=>{this.onClosed_()},this.urlFn);let e={};e[ep]="t",e.ser=Math.floor(1e8*Math.random()),this.scriptTagHolder.uniqueCallbackIdentifier&&(e.cb=this.scriptTagHolder.uniqueCallbackIdentifier),e.v="5",this.transportSessionId&&(e.s=this.transportSessionId),this.lastSessionId&&(e.ls=this.lastSessionId),this.applicationId&&(e.p=this.applicationId),this.appCheckToken&&(e.ac=this.appCheckToken),"undefined"!=typeof location&&location.hostname&&er.test(location.hostname)&&(e.r="f");let t=this.urlFn(e);this.log_("Connecting via long-poll to "+t),this.scriptTagHolder.addTag(t,()=>{})})}start(){this.scriptTagHolder.startLongPoll(this.id,this.password),this.addDisconnectPingFrame(this.id,this.password)}static forceAllow(){ef.forceAllow_=!0}static forceDisallow(){ef.forceDisallow_=!0}static isAvailable(){return!(0,g.$g)()&&(!!ef.forceAllow_||!ef.forceDisallow_&&"undefined"!=typeof document&&null!=document.createElement&&!("object"==typeof window&&window.chrome&&window.chrome.extension&&!/^chrome/.test(window.location.href))&&("object"!=typeof Windows||"object"!=typeof Windows.UI))}markConnectionHealthy(){}shutdown_(){this.isClosed_=!0,this.scriptTagHolder&&(this.scriptTagHolder.close(),this.scriptTagHolder=null),this.myDisconnFrame&&(document.body.removeChild(this.myDisconnFrame),this.myDisconnFrame=null),this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null)}onClosed_(){!this.isClosed_&&(this.log_("Longpoll is closing itself"),this.shutdown_(),this.onDisconnect_&&(this.onDisconnect_(this.everConnected_),this.onDisconnect_=null))}close(){this.isClosed_||(this.log_("Longpoll is being closed."),this.shutdown_())}send(e){let t=(0,g.As)(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);let n=$((0,g.KA)(t),1840);for(let e=0;e<n.length;e++)this.scriptTagHolder.enqueueSegment(this.curSegmentNum,n.length,n[e]),this.curSegmentNum++}addDisconnectPingFrame(e,t){if((0,g.$g)())return;this.myDisconnFrame=document.createElement("iframe");let n={};n.dframe="t",n.id=e,n.pw=t,this.myDisconnFrame.src=this.urlFn(n),this.myDisconnFrame.style.display="none",document.body.appendChild(this.myDisconnFrame)}incrementIncomingBytes_(e){let t=(0,g.As)(e).length;this.bytesReceived+=t,this.stats_.incrementCounter("bytes_received",t)}}class eg{constructor(e,t,n,i){if(this.onDisconnect=n,this.urlFn=i,this.outstandingRequests=new Set,this.pendingSegs=[],this.currentSerial=Math.floor(1e8*Math.random()),this.sendNewPolls=!0,(0,g.$g)())this.commandCB=e,this.onMessageCB=t;else{this.uniqueCallbackIdentifier=P(),window["pLPCommand"+this.uniqueCallbackIdentifier]=e,window["pRTLPCB"+this.uniqueCallbackIdentifier]=t,this.myIFrame=eg.createIFrame_();let n="";this.myIFrame.src&&"javascript:"===this.myIFrame.src.substr(0,11)&&(n='<script>document.domain="'+document.domain+'";<\/script>');let i="<html><body>"+n+"</body></html>";try{this.myIFrame.doc.open(),this.myIFrame.doc.write(i),this.myIFrame.doc.close()}catch(e){M("frame writing exception"),e.stack&&M(e.stack),M(e)}}}static createIFrame_(){let e=document.createElement("iframe");if(e.style.display="none",document.body){document.body.appendChild(e);try{e.contentWindow.document||M("No IE domain setting required")}catch(t){e.src="javascript:void((function(){document.open();document.domain='"+document.domain+"';document.close();})())"}}else throw"Document body has not initialized. Wait to initialize Firebase until after the document is ready.";return e.contentDocument?e.doc=e.contentDocument:e.contentWindow?e.doc=e.contentWindow.document:e.document&&(e.doc=e.document),e}close(){this.alive=!1,this.myIFrame&&(this.myIFrame.doc.body.textContent="",setTimeout(()=>{null!==this.myIFrame&&(document.body.removeChild(this.myIFrame),this.myIFrame=null)},Math.floor(0)));let e=this.onDisconnect;e&&(this.onDisconnect=null,e())}startLongPoll(e,t){for(this.myID=e,this.myPW=t,this.alive=!0;this.newRequest_(););}newRequest_(){if(!this.alive||!this.sendNewPolls||!(this.outstandingRequests.size<(this.pendingSegs.length>0?2:1)))return!1;{this.currentSerial++;let e={};e.id=this.myID,e.pw=this.myPW,e.ser=this.currentSerial;let t=this.urlFn(e),n="",i=0;for(;this.pendingSegs.length>0;)if(this.pendingSegs[0].d.length+30+n.length<=1870){let e=this.pendingSegs.shift();n=n+"&seg"+i+"="+e.seg+"&ts"+i+"="+e.ts+"&d"+i+"="+e.d,i++}else break;return t+=n,this.addLongPollTag_(t,this.currentSerial),!0}}enqueueSegment(e,t,n){this.pendingSegs.push({seg:e,ts:t,d:n}),this.alive&&this.newRequest_()}addLongPollTag_(e,t){this.outstandingRequests.add(t);let n=()=>{this.outstandingRequests.delete(t),this.newRequest_()},i=setTimeout(n,Math.floor(25e3));this.addTag(e,()=>{clearTimeout(i),n()})}addTag(e,t){(0,g.$g)()?this.doNodeLongPoll(e,t):setTimeout(()=>{try{if(!this.sendNewPolls)return;let n=this.myIFrame.doc.createElement("script");n.type="text/javascript",n.async=!0,n.src=e,n.onload=n.onreadystatechange=function(){let e=n.readyState;e&&"loaded"!==e&&"complete"!==e||(n.onload=n.onreadystatechange=null,n.parentNode&&n.parentNode.removeChild(n),t())},n.onerror=()=>{M("Long-poll script failed to load: "+e),this.sendNewPolls=!1,this.close()},this.myIFrame.doc.body.appendChild(n)}catch(e){}},Math.floor(1))}}let em=null;"undefined"!=typeof MozWebSocket?em=MozWebSocket:"undefined"!=typeof WebSocket&&(em=WebSocket);class ey{constructor(e,t,n,i,r,s,o){this.connId=e,this.applicationId=n,this.appCheckToken=i,this.authToken=r,this.keepaliveTimer=null,this.frames=null,this.totalFrames=0,this.bytesSent=0,this.bytesReceived=0,this.log_=F(this.connId),this.stats_=ed(t),this.connURL=ey.connectionURL_(t,s,o,i,n),this.nodeAdmin=t.nodeAdmin}static connectionURL_(e,t,n,i,r){let s={};return s.v="5",!(0,g.$g)()&&"undefined"!=typeof location&&location.hostname&&er.test(location.hostname)&&(s.r="f"),t&&(s.s=t),n&&(s.ls=n),i&&(s.ac=i),r&&(s.p=r),ea(e,es,s)}open(e,t){this.onDisconnect=t,this.onMessage=e,this.log_("Websocket connecting to "+this.connURL),this.everConnected_=!1,b.set("previous_websocket_failure",!0);try{let e;if((0,g.$g)()){let t=this.nodeAdmin?"AdminNode":"Node";e={headers:{"User-Agent":`Firebase/5/${w}/${y.platform}/${t}`,"X-Firebase-GMPID":this.applicationId||""}},this.authToken&&(e.headers.Authorization=`Bearer ${this.authToken}`),this.appCheckToken&&(e.headers["X-Firebase-AppCheck"]=this.appCheckToken);let n=y.env,i=0===this.connURL.indexOf("wss://")?n.HTTPS_PROXY||n.https_proxy:n.HTTP_PROXY||n.http_proxy;i&&(e.proxy={origin:i})}this.mySock=new em(this.connURL,[],e)}catch(t){this.log_("Error instantiating WebSocket.");let e=t.message||t.data;e&&this.log_(e),this.onClosed_();return}this.mySock.onopen=()=>{this.log_("Websocket connected."),this.everConnected_=!0},this.mySock.onclose=()=>{this.log_("Websocket connection was disconnected."),this.mySock=null,this.onClosed_()},this.mySock.onmessage=e=>{this.handleIncomingFrame(e)},this.mySock.onerror=e=>{this.log_("WebSocket error.  Closing connection.");let t=e.message||e.data;t&&this.log_(t),this.onClosed_()}}start(){}static forceDisallow(){ey.forceDisallow_=!0}static isAvailable(){let e=!1;if("undefined"!=typeof navigator&&navigator.userAgent){let t=navigator.userAgent.match(/Android ([0-9]{0,}\.[0-9]{0,})/);t&&t.length>1&&4.4>parseFloat(t[1])&&(e=!0)}return!e&&null!==em&&!ey.forceDisallow_}static previouslyFailed(){return b.isInMemoryStorage||!0===b.get("previous_websocket_failure")}markConnectionHealthy(){b.remove("previous_websocket_failure")}appendFrame_(e){if(this.frames.push(e),this.frames.length===this.totalFrames){let e=this.frames.join("");this.frames=null;let t=(0,g.$L)(e);this.onMessage(t)}}handleNewFrameCount_(e){this.totalFrames=e,this.frames=[]}extractFrameCount_(e){if((0,g.vA)(null===this.frames,"We already have a frame buffer"),e.length<=6){let t=Number(e);if(!isNaN(t))return this.handleNewFrameCount_(t),null}return this.handleNewFrameCount_(1),e}handleIncomingFrame(e){if(null===this.mySock)return;let t=e.data;if(this.bytesReceived+=t.length,this.stats_.incrementCounter("bytes_received",t.length),this.resetKeepAlive(),null!==this.frames)this.appendFrame_(t);else{let e=this.extractFrameCount_(t);null!==e&&this.appendFrame_(e)}}send(e){this.resetKeepAlive();let t=(0,g.As)(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);let n=$(t,16384);n.length>1&&this.sendString_(String(n.length));for(let e=0;e<n.length;e++)this.sendString_(n[e])}shutdown_(){this.isClosed_=!0,this.keepaliveTimer&&(clearInterval(this.keepaliveTimer),this.keepaliveTimer=null),this.mySock&&(this.mySock.close(),this.mySock=null)}onClosed_(){!this.isClosed_&&(this.log_("WebSocket is closing itself"),this.shutdown_(),this.onDisconnect&&(this.onDisconnect(this.everConnected_),this.onDisconnect=null))}close(){this.isClosed_||(this.log_("WebSocket is being closed"),this.shutdown_())}resetKeepAlive(){clearInterval(this.keepaliveTimer),this.keepaliveTimer=setInterval(()=>{this.mySock&&this.sendString_("0"),this.resetKeepAlive()},Math.floor(45e3))}sendString_(e){try{this.mySock.send(e)}catch(e){this.log_("Exception thrown from WebSocket.send():",e.message||e.data,"Closing connection."),setTimeout(this.onClosed_.bind(this),0)}}}ey.responsesRequiredToBeHealthy=2,ey.healthyTimeout=3e4;class ev{static get ALL_TRANSPORTS(){return[ef,ey]}static get IS_TRANSPORT_INITIALIZED(){return this.globalTransportInitialized_}constructor(e){this.initTransports_(e)}initTransports_(e){let t=ey&&ey.isAvailable(),n=t&&!ey.previouslyFailed();if(e.webSocketOnly&&(t||q("wss:// URL used, but browser isn't known to support websockets.  Trying anyway."),n=!0),n)this.transports_=[ey];else{let e=this.transports_=[];for(let t of ev.ALL_TRANSPORTS)t&&t.isAvailable()&&e.push(t);ev.globalTransportInitialized_=!0}}initialTransport(){if(this.transports_.length>0)return this.transports_[0];throw Error("No transports available")}upgradeTransport(){return this.transports_.length>1?this.transports_[1]:null}}ev.globalTransportInitialized_=!1;class eC{constructor(e,t,n,i,r,s,o,l,a,h){this.id=e,this.repoInfo_=t,this.applicationId_=n,this.appCheckToken_=i,this.authToken_=r,this.onMessage_=s,this.onReady_=o,this.onDisconnect_=l,this.onKill_=a,this.lastSessionId=h,this.connectionCount=0,this.pendingDataMessages=[],this.state_=0,this.log_=F("c:"+this.id+":"),this.transportManager_=new ev(t),this.log_("Connection created"),this.start_()}start_(){let e=this.transportManager_.initialTransport();this.conn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,null,this.lastSessionId),this.primaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;let t=this.connReceiver_(this.conn_),n=this.disconnReceiver_(this.conn_);this.tx_=this.conn_,this.rx_=this.conn_,this.secondaryConn_=null,this.isHealthy_=!1,setTimeout(()=>{this.conn_&&this.conn_.open(t,n)},Math.floor(0));let i=e.healthyTimeout||0;i>0&&(this.healthyTimeout_=ee(()=>{this.healthyTimeout_=null,this.isHealthy_||(this.conn_&&this.conn_.bytesReceived>102400?(this.log_("Connection exceeded healthy timeout but has received "+this.conn_.bytesReceived+" bytes.  Marking connection healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()):this.conn_&&this.conn_.bytesSent>10240?this.log_("Connection exceeded healthy timeout but has sent "+this.conn_.bytesSent+" bytes.  Leaving connection alive."):(this.log_("Closing unhealthy connection after timeout."),this.close()))},Math.floor(i)))}nextTransportId_(){return"c:"+this.id+":"+this.connectionCount++}disconnReceiver_(e){return t=>{e===this.conn_?this.onConnectionLost_(t):e===this.secondaryConn_?(this.log_("Secondary connection lost."),this.onSecondaryConnectionLost_()):this.log_("closing an old connection")}}connReceiver_(e){return t=>{2!==this.state_&&(e===this.rx_?this.onPrimaryMessageReceived_(t):e===this.secondaryConn_?this.onSecondaryMessageReceived_(t):this.log_("message on old connection"))}}sendRequest(e){this.sendData_({t:"d",d:e})}tryCleanupConnection(){this.tx_===this.secondaryConn_&&this.rx_===this.secondaryConn_&&(this.log_("cleaning up and promoting a connection: "+this.secondaryConn_.connId),this.conn_=this.secondaryConn_,this.secondaryConn_=null)}onSecondaryControl_(e){if("t"in e){let t=e.t;"a"===t?this.upgradeIfSecondaryHealthy_():"r"===t?(this.log_("Got a reset on secondary, closing it"),this.secondaryConn_.close(),(this.tx_===this.secondaryConn_||this.rx_===this.secondaryConn_)&&this.close()):"o"===t&&(this.log_("got pong on secondary."),this.secondaryResponsesRequired_--,this.upgradeIfSecondaryHealthy_())}}onSecondaryMessageReceived_(e){let t=V("t",e),n=V("d",e);if("c"===t)this.onSecondaryControl_(n);else if("d"===t)this.pendingDataMessages.push(n);else throw Error("Unknown protocol layer: "+t)}upgradeIfSecondaryHealthy_(){this.secondaryResponsesRequired_<=0?(this.log_("Secondary connection is healthy."),this.isHealthy_=!0,this.secondaryConn_.markConnectionHealthy(),this.proceedWithUpgrade_()):(this.log_("sending ping on secondary."),this.secondaryConn_.send({t:"c",d:{t:"p",d:{}}}))}proceedWithUpgrade_(){this.secondaryConn_.start(),this.log_("sending client ack on secondary"),this.secondaryConn_.send({t:"c",d:{t:"a",d:{}}}),this.log_("Ending transmission on primary"),this.conn_.send({t:"c",d:{t:"n",d:{}}}),this.tx_=this.secondaryConn_,this.tryCleanupConnection()}onPrimaryMessageReceived_(e){let t=V("t",e),n=V("d",e);"c"===t?this.onControl_(n):"d"===t&&this.onDataMessage_(n)}onDataMessage_(e){this.onPrimaryResponse_(),this.onMessage_(e)}onPrimaryResponse_(){!this.isHealthy_&&(this.primaryResponsesRequired_--,this.primaryResponsesRequired_<=0&&(this.log_("Primary connection is healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()))}onControl_(e){let t=V("t",e);if("d"in e){let n=e.d;if("h"===t){let e={...n};this.repoInfo_.isUsingEmulator&&(e.h=this.repoInfo_.host),this.onHandshake_(e)}else if("n"===t){this.log_("recvd end transmission on primary"),this.rx_=this.secondaryConn_;for(let e=0;e<this.pendingDataMessages.length;++e)this.onDataMessage_(this.pendingDataMessages[e]);this.pendingDataMessages=[],this.tryCleanupConnection()}else"s"===t?this.onConnectionShutdown_(n):"r"===t?this.onReset_(n):"e"===t?O("Server Error: "+n):"o"===t?(this.log_("got pong on primary."),this.onPrimaryResponse_(),this.sendPingOnPrimaryIfNecessary_()):O("Unknown control packet command: "+t)}}onHandshake_(e){let t=e.ts,n=e.v,i=e.h;this.sessionId=e.s,this.repoInfo_.host=i,0===this.state_&&(this.conn_.start(),this.onConnectionEstablished_(this.conn_,t),"5"!==n&&q("Protocol version mismatch detected"),this.tryStartUpgrade_())}tryStartUpgrade_(){let e=this.transportManager_.upgradeTransport();e&&this.startUpgrade_(e)}startUpgrade_(e){this.secondaryConn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,this.sessionId),this.secondaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;let t=this.connReceiver_(this.secondaryConn_),n=this.disconnReceiver_(this.secondaryConn_);this.secondaryConn_.open(t,n),ee(()=>{this.secondaryConn_&&(this.log_("Timed out trying to upgrade."),this.secondaryConn_.close())},Math.floor(6e4))}onReset_(e){this.log_("Reset packet received.  New host: "+e),this.repoInfo_.host=e,1===this.state_?this.close():(this.closeConnections_(),this.start_())}onConnectionEstablished_(e,t){this.log_("Realtime connection established."),this.conn_=e,this.state_=1,this.onReady_&&(this.onReady_(t,this.sessionId),this.onReady_=null),0===this.primaryResponsesRequired_?(this.log_("Primary connection is healthy."),this.isHealthy_=!0):ee(()=>{this.sendPingOnPrimaryIfNecessary_()},Math.floor(5e3))}sendPingOnPrimaryIfNecessary_(){this.isHealthy_||1!==this.state_||(this.log_("sending ping on primary."),this.sendData_({t:"c",d:{t:"p",d:{}}}))}onSecondaryConnectionLost_(){let e=this.secondaryConn_;this.secondaryConn_=null,(this.tx_===e||this.rx_===e)&&this.close()}onConnectionLost_(e){this.conn_=null,e||0!==this.state_?1===this.state_&&this.log_("Realtime connection lost."):(this.log_("Realtime connection failed."),this.repoInfo_.isCacheableHost()&&(b.remove("host:"+this.repoInfo_.host),this.repoInfo_.internalHost=this.repoInfo_.host)),this.close()}onConnectionShutdown_(e){this.log_("Connection shutdown command received. Shutting down..."),this.onKill_&&(this.onKill_(e),this.onKill_=null),this.onDisconnect_=null,this.close()}sendData_(e){if(1!==this.state_)throw"Connection is not connected";this.tx_.send(e)}close(){2!==this.state_&&(this.log_("Closing realtime connection."),this.state_=2,this.closeConnections_(),this.onDisconnect_&&(this.onDisconnect_(),this.onDisconnect_=null))}closeConnections_(){this.log_("Shutting down all connections"),this.conn_&&(this.conn_.close(),this.conn_=null),this.secondaryConn_&&(this.secondaryConn_.close(),this.secondaryConn_=null),this.healthyTimeout_&&(clearTimeout(this.healthyTimeout_),this.healthyTimeout_=null)}}class ew{put(e,t,n,i){}merge(e,t,n,i){}refreshAuthToken(e){}refreshAppCheckToken(e){}onDisconnectPut(e,t,n){}onDisconnectMerge(e,t,n){}onDisconnectCancel(e,t){}reportStats(e){}}class eT{constructor(e){this.allowedEvents_=e,this.listeners_={},(0,g.vA)(Array.isArray(e)&&e.length>0,"Requires a non-empty array")}trigger(e,...t){if(Array.isArray(this.listeners_[e])){let n=[...this.listeners_[e]];for(let e=0;e<n.length;e++)n[e].callback.apply(n[e].context,t)}}on(e,t,n){this.validateEventType_(e),this.listeners_[e]=this.listeners_[e]||[],this.listeners_[e].push({callback:t,context:n});let i=this.getInitialEvent(e);i&&t.apply(n,i)}off(e,t,n){this.validateEventType_(e);let i=this.listeners_[e]||[];for(let e=0;e<i.length;e++)if(i[e].callback===t&&(!n||n===i[e].context))return void i.splice(e,1)}validateEventType_(e){(0,g.vA)(this.allowedEvents_.find(t=>t===e),"Unknown event: "+e)}}class eI extends eT{static getInstance(){return new eI}constructor(){super(["online"]),this.online_=!0,"undefined"==typeof window||void 0===window.addEventListener||(0,g.jZ)()||(window.addEventListener("online",()=>{this.online_||(this.online_=!0,this.trigger("online",!0))},!1),window.addEventListener("offline",()=>{this.online_&&(this.online_=!1,this.trigger("online",!1))},!1))}getInitialEvent(e){return(0,g.vA)("online"===e,"Unknown event type: "+e),[this.online_]}currentlyOnline(){return this.online_}}class ek{constructor(e,t){if(void 0===t){this.pieces_=e.split("/");let t=0;for(let e=0;e<this.pieces_.length;e++)this.pieces_[e].length>0&&(this.pieces_[t]=this.pieces_[e],t++);this.pieces_.length=t,this.pieceNum_=0}else this.pieces_=e,this.pieceNum_=t}toString(){let e="";for(let t=this.pieceNum_;t<this.pieces_.length;t++)""!==this.pieces_[t]&&(e+="/"+this.pieces_[t]);return e||"/"}}function eb(){return new ek("")}function eE(e){return e.pieceNum_>=e.pieces_.length?null:e.pieces_[e.pieceNum_]}function eS(e){return e.pieces_.length-e.pieceNum_}function eP(e){let t=e.pieceNum_;return t<e.pieces_.length&&t++,new ek(e.pieces_,t)}function eN(e){return e.pieceNum_<e.pieces_.length?e.pieces_[e.pieces_.length-1]:null}function ex(e,t=0){return e.pieces_.slice(e.pieceNum_+t)}function eR(e){if(e.pieceNum_>=e.pieces_.length)return null;let t=[];for(let n=e.pieceNum_;n<e.pieces_.length-1;n++)t.push(e.pieces_[n]);return new ek(t,0)}function eA(e,t){let n=[];for(let t=e.pieceNum_;t<e.pieces_.length;t++)n.push(e.pieces_[t]);if(t instanceof ek)for(let e=t.pieceNum_;e<t.pieces_.length;e++)n.push(t.pieces_[e]);else{let e=t.split("/");for(let t=0;t<e.length;t++)e[t].length>0&&n.push(e[t])}return new ek(n,0)}function eD(e){return e.pieceNum_>=e.pieces_.length}function eM(e,t){let n=eE(e),i=eE(t);if(null===n)return t;if(n===i)return eM(eP(e),eP(t));throw Error("INTERNAL ERROR: innerPath ("+t+") is not within outerPath ("+e+")")}function eF(e,t){let n=ex(e,0),i=ex(t,0);for(let e=0;e<n.length&&e<i.length;e++){let t=Y(n[e],i[e]);if(0!==t)return t}return n.length===i.length?0:n.length<i.length?-1:1}function eO(e,t){if(eS(e)!==eS(t))return!1;for(let n=e.pieceNum_,i=t.pieceNum_;n<=e.pieces_.length;n++,i++)if(e.pieces_[n]!==t.pieces_[i])return!1;return!0}function eL(e,t){let n=e.pieceNum_,i=t.pieceNum_;if(eS(e)>eS(t))return!1;for(;n<e.pieces_.length;){if(e.pieces_[n]!==t.pieces_[i])return!1;++n,++i}return!0}class eq{constructor(e,t){this.errorPrefix_=t,this.parts_=ex(e,0),this.byteLength_=Math.max(1,this.parts_.length);for(let e=0;e<this.parts_.length;e++)this.byteLength_+=(0,g.OE)(this.parts_[e]);eW(this)}}function eW(e){if(e.byteLength_>768)throw Error(e.errorPrefix_+"has a key path longer than 768 bytes ("+e.byteLength_+").");if(e.parts_.length>32)throw Error(e.errorPrefix_+"path specified exceeds the maximum depth that can be written (32) or object contains a cycle "+eU(e))}function eU(e){return 0===e.parts_.length?"":"in property '"+e.parts_.join(".")+"'"}class eH extends eT{static getInstance(){return new eH}constructor(){let e,t;super(["visible"]),"undefined"!=typeof document&&void 0!==document.addEventListener&&(void 0!==document.hidden?(t="visibilitychange",e="hidden"):void 0!==document.mozHidden?(t="mozvisibilitychange",e="mozHidden"):void 0!==document.msHidden?(t="msvisibilitychange",e="msHidden"):void 0!==document.webkitHidden&&(t="webkitvisibilitychange",e="webkitHidden")),this.visible_=!0,t&&document.addEventListener(t,()=>{let t=!document[e];t!==this.visible_&&(this.visible_=t,this.trigger("visible",t))},!1)}getInitialEvent(e){return(0,g.vA)("visible"===e,"Unknown event type: "+e),[this.visible_]}}class ej extends ew{constructor(e,t,n,i,r,s,o,l){if(super(),this.repoInfo_=e,this.applicationId_=t,this.onDataUpdate_=n,this.onConnectStatus_=i,this.onServerInfoUpdate_=r,this.authTokenProvider_=s,this.appCheckTokenProvider_=o,this.authOverride_=l,this.id=ej.nextPersistentConnectionId_++,this.log_=F("p:"+this.id+":"),this.interruptReasons_={},this.listens=new Map,this.outstandingPuts_=[],this.outstandingGets_=[],this.outstandingPutCount_=0,this.outstandingGetCount_=0,this.onDisconnectRequestQueue_=[],this.connected_=!1,this.reconnectDelay_=1e3,this.maxReconnectDelay_=3e5,this.securityDebugCallback_=null,this.lastSessionId=null,this.establishConnectionTimer_=null,this.visible_=!1,this.requestCBHash_={},this.requestNumber_=0,this.realtime_=null,this.authToken_=null,this.appCheckToken_=null,this.forceTokenRefresh_=!1,this.invalidAuthTokenCount_=0,this.invalidAppCheckTokenCount_=0,this.firstConnection_=!0,this.lastConnectionAttemptTime_=null,this.lastConnectionEstablishedTime_=null,l&&!(0,g.$g)())throw Error("Auth override specified in options, but not supported on non Node.js platforms");eH.getInstance().on("visible",this.onVisible_,this),-1===e.host.indexOf("fblocal")&&eI.getInstance().on("online",this.onOnline_,this)}sendRequest(e,t,n){let i=++this.requestNumber_,r={r:i,a:e,b:t};this.log_((0,g.As)(r)),(0,g.vA)(this.connected_,"sendRequest call when we're not connected not allowed."),this.realtime_.sendRequest(r),n&&(this.requestCBHash_[i]=n)}get(e){this.initConnection_();let t=new g.cY,n={p:e._path.toString(),q:e._queryObject};this.outstandingGets_.push({action:"g",request:n,onComplete:e=>{let n=e.d;"ok"===e.s?t.resolve(n):t.reject(n)}}),this.outstandingGetCount_++;let i=this.outstandingGets_.length-1;return this.connected_&&this.sendGet_(i),t.promise}listen(e,t,n,i){this.initConnection_();let r=e._queryIdentifier,s=e._path.toString();this.log_("Listen called for "+s+" "+r),this.listens.has(s)||this.listens.set(s,new Map),(0,g.vA)(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"listen() called for non-default but complete query"),(0,g.vA)(!this.listens.get(s).has(r),"listen() called twice for same path/queryId.");let o={onComplete:i,hashFn:t,query:e,tag:n};this.listens.get(s).set(r,o),this.connected_&&this.sendListen_(o)}sendGet_(e){let t=this.outstandingGets_[e];this.sendRequest("g",t.request,n=>{delete this.outstandingGets_[e],this.outstandingGetCount_--,0===this.outstandingGetCount_&&(this.outstandingGets_=[]),t.onComplete&&t.onComplete(n)})}sendListen_(e){let t=e.query,n=t._path.toString(),i=t._queryIdentifier;this.log_("Listen on "+n+" for "+i);let r={p:n};e.tag&&(r.q=t._queryObject,r.t=e.tag),r.h=e.hashFn(),this.sendRequest("q",r,r=>{let s=r.d,o=r.s;ej.warnOnListenWarnings_(s,t),(this.listens.get(n)&&this.listens.get(n).get(i))===e&&(this.log_("listen response",r),"ok"!==o&&this.removeListen_(n,i),e.onComplete&&e.onComplete(o,s))})}static warnOnListenWarnings_(e,t){if(e&&"object"==typeof e&&(0,g.gR)(e,"w")){let n=(0,g.yw)(e,"w");if(Array.isArray(n)&&~n.indexOf("no_index")){let e='".indexOn": "'+t._queryParams.getIndex().toString()+'"',n=t._path.toString();q(`Using an unspecified index. Your data will be downloaded and filtered on the client. Consider adding ${e} at ${n} to your security rules for better performance.`)}}}refreshAuthToken(e){this.authToken_=e,this.log_("Auth token refreshed"),this.authToken_?this.tryAuth():this.connected_&&this.sendRequest("unauth",{},()=>{}),this.reduceReconnectDelayIfAdminCredential_(e)}reduceReconnectDelayIfAdminCredential_(e){(e&&40===e.length||(0,g.qc)(e))&&(this.log_("Admin auth credential detected.  Reducing max reconnect time."),this.maxReconnectDelay_=3e4)}refreshAppCheckToken(e){this.appCheckToken_=e,this.log_("App check token refreshed"),this.appCheckToken_?this.tryAppCheck():this.connected_&&this.sendRequest("unappeck",{},()=>{})}tryAuth(){if(this.connected_&&this.authToken_){let e=this.authToken_,t=(0,g.Cv)(e)?"auth":"gauth",n={cred:e};null===this.authOverride_?n.noauth=!0:"object"==typeof this.authOverride_&&(n.authvar=this.authOverride_),this.sendRequest(t,n,t=>{let n=t.s,i=t.d||"error";this.authToken_===e&&("ok"===n?this.invalidAuthTokenCount_=0:this.onAuthRevoked_(n,i))})}}tryAppCheck(){this.connected_&&this.appCheckToken_&&this.sendRequest("appcheck",{token:this.appCheckToken_},e=>{let t=e.s,n=e.d||"error";"ok"===t?this.invalidAppCheckTokenCount_=0:this.onAppCheckRevoked_(t,n)})}unlisten(e,t){let n=e._path.toString(),i=e._queryIdentifier;this.log_("Unlisten called for "+n+" "+i),(0,g.vA)(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"unlisten() called for non-default but complete query"),this.removeListen_(n,i)&&this.connected_&&this.sendUnlisten_(n,i,e._queryObject,t)}sendUnlisten_(e,t,n,i){this.log_("Unlisten on "+e+" for "+t);let r={p:e};i&&(r.q=n,r.t=i),this.sendRequest("n",r)}onDisconnectPut(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("o",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"o",data:t,onComplete:n})}onDisconnectMerge(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("om",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"om",data:t,onComplete:n})}onDisconnectCancel(e,t){this.initConnection_(),this.connected_?this.sendOnDisconnect_("oc",e,null,t):this.onDisconnectRequestQueue_.push({pathString:e,action:"oc",data:null,onComplete:t})}sendOnDisconnect_(e,t,n,i){let r={p:t,d:n};this.log_("onDisconnect "+e,r),this.sendRequest(e,r,e=>{i&&setTimeout(()=>{i(e.s,e.d)},Math.floor(0))})}put(e,t,n,i){this.putInternal("p",e,t,n,i)}merge(e,t,n,i){this.putInternal("m",e,t,n,i)}putInternal(e,t,n,i,r){this.initConnection_();let s={p:t,d:n};void 0!==r&&(s.h=r),this.outstandingPuts_.push({action:e,request:s,onComplete:i}),this.outstandingPutCount_++;let o=this.outstandingPuts_.length-1;this.connected_?this.sendPut_(o):this.log_("Buffering put: "+t)}sendPut_(e){let t=this.outstandingPuts_[e].action,n=this.outstandingPuts_[e].request,i=this.outstandingPuts_[e].onComplete;this.outstandingPuts_[e].queued=this.connected_,this.sendRequest(t,n,n=>{this.log_(t+" response",n),delete this.outstandingPuts_[e],this.outstandingPutCount_--,0===this.outstandingPutCount_&&(this.outstandingPuts_=[]),i&&i(n.s,n.d)})}reportStats(e){if(this.connected_){let t={c:e};this.log_("reportStats",t),this.sendRequest("s",t,e=>{if("ok"!==e.s){let t=e.d;this.log_("reportStats","Error sending stats: "+t)}})}}onDataMessage_(e){if("r"in e){this.log_("from server: "+(0,g.As)(e));let t=e.r,n=this.requestCBHash_[t];n&&(delete this.requestCBHash_[t],n(e.b))}else if("error"in e)throw"A server-side error has occurred: "+e.error;else"a"in e&&this.onDataPush_(e.a,e.b)}onDataPush_(e,t){this.log_("handleServerMessage",e,t),"d"===e?this.onDataUpdate_(t.p,t.d,!1,t.t):"m"===e?this.onDataUpdate_(t.p,t.d,!0,t.t):"c"===e?this.onListenRevoked_(t.p,t.q):"ac"===e?this.onAuthRevoked_(t.s,t.d):"apc"===e?this.onAppCheckRevoked_(t.s,t.d):"sd"===e?this.onSecurityDebugPacket_(t):O("Unrecognized action received from server: "+(0,g.As)(e)+"\nAre you using the latest client?")}onReady_(e,t){this.log_("connection ready"),this.connected_=!0,this.lastConnectionEstablishedTime_=new Date().getTime(),this.handleTimestamp_(e),this.lastSessionId=t,this.firstConnection_&&this.sendConnectStats_(),this.restoreState_(),this.firstConnection_=!1,this.onConnectStatus_(!0)}scheduleConnect_(e){(0,g.vA)(!this.realtime_,"Scheduling a connect when we're already connected/ing?"),this.establishConnectionTimer_&&clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=setTimeout(()=>{this.establishConnectionTimer_=null,this.establishConnection_()},Math.floor(e))}initConnection_(){!this.realtime_&&this.firstConnection_&&this.scheduleConnect_(0)}onVisible_(e){e&&!this.visible_&&this.reconnectDelay_===this.maxReconnectDelay_&&(this.log_("Window became visible.  Reducing delay."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)),this.visible_=e}onOnline_(e){e?(this.log_("Browser went online."),this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0)):(this.log_("Browser went offline.  Killing connection."),this.realtime_&&this.realtime_.close())}onRealtimeDisconnect_(){if(this.log_("data client disconnected"),this.connected_=!1,this.realtime_=null,this.cancelSentTransactions_(),this.requestCBHash_={},this.shouldReconnect_()){this.visible_?this.lastConnectionEstablishedTime_&&(new Date().getTime()-this.lastConnectionEstablishedTime_>3e4&&(this.reconnectDelay_=1e3),this.lastConnectionEstablishedTime_=null):(this.log_("Window isn't visible.  Delaying reconnect."),this.reconnectDelay_=this.maxReconnectDelay_,this.lastConnectionAttemptTime_=new Date().getTime());let e=Math.max(0,new Date().getTime()-this.lastConnectionAttemptTime_),t=Math.max(0,this.reconnectDelay_-e);t=Math.random()*t,this.log_("Trying to reconnect in "+t+"ms"),this.scheduleConnect_(t),this.reconnectDelay_=Math.min(this.maxReconnectDelay_,1.3*this.reconnectDelay_)}this.onConnectStatus_(!1)}async establishConnection_(){if(this.shouldReconnect_()){this.log_("Making a connection attempt"),this.lastConnectionAttemptTime_=new Date().getTime(),this.lastConnectionEstablishedTime_=null;let e=this.onDataMessage_.bind(this),t=this.onReady_.bind(this),n=this.onRealtimeDisconnect_.bind(this),i=this.id+":"+ej.nextConnectionId_++,r=this.lastSessionId,s=!1,o=null,l=function(){o?o.close():(s=!0,n())};this.realtime_={close:l,sendRequest:function(e){(0,g.vA)(o,"sendRequest call when we're not connected not allowed."),o.sendRequest(e)}};let a=this.forceTokenRefresh_;this.forceTokenRefresh_=!1;try{let[l,h]=await Promise.all([this.authTokenProvider_.getToken(a),this.appCheckTokenProvider_.getToken(a)]);s?M("getToken() completed but was canceled"):(M("getToken() completed. Creating connection."),this.authToken_=l&&l.accessToken,this.appCheckToken_=h&&h.token,o=new eC(i,this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,e,t,n,e=>{q(e+" ("+this.repoInfo_.toString()+")"),this.interrupt("server_kill")},r))}catch(e){this.log_("Failed to get token: "+e),s||(this.repoInfo_.nodeAdmin&&q(e),l())}}}interrupt(e){M("Interrupting connection for reason: "+e),this.interruptReasons_[e]=!0,this.realtime_?this.realtime_.close():(this.establishConnectionTimer_&&(clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=null),this.connected_&&this.onRealtimeDisconnect_())}resume(e){M("Resuming connection for reason: "+e),delete this.interruptReasons_[e],(0,g.Im)(this.interruptReasons_)&&(this.reconnectDelay_=1e3,this.realtime_||this.scheduleConnect_(0))}handleTimestamp_(e){let t=e-new Date().getTime();this.onServerInfoUpdate_({serverTimeOffset:t})}cancelSentTransactions_(){for(let e=0;e<this.outstandingPuts_.length;e++){let t=this.outstandingPuts_[e];t&&"h"in t.request&&t.queued&&(t.onComplete&&t.onComplete("disconnect"),delete this.outstandingPuts_[e],this.outstandingPutCount_--)}0===this.outstandingPutCount_&&(this.outstandingPuts_=[])}onListenRevoked_(e,t){let n;n=t?t.map(e=>B(e)).join("$"):"default";let i=this.removeListen_(e,n);i&&i.onComplete&&i.onComplete("permission_denied")}removeListen_(e,t){let n,i=new ek(e).toString();if(this.listens.has(i)){let e=this.listens.get(i);n=e.get(t),e.delete(t),0===e.size&&this.listens.delete(i)}else n=void 0;return n}onAuthRevoked_(e,t){M("Auth token revoked: "+e+"/"+t),this.authToken_=null,this.forceTokenRefresh_=!0,this.realtime_.close(),("invalid_token"===e||"permission_denied"===e)&&(this.invalidAuthTokenCount_++,this.invalidAuthTokenCount_>=3&&(this.reconnectDelay_=3e4,this.authTokenProvider_.notifyForInvalidToken()))}onAppCheckRevoked_(e,t){M("App check token revoked: "+e+"/"+t),this.appCheckToken_=null,this.forceTokenRefresh_=!0,("invalid_token"===e||"permission_denied"===e)&&(this.invalidAppCheckTokenCount_++,this.invalidAppCheckTokenCount_>=3&&this.appCheckTokenProvider_.notifyForInvalidToken())}onSecurityDebugPacket_(e){this.securityDebugCallback_?this.securityDebugCallback_(e):"msg"in e&&console.log("FIREBASE: "+e.msg.replace("\n","\nFIREBASE: "))}restoreState_(){for(let e of(this.tryAuth(),this.tryAppCheck(),this.listens.values()))for(let t of e.values())this.sendListen_(t);for(let e=0;e<this.outstandingPuts_.length;e++)this.outstandingPuts_[e]&&this.sendPut_(e);for(;this.onDisconnectRequestQueue_.length;){let e=this.onDisconnectRequestQueue_.shift();this.sendOnDisconnect_(e.action,e.pathString,e.data,e.onComplete)}for(let e=0;e<this.outstandingGets_.length;e++)this.outstandingGets_[e]&&this.sendGet_(e)}sendConnectStats_(){let e={},t="js";(0,g.$g)()&&(t=this.repoInfo_.nodeAdmin?"admin_node":"node"),e["sdk."+t+"."+w.replace(/\./g,"-")]=1,(0,g.jZ)()?e["framework.cordova"]=1:(0,g.lV)()&&(e["framework.reactnative"]=1),this.reportStats(e)}shouldReconnect_(){let e=eI.getInstance().currentlyOnline();return(0,g.Im)(this.interruptReasons_)&&e}}ej.nextPersistentConnectionId_=0,ej.nextConnectionId_=0;class ez{constructor(e,t){this.name=e,this.node=t}static Wrap(e,t){return new ez(e,t)}}class eY{getCompare(){return this.compare.bind(this)}indexedValueChanged(e,t){let n=new ez(j,e),i=new ez(j,t);return 0!==this.compare(n,i)}minPost(){return ez.MIN}}class eK extends eY{static get __EMPTY_NODE(){return i}static set __EMPTY_NODE(e){i=e}compare(e,t){return Y(e.name,t.name)}isDefinedOn(e){throw(0,g.Hk)("KeyIndex.isDefinedOn not expected to be called.")}indexedValueChanged(e,t){return!1}minPost(){return ez.MIN}maxPost(){return new ez(z,i)}makePost(e,t){return(0,g.vA)("string"==typeof e,"KeyIndex indexValue must always be a string."),new ez(e,i)}toString(){return".key"}}let eV=new eK;class eB{constructor(e,t,n,i,r=null){this.isReverse_=i,this.resultGenerator_=r,this.nodeStack_=[];let s=1;for(;!e.isEmpty();)if(s=t?n(e.key,t):1,i&&(s*=-1),s<0)e=this.isReverse_?e.left:e.right;else if(0===s){this.nodeStack_.push(e);break}else this.nodeStack_.push(e),e=this.isReverse_?e.right:e.left}getNext(){let e;if(0===this.nodeStack_.length)return null;let t=this.nodeStack_.pop();if(e=this.resultGenerator_?this.resultGenerator_(t.key,t.value):{key:t.key,value:t.value},this.isReverse_)for(t=t.left;!t.isEmpty();)this.nodeStack_.push(t),t=t.right;else for(t=t.right;!t.isEmpty();)this.nodeStack_.push(t),t=t.left;return e}hasNext(){return this.nodeStack_.length>0}peek(){if(0===this.nodeStack_.length)return null;let e=this.nodeStack_[this.nodeStack_.length-1];return this.resultGenerator_?this.resultGenerator_(e.key,e.value):{key:e.key,value:e.value}}}class e${constructor(e,t,n,i,r){this.key=e,this.value=t,this.color=null!=n?n:e$.RED,this.left=null!=i?i:eG.EMPTY_NODE,this.right=null!=r?r:eG.EMPTY_NODE}copy(e,t,n,i,r){return new e$(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=i?i:this.left,null!=r?r:this.right)}count(){return this.left.count()+1+this.right.count()}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||!!e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min_(){return this.left.isEmpty()?this:this.left.min_()}minKey(){return this.min_().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let i=this,r=n(e,i.key);return(i=r<0?i.copy(null,null,null,i.left.insert(e,t,n),null):0===r?i.copy(null,t,null,null,null):i.copy(null,null,null,null,i.right.insert(e,t,n))).fixUp_()}removeMin_(){if(this.left.isEmpty())return eG.EMPTY_NODE;let e=this;return e.left.isRed_()||e.left.left.isRed_()||(e=e.moveRedLeft_()),(e=e.copy(null,null,null,e.left.removeMin_(),null)).fixUp_()}remove(e,t){let n,i;if(n=this,0>t(e,n.key))n.left.isEmpty()||n.left.isRed_()||n.left.left.isRed_()||(n=n.moveRedLeft_()),n=n.copy(null,null,null,n.left.remove(e,t),null);else{if(n.left.isRed_()&&(n=n.rotateRight_()),n.right.isEmpty()||n.right.isRed_()||n.right.left.isRed_()||(n=n.moveRedRight_()),0===t(e,n.key))if(n.right.isEmpty())return eG.EMPTY_NODE;else i=n.right.min_(),n=n.copy(i.key,i.value,null,null,n.right.removeMin_());n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp_()}isRed_(){return this.color}fixUp_(){let e=this;return e.right.isRed_()&&!e.left.isRed_()&&(e=e.rotateLeft_()),e.left.isRed_()&&e.left.left.isRed_()&&(e=e.rotateRight_()),e.left.isRed_()&&e.right.isRed_()&&(e=e.colorFlip_()),e}moveRedLeft_(){let e=this.colorFlip_();return e.right.left.isRed_()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight_())).rotateLeft_()).colorFlip_()),e}moveRedRight_(){let e=this.colorFlip_();return e.left.left.isRed_()&&(e=(e=e.rotateRight_()).colorFlip_()),e}rotateLeft_(){let e=this.copy(null,null,e$.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight_(){let e=this.copy(null,null,e$.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip_(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth_(){return Math.pow(2,this.check_())<=this.count()+1}check_(){if(this.isRed_()&&this.left.isRed_())throw Error("Red node has red child("+this.key+","+this.value+")");if(this.right.isRed_())throw Error("Right child of ("+this.key+","+this.value+") is red");let e=this.left.check_();if(e===this.right.check_())return e+ +!this.isRed_();throw Error("Black depths differ")}}e$.RED=!0,e$.BLACK=!1;class eQ{copy(e,t,n,i,r){return this}insert(e,t,n){return new e$(e,t,null)}remove(e,t){return this}count(){return 0}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}check_(){return 0}isRed_(){return!1}}class eG{constructor(e,t=eG.EMPTY_NODE){this.comparator_=e,this.root_=t}insert(e,t){return new eG(this.comparator_,this.root_.insert(e,t,this.comparator_).copy(null,null,e$.BLACK,null,null))}remove(e){return new eG(this.comparator_,this.root_.remove(e,this.comparator_).copy(null,null,e$.BLACK,null,null))}get(e){let t,n=this.root_;for(;!n.isEmpty();){if(0===(t=this.comparator_(e,n.key)))return n.value;t<0?n=n.left:t>0&&(n=n.right)}return null}getPredecessorKey(e){let t,n=this.root_,i=null;for(;!n.isEmpty();){if(0===(t=this.comparator_(e,n.key)))if(n.left.isEmpty())if(i)return i.key;else return null;else{for(n=n.left;!n.right.isEmpty();)n=n.right;return n.key}t<0?n=n.left:t>0&&(i=n,n=n.right)}throw Error("Attempted to find predecessor key for a nonexistent key.  What gives?")}isEmpty(){return this.root_.isEmpty()}count(){return this.root_.count()}minKey(){return this.root_.minKey()}maxKey(){return this.root_.maxKey()}inorderTraversal(e){return this.root_.inorderTraversal(e)}reverseTraversal(e){return this.root_.reverseTraversal(e)}getIterator(e){return new eB(this.root_,null,this.comparator_,!1,e)}getIteratorFrom(e,t){return new eB(this.root_,e,this.comparator_,!1,t)}getReverseIteratorFrom(e,t){return new eB(this.root_,e,this.comparator_,!0,t)}getReverseIterator(e){return new eB(this.root_,null,this.comparator_,!0,e)}}function eX(e,t){return Y(e.name,t.name)}function eJ(e,t){return Y(e,t)}eG.EMPTY_NODE=new eQ;let eZ=function(e){return"number"==typeof e?"number:"+G(e):"string:"+e},e0=function(e){if(e.isLeafNode()){let t=e.val();(0,g.vA)("string"==typeof t||"number"==typeof t||"object"==typeof t&&(0,g.gR)(t,".sv"),"Priority must be a string or number.")}else(0,g.vA)(e===r||e.isEmpty(),"priority of unexpected type.");(0,g.vA)(e===r||e.getPriority().isEmpty(),"Priority nodes can't have a priority of their own.")};class e1{static set __childrenNodeConstructor(e){s=e}static get __childrenNodeConstructor(){return s}constructor(e,t=e1.__childrenNodeConstructor.EMPTY_NODE){this.value_=e,this.priorityNode_=t,this.lazyHash_=null,(0,g.vA)(void 0!==this.value_&&null!==this.value_,"LeafNode shouldn't be created with null/undefined value."),e0(this.priorityNode_)}isLeafNode(){return!0}getPriority(){return this.priorityNode_}updatePriority(e){return new e1(this.value_,e)}getImmediateChild(e){return".priority"===e?this.priorityNode_:e1.__childrenNodeConstructor.EMPTY_NODE}getChild(e){return eD(e)?this:".priority"===eE(e)?this.priorityNode_:e1.__childrenNodeConstructor.EMPTY_NODE}hasChild(){return!1}getPredecessorChildName(e,t){return null}updateImmediateChild(e,t){return".priority"===e?this.updatePriority(t):t.isEmpty()&&".priority"!==e?this:e1.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(e,t).updatePriority(this.priorityNode_)}updateChild(e,t){let n=eE(e);return null===n?t:t.isEmpty()&&".priority"!==n?this:((0,g.vA)(".priority"!==n||1===eS(e),".priority must be the last token in a path"),this.updateImmediateChild(n,e1.__childrenNodeConstructor.EMPTY_NODE.updateChild(eP(e),t)))}isEmpty(){return!1}numChildren(){return 0}forEachChild(e,t){return!1}val(e){return e&&!this.getPriority().isEmpty()?{".value":this.getValue(),".priority":this.getPriority().val()}:this.getValue()}hash(){if(null===this.lazyHash_){let e="";this.priorityNode_.isEmpty()||(e+="priority:"+eZ(this.priorityNode_.val())+":");let t=typeof this.value_;e+=t+":","number"===t?e+=G(this.value_):e+=this.value_,this.lazyHash_=N(e)}return this.lazyHash_}getValue(){return this.value_}compareTo(e){return e===e1.__childrenNodeConstructor.EMPTY_NODE?1:e instanceof e1.__childrenNodeConstructor?-1:((0,g.vA)(e.isLeafNode(),"Unknown node type"),this.compareToLeafNode_(e))}compareToLeafNode_(e){let t=typeof e.value_,n=typeof this.value_,i=e1.VALUE_TYPE_ORDER.indexOf(t),r=e1.VALUE_TYPE_ORDER.indexOf(n);return((0,g.vA)(i>=0,"Unknown leaf type: "+t),(0,g.vA)(r>=0,"Unknown leaf type: "+n),i!==r)?r-i:"object"===n?0:this.value_<e.value_?-1:1*(this.value_!==e.value_)}withIndex(){return this}isIndexed(){return!0}equals(e){return e===this||!!e.isLeafNode()&&this.value_===e.value_&&this.priorityNode_.equals(e.priorityNode_)}}e1.VALUE_TYPE_ORDER=["object","boolean","number","string"];class e2 extends eY{compare(e,t){let n=e.node.getPriority(),i=t.node.getPriority(),r=n.compareTo(i);return 0===r?Y(e.name,t.name):r}isDefinedOn(e){return!e.getPriority().isEmpty()}indexedValueChanged(e,t){return!e.getPriority().equals(t.getPriority())}minPost(){return ez.MIN}maxPost(){return new ez(z,new e1("[PRIORITY-POST]",l))}makePost(e,t){return new ez(t,new e1("[PRIORITY-POST]",o(e)))}toString(){return".priority"}}let e3=new e2,e4=Math.log(2);class e5{constructor(e){this.count=parseInt(Math.log(e+1)/e4,10),this.current_=this.count-1;let t=parseInt(Array(this.count+1).join("1"),2);this.bits_=e+1&t}nextBitIsOne(){let e=!(this.bits_&1<<this.current_);return this.current_--,e}}let e8=function(e,t,n,i){e.sort(t);let r=function(t,i){let s,o=i-t;if(0===o)return null;if(1===o)return s=e[t],new e$(n?n(s):s,s.node,e$.BLACK,null,null);{let l=parseInt(o/2,10)+t,a=r(t,l),h=r(l+1,i);return s=e[l],new e$(n?n(s):s,s.node,e$.BLACK,a,h)}};return new eG(i||t,function(t){let i=null,s=null,o=e.length,l=function(t,i){let s=o-t,l=o;o-=t;let h=r(s+1,l),u=e[s];a(new e$(n?n(u):u,u.node,i,null,h))},a=function(e){i?i.left=e:s=e,i=e};for(let e=0;e<t.count;++e){let n=t.nextBitIsOne(),i=Math.pow(2,t.count-(e+1));n?l(i,e$.BLACK):(l(i,e$.BLACK),l(i,e$.RED))}return s}(new e5(e.length)))},e6={};class e7{static get Default(){return(0,g.vA)(e6&&e3,"ChildrenNode.ts has not been loaded"),a=a||new e7({".priority":e6},{".priority":e3})}constructor(e,t){this.indexes_=e,this.indexSet_=t}get(e){let t=(0,g.yw)(this.indexes_,e);if(!t)throw Error("No index defined for "+e);return t instanceof eG?t:null}hasIndex(e){return(0,g.gR)(this.indexSet_,e.toString())}addIndex(e,t){let n;(0,g.vA)(e!==eV,"KeyIndex always exists and isn't meant to be added to the IndexMap.");let i=[],r=!1,s=t.getIterator(ez.Wrap),o=s.getNext();for(;o;)r=r||e.isDefinedOn(o.node),i.push(o),o=s.getNext();n=r?e8(i,e.getCompare()):e6;let l=e.toString(),a={...this.indexSet_};a[l]=e;let h={...this.indexes_};return h[l]=n,new e7(h,a)}addToIndexes(e,t){return new e7((0,g.kH)(this.indexes_,(n,i)=>{let r=(0,g.yw)(this.indexSet_,i);if((0,g.vA)(r,"Missing index implementation for "+i),n===e6)if(!r.isDefinedOn(e.node))return e6;else{let n=[],i=t.getIterator(ez.Wrap),s=i.getNext();for(;s;)s.name!==e.name&&n.push(s),s=i.getNext();return n.push(e),e8(n,r.getCompare())}{let i=t.get(e.name),r=n;return i&&(r=r.remove(new ez(e.name,i))),r.insert(e,e.node)}}),this.indexSet_)}removeFromIndexes(e,t){return new e7((0,g.kH)(this.indexes_,n=>{if(n===e6)return n;{let i=t.get(e.name);return i?n.remove(new ez(e.name,i)):n}}),this.indexSet_)}}class e9{static get EMPTY_NODE(){return h||(h=new e9(new eG(eJ),null,e7.Default))}constructor(e,t,n){this.children_=e,this.priorityNode_=t,this.indexMap_=n,this.lazyHash_=null,this.priorityNode_&&e0(this.priorityNode_),this.children_.isEmpty()&&(0,g.vA)(!this.priorityNode_||this.priorityNode_.isEmpty(),"An empty node cannot have a priority")}isLeafNode(){return!1}getPriority(){return this.priorityNode_||h}updatePriority(e){return this.children_.isEmpty()?this:new e9(this.children_,e,this.indexMap_)}getImmediateChild(e){if(".priority"===e)return this.getPriority();{let t=this.children_.get(e);return null===t?h:t}}getChild(e){let t=eE(e);return null===t?this:this.getImmediateChild(t).getChild(eP(e))}hasChild(e){return null!==this.children_.get(e)}updateImmediateChild(e,t){if((0,g.vA)(t,"We should always be passing snapshot nodes"),".priority"===e)return this.updatePriority(t);{let n,i,r=new ez(e,t);t.isEmpty()?(n=this.children_.remove(e),i=this.indexMap_.removeFromIndexes(r,this.children_)):(n=this.children_.insert(e,t),i=this.indexMap_.addToIndexes(r,this.children_));let s=n.isEmpty()?h:this.priorityNode_;return new e9(n,s,i)}}updateChild(e,t){let n=eE(e);if(null===n)return t;{(0,g.vA)(".priority"!==eE(e)||1===eS(e),".priority must be the last token in a path");let i=this.getImmediateChild(n).updateChild(eP(e),t);return this.updateImmediateChild(n,i)}}isEmpty(){return this.children_.isEmpty()}numChildren(){return this.children_.count()}val(e){if(this.isEmpty())return null;let t={},n=0,i=0,r=!0;if(this.forEachChild(e3,(s,o)=>{t[s]=o.val(e),n++,r&&e9.INTEGER_REGEXP_.test(s)?i=Math.max(i,Number(s)):r=!1}),e||!r||!(i<2*n))return e&&!this.getPriority().isEmpty()&&(t[".priority"]=this.getPriority().val()),t;{let e=[];for(let n in t)e[n]=t[n];return e}}hash(){if(null===this.lazyHash_){let e="";this.getPriority().isEmpty()||(e+="priority:"+eZ(this.getPriority().val())+":"),this.forEachChild(e3,(t,n)=>{let i=n.hash();""!==i&&(e+=":"+t+":"+i)}),this.lazyHash_=""===e?"":N(e)}return this.lazyHash_}getPredecessorChildName(e,t,n){let i=this.resolveIndex_(n);if(!i)return this.children_.getPredecessorKey(e);{let n=i.getPredecessorKey(new ez(e,t));return n?n.name:null}}getFirstChildName(e){let t=this.resolveIndex_(e);if(!t)return this.children_.minKey();{let e=t.minKey();return e&&e.name}}getFirstChild(e){let t=this.getFirstChildName(e);return t?new ez(t,this.children_.get(t)):null}getLastChildName(e){let t=this.resolveIndex_(e);if(!t)return this.children_.maxKey();{let e=t.maxKey();return e&&e.name}}getLastChild(e){let t=this.getLastChildName(e);return t?new ez(t,this.children_.get(t)):null}forEachChild(e,t){let n=this.resolveIndex_(e);return n?n.inorderTraversal(e=>t(e.name,e.node)):this.children_.inorderTraversal(t)}getIterator(e){return this.getIteratorFrom(e.minPost(),e)}getIteratorFrom(e,t){let n=this.resolveIndex_(t);if(n)return n.getIteratorFrom(e,e=>e);{let n=this.children_.getIteratorFrom(e.name,ez.Wrap),i=n.peek();for(;null!=i&&0>t.compare(i,e);)n.getNext(),i=n.peek();return n}}getReverseIterator(e){return this.getReverseIteratorFrom(e.maxPost(),e)}getReverseIteratorFrom(e,t){let n=this.resolveIndex_(t);if(n)return n.getReverseIteratorFrom(e,e=>e);{let n=this.children_.getReverseIteratorFrom(e.name,ez.Wrap),i=n.peek();for(;null!=i&&t.compare(i,e)>0;)n.getNext(),i=n.peek();return n}}compareTo(e){if(this.isEmpty())if(e.isEmpty())return 0;else return -1;return e.isLeafNode()||e.isEmpty()?1:e===tt?-1:0}withIndex(e){if(e===eV||this.indexMap_.hasIndex(e))return this;{let t=this.indexMap_.addIndex(e,this.children_);return new e9(this.children_,this.priorityNode_,t)}}isIndexed(e){return e===eV||this.indexMap_.hasIndex(e)}equals(e){if(e===this)return!0;if(e.isLeafNode()||!this.getPriority().equals(e.getPriority()))return!1;if(this.children_.count()!==e.children_.count())return!1;{let t=this.getIterator(e3),n=e.getIterator(e3),i=t.getNext(),r=n.getNext();for(;i&&r;){if(i.name!==r.name||!i.node.equals(r.node))return!1;i=t.getNext(),r=n.getNext()}return null===i&&null===r}}resolveIndex_(e){return e===eV?null:this.indexMap_.get(e.toString())}}e9.INTEGER_REGEXP_=/^(0|[1-9]\d*)$/;class te extends e9{constructor(){super(new eG(eJ),e9.EMPTY_NODE,e7.Default)}compareTo(e){return+(e!==this)}equals(e){return e===this}getPriority(){return this}getImmediateChild(e){return e9.EMPTY_NODE}isEmpty(){return!1}}let tt=new te;function tn(e,t=null){if(null===e)return e9.EMPTY_NODE;if("object"==typeof e&&".priority"in e&&(t=e[".priority"]),(0,g.vA)(null===t||"string"==typeof t||"number"==typeof t||"object"==typeof t&&".sv"in t,"Invalid priority type found: "+typeof t),"object"==typeof e&&".value"in e&&null!==e[".value"]&&(e=e[".value"]),"object"!=typeof e||".sv"in e)return new e1(e,tn(t));if(e instanceof Array){let n=e9.EMPTY_NODE;return Q(e,(t,i)=>{if((0,g.gR)(e,t)&&"."!==t.substring(0,1)){let e=tn(i);(e.isLeafNode()||!e.isEmpty())&&(n=n.updateImmediateChild(t,e))}}),n.updatePriority(tn(t))}{let n=[],i=!1;if(Q(e,(e,t)=>{if("."!==e.substring(0,1)){let r=tn(t);r.isEmpty()||(i=i||!r.getPriority().isEmpty(),n.push(new ez(e,r)))}}),0===n.length)return e9.EMPTY_NODE;let r=e8(n,eX,e=>e.name,eJ);if(!i)return new e9(r,tn(t),e7.Default);{let e=e8(n,e3.getCompare());return new e9(r,tn(t),new e7({".priority":e},{".priority":e3}))}}}Object.defineProperties(ez,{MIN:{value:new ez(j,e9.EMPTY_NODE)},MAX:{value:new ez(z,tt)}}),eK.__EMPTY_NODE=e9.EMPTY_NODE,e1.__childrenNodeConstructor=e9,r=tt,l=tt,o=tn;class ti extends eY{constructor(e){super(),this.indexPath_=e,(0,g.vA)(!eD(e)&&".priority"!==eE(e),"Can't create PathIndex with empty path or .priority key")}extractChild(e){return e.getChild(this.indexPath_)}isDefinedOn(e){return!e.getChild(this.indexPath_).isEmpty()}compare(e,t){let n=this.extractChild(e.node),i=this.extractChild(t.node),r=n.compareTo(i);return 0===r?Y(e.name,t.name):r}makePost(e,t){let n=tn(e);return new ez(t,e9.EMPTY_NODE.updateChild(this.indexPath_,n))}maxPost(){return new ez(z,e9.EMPTY_NODE.updateChild(this.indexPath_,tt))}toString(){return ex(this.indexPath_,0).join("/")}}class tr extends eY{compare(e,t){let n=e.node.compareTo(t.node);return 0===n?Y(e.name,t.name):n}isDefinedOn(e){return!0}indexedValueChanged(e,t){return!e.equals(t)}minPost(){return ez.MIN}maxPost(){return ez.MAX}makePost(e,t){return new ez(t,tn(e))}toString(){return".value"}}let ts=new tr;function to(e){return{type:"value",snapshotNode:e}}function tl(e,t){return{type:"child_added",snapshotNode:t,childName:e}}function ta(e,t){return{type:"child_removed",snapshotNode:t,childName:e}}function th(e,t,n){return{type:"child_changed",snapshotNode:t,childName:e,oldSnap:n}}class tu{constructor(e){this.index_=e}updateChild(e,t,n,i,r,s){(0,g.vA)(e.isIndexed(this.index_),"A node must be indexed if only a child is updated");let o=e.getImmediateChild(t);return o.getChild(i).equals(n.getChild(i))&&o.isEmpty()===n.isEmpty()||(null!=s&&(n.isEmpty()?e.hasChild(t)?s.trackChildChange(ta(t,o)):(0,g.vA)(e.isLeafNode(),"A child remove without an old child only makes sense on a leaf node"):o.isEmpty()?s.trackChildChange(tl(t,n)):s.trackChildChange(th(t,n,o))),e.isLeafNode()&&n.isEmpty())?e:e.updateImmediateChild(t,n).withIndex(this.index_)}updateFullNode(e,t,n){return null!=n&&(e.isLeafNode()||e.forEachChild(e3,(e,i)=>{t.hasChild(e)||n.trackChildChange(ta(e,i))}),t.isLeafNode()||t.forEachChild(e3,(t,i)=>{if(e.hasChild(t)){let r=e.getImmediateChild(t);r.equals(i)||n.trackChildChange(th(t,i,r))}else n.trackChildChange(tl(t,i))})),t.withIndex(this.index_)}updatePriority(e,t){return e.isEmpty()?e9.EMPTY_NODE:e.updatePriority(t)}filtersNodes(){return!1}getIndexedFilter(){return this}getIndex(){return this.index_}}class tc{constructor(e){this.indexedFilter_=new tu(e.getIndex()),this.index_=e.getIndex(),this.startPost_=tc.getStartPost_(e),this.endPost_=tc.getEndPost_(e),this.startIsInclusive_=!e.startAfterSet_,this.endIsInclusive_=!e.endBeforeSet_}getStartPost(){return this.startPost_}getEndPost(){return this.endPost_}matches(e){let t=this.startIsInclusive_?0>=this.index_.compare(this.getStartPost(),e):0>this.index_.compare(this.getStartPost(),e),n=this.endIsInclusive_?0>=this.index_.compare(e,this.getEndPost()):0>this.index_.compare(e,this.getEndPost());return t&&n}updateChild(e,t,n,i,r,s){return this.matches(new ez(t,n))||(n=e9.EMPTY_NODE),this.indexedFilter_.updateChild(e,t,n,i,r,s)}updateFullNode(e,t,n){t.isLeafNode()&&(t=e9.EMPTY_NODE);let i=t.withIndex(this.index_);i=i.updatePriority(e9.EMPTY_NODE);let r=this;return t.forEachChild(e3,(e,t)=>{r.matches(new ez(e,t))||(i=i.updateImmediateChild(e,e9.EMPTY_NODE))}),this.indexedFilter_.updateFullNode(e,i,n)}updatePriority(e,t){return e}filtersNodes(){return!0}getIndexedFilter(){return this.indexedFilter_}getIndex(){return this.index_}static getStartPost_(e){if(!e.hasStart())return e.getIndex().minPost();{let t=e.getIndexStartName();return e.getIndex().makePost(e.getIndexStartValue(),t)}}static getEndPost_(e){if(!e.hasEnd())return e.getIndex().maxPost();{let t=e.getIndexEndName();return e.getIndex().makePost(e.getIndexEndValue(),t)}}}class td{constructor(e){this.withinDirectionalStart=e=>this.reverse_?this.withinEndPost(e):this.withinStartPost(e),this.withinDirectionalEnd=e=>this.reverse_?this.withinStartPost(e):this.withinEndPost(e),this.withinStartPost=e=>{let t=this.index_.compare(this.rangedFilter_.getStartPost(),e);return this.startIsInclusive_?t<=0:t<0},this.withinEndPost=e=>{let t=this.index_.compare(e,this.rangedFilter_.getEndPost());return this.endIsInclusive_?t<=0:t<0},this.rangedFilter_=new tc(e),this.index_=e.getIndex(),this.limit_=e.getLimit(),this.reverse_=!e.isViewFromLeft(),this.startIsInclusive_=!e.startAfterSet_,this.endIsInclusive_=!e.endBeforeSet_}updateChild(e,t,n,i,r,s){return(this.rangedFilter_.matches(new ez(t,n))||(n=e9.EMPTY_NODE),e.getImmediateChild(t).equals(n))?e:e.numChildren()<this.limit_?this.rangedFilter_.getIndexedFilter().updateChild(e,t,n,i,r,s):this.fullLimitUpdateChild_(e,t,n,r,s)}updateFullNode(e,t,n){let i;if(t.isLeafNode()||t.isEmpty())i=e9.EMPTY_NODE.withIndex(this.index_);else if(2*this.limit_<t.numChildren()&&t.isIndexed(this.index_)){let e;i=e9.EMPTY_NODE.withIndex(this.index_),e=this.reverse_?t.getReverseIteratorFrom(this.rangedFilter_.getEndPost(),this.index_):t.getIteratorFrom(this.rangedFilter_.getStartPost(),this.index_);let n=0;for(;e.hasNext()&&n<this.limit_;){let t=e.getNext();if(this.withinDirectionalStart(t))if(this.withinDirectionalEnd(t))i=i.updateImmediateChild(t.name,t.node),n++;else break}}else{let e;i=(i=t.withIndex(this.index_)).updatePriority(e9.EMPTY_NODE),e=this.reverse_?i.getReverseIterator(this.index_):i.getIterator(this.index_);let n=0;for(;e.hasNext();){let t=e.getNext();n<this.limit_&&this.withinDirectionalStart(t)&&this.withinDirectionalEnd(t)?n++:i=i.updateImmediateChild(t.name,e9.EMPTY_NODE)}}return this.rangedFilter_.getIndexedFilter().updateFullNode(e,i,n)}updatePriority(e,t){return e}filtersNodes(){return!0}getIndexedFilter(){return this.rangedFilter_.getIndexedFilter()}getIndex(){return this.index_}fullLimitUpdateChild_(e,t,n,i,r){let s;if(this.reverse_){let e=this.index_.getCompare();s=(t,n)=>e(n,t)}else s=this.index_.getCompare();(0,g.vA)(e.numChildren()===this.limit_,"");let o=new ez(t,n),l=this.reverse_?e.getFirstChild(this.index_):e.getLastChild(this.index_),a=this.rangedFilter_.matches(o);if(e.hasChild(t)){let h=e.getImmediateChild(t),u=i.getChildAfterChild(this.index_,l,this.reverse_);for(;null!=u&&(u.name===t||e.hasChild(u.name));)u=i.getChildAfterChild(this.index_,u,this.reverse_);let c=null==u?1:s(u,o);if(a&&!n.isEmpty()&&c>=0)return null!=r&&r.trackChildChange(th(t,n,h)),e.updateImmediateChild(t,n);{null!=r&&r.trackChildChange(ta(t,h));let n=e.updateImmediateChild(t,e9.EMPTY_NODE);return null!=u&&this.rangedFilter_.matches(u)?(null!=r&&r.trackChildChange(tl(u.name,u.node)),n.updateImmediateChild(u.name,u.node)):n}}return n.isEmpty()?e:a?s(l,o)>=0?(null!=r&&(r.trackChildChange(ta(l.name,l.node)),r.trackChildChange(tl(t,n))),e.updateImmediateChild(t,n).updateImmediateChild(l.name,e9.EMPTY_NODE)):e:e}}class t_{constructor(){this.limitSet_=!1,this.startSet_=!1,this.startNameSet_=!1,this.startAfterSet_=!1,this.endSet_=!1,this.endNameSet_=!1,this.endBeforeSet_=!1,this.limit_=0,this.viewFrom_="",this.indexStartValue_=null,this.indexStartName_="",this.indexEndValue_=null,this.indexEndName_="",this.index_=e3}hasStart(){return this.startSet_}isViewFromLeft(){return""===this.viewFrom_?this.startSet_:"l"===this.viewFrom_}getIndexStartValue(){return(0,g.vA)(this.startSet_,"Only valid if start has been set"),this.indexStartValue_}getIndexStartName(){return((0,g.vA)(this.startSet_,"Only valid if start has been set"),this.startNameSet_)?this.indexStartName_:j}hasEnd(){return this.endSet_}getIndexEndValue(){return(0,g.vA)(this.endSet_,"Only valid if end has been set"),this.indexEndValue_}getIndexEndName(){return((0,g.vA)(this.endSet_,"Only valid if end has been set"),this.endNameSet_)?this.indexEndName_:z}hasLimit(){return this.limitSet_}hasAnchoredLimit(){return this.limitSet_&&""!==this.viewFrom_}getLimit(){return(0,g.vA)(this.limitSet_,"Only valid if limit has been set"),this.limit_}getIndex(){return this.index_}loadsAllData(){return!(this.startSet_||this.endSet_||this.limitSet_)}isDefault(){return this.loadsAllData()&&this.index_===e3}copy(){let e=new t_;return e.limitSet_=this.limitSet_,e.limit_=this.limit_,e.startSet_=this.startSet_,e.startAfterSet_=this.startAfterSet_,e.indexStartValue_=this.indexStartValue_,e.startNameSet_=this.startNameSet_,e.indexStartName_=this.indexStartName_,e.endSet_=this.endSet_,e.endBeforeSet_=this.endBeforeSet_,e.indexEndValue_=this.indexEndValue_,e.endNameSet_=this.endNameSet_,e.indexEndName_=this.indexEndName_,e.index_=this.index_,e.viewFrom_=this.viewFrom_,e}}function tp(e){let t,n={};if(e.isDefault())return n;if(e.index_===e3?t="$priority":e.index_===ts?t="$value":e.index_===eV?t="$key":((0,g.vA)(e.index_ instanceof ti,"Unrecognized index type!"),t=e.index_.toString()),n.orderBy=(0,g.As)(t),e.startSet_){let t=e.startAfterSet_?"startAfter":"startAt";n[t]=(0,g.As)(e.indexStartValue_),e.startNameSet_&&(n[t]+=","+(0,g.As)(e.indexStartName_))}if(e.endSet_){let t=e.endBeforeSet_?"endBefore":"endAt";n[t]=(0,g.As)(e.indexEndValue_),e.endNameSet_&&(n[t]+=","+(0,g.As)(e.indexEndName_))}return e.limitSet_&&(e.isViewFromLeft()?n.limitToFirst=e.limit_:n.limitToLast=e.limit_),n}function tf(e){let t={};if(e.startSet_&&(t.sp=e.indexStartValue_,e.startNameSet_&&(t.sn=e.indexStartName_),t.sin=!e.startAfterSet_),e.endSet_&&(t.ep=e.indexEndValue_,e.endNameSet_&&(t.en=e.indexEndName_),t.ein=!e.endBeforeSet_),e.limitSet_){t.l=e.limit_;let n=e.viewFrom_;""===n&&(n=e.isViewFromLeft()?"l":"r"),t.vf=n}return e.index_!==e3&&(t.i=e.index_.toString()),t}class tg extends ew{reportStats(e){throw Error("Method not implemented.")}static getListenId_(e,t){return void 0!==t?"tag$"+t:((0,g.vA)(e._queryParams.isDefault(),"should have a tag if it's not a default query."),e._path.toString())}constructor(e,t,n,i){super(),this.repoInfo_=e,this.onDataUpdate_=t,this.authTokenProvider_=n,this.appCheckTokenProvider_=i,this.log_=F("p:rest:"),this.listens_={}}listen(e,t,n,i){let r=e._path.toString();this.log_("Listen called for "+r+" "+e._queryIdentifier);let s=tg.getListenId_(e,n),o={};this.listens_[s]=o;let l=tp(e._queryParams);this.restRequest_(r+".json",l,(e,t)=>{let l=t;(404===e&&(l=null,e=null),null===e&&this.onDataUpdate_(r,l,!1,n),(0,g.yw)(this.listens_,s)===o)&&i(e?401===e?"permission_denied":"rest_error:"+e:"ok",null)})}unlisten(e,t){let n=tg.getListenId_(e,t);delete this.listens_[n]}get(e){let t=tp(e._queryParams),n=e._path.toString(),i=new g.cY;return this.restRequest_(n+".json",t,(e,t)=>{let r=t;404===e&&(r=null,e=null),null===e?(this.onDataUpdate_(n,r,!1,null),i.resolve(r)):i.reject(Error(r))}),i.promise}refreshAuthToken(e){}restRequest_(e,t={},n){return t.format="export",Promise.all([this.authTokenProvider_.getToken(!1),this.appCheckTokenProvider_.getToken(!1)]).then(([i,r])=>{i&&i.accessToken&&(t.auth=i.accessToken),r&&r.token&&(t.ac=r.token);let s=(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host+e+"?ns="+this.repoInfo_.namespace+(0,g.Am)(t);this.log_("Sending REST request for "+s);let o=new XMLHttpRequest;o.onreadystatechange=()=>{if(n&&4===o.readyState){this.log_("REST Response for "+s+" received. status:",o.status,"response:",o.responseText);let e=null;if(o.status>=200&&o.status<300){try{e=(0,g.$L)(o.responseText)}catch(e){q("Failed to parse JSON response for "+s+": "+o.responseText)}n(null,e)}else 401!==o.status&&404!==o.status&&q("Got unsuccessful REST response for "+s+" Status: "+o.status),n(o.status);n=null}},o.open("GET",s,!0),o.send()})}}class tm{constructor(){this.rootNode_=e9.EMPTY_NODE}getNode(e){return this.rootNode_.getChild(e)}updateSnapshot(e,t){this.rootNode_=this.rootNode_.updateChild(e,t)}}function ty(){return{value:null,children:new Map}}function tv(e,t,n){var i,r;null!==e.value?n(t,e.value):(i=e,r=(e,i)=>{tv(i,new ek(t.toString()+"/"+e),n)},i.children.forEach((e,t)=>{r(t,e)}))}class tC{constructor(e){this.collection_=e,this.last_=null}get(){let e=this.collection_.get(),t={...e};return this.last_&&Q(this.last_,(e,n)=>{t[e]=t[e]-n}),this.last_=e,t}}class tw{constructor(e,t){this.server_=t,this.statsToReport_={},this.statsListener_=new tC(e);let n=1e4+2e4*Math.random();ee(this.reportStats_.bind(this),Math.floor(n))}reportStats_(){let e=this.statsListener_.get(),t={},n=!1;Q(e,(e,i)=>{i>0&&(0,g.gR)(this.statsToReport_,e)&&(t[e]=i,n=!0)}),n&&this.server_.reportStats(t),ee(this.reportStats_.bind(this),Math.floor(2*Math.random()*3e5))}}function tT(){return{fromUser:!0,fromServer:!1,queryId:null,tagged:!1}}function tI(){return{fromUser:!1,fromServer:!0,queryId:null,tagged:!1}}function tk(e){return{fromUser:!1,fromServer:!0,queryId:e,tagged:!0}}!function(e){e[e.OVERWRITE=0]="OVERWRITE",e[e.MERGE=1]="MERGE",e[e.ACK_USER_WRITE=2]="ACK_USER_WRITE",e[e.LISTEN_COMPLETE=3]="LISTEN_COMPLETE"}(_||(_={}));class tb{constructor(e,t,n){this.path=e,this.affectedTree=t,this.revert=n,this.type=_.ACK_USER_WRITE,this.source=tT()}operationForChild(e){if(!eD(this.path))return(0,g.vA)(eE(this.path)===e,"operationForChild called for unrelated child."),new tb(eP(this.path),this.affectedTree,this.revert);if(null!=this.affectedTree.value)return(0,g.vA)(this.affectedTree.children.isEmpty(),"affectedTree should not have overlapping affected paths."),this;{let t=this.affectedTree.subtree(new ek(e));return new tb(eb(),t,this.revert)}}}class tE{constructor(e,t){this.source=e,this.path=t,this.type=_.LISTEN_COMPLETE}operationForChild(e){return eD(this.path)?new tE(this.source,eb()):new tE(this.source,eP(this.path))}}class tS{constructor(e,t,n){this.source=e,this.path=t,this.snap=n,this.type=_.OVERWRITE}operationForChild(e){return eD(this.path)?new tS(this.source,eb(),this.snap.getImmediateChild(e)):new tS(this.source,eP(this.path),this.snap)}}class tP{constructor(e,t,n){this.source=e,this.path=t,this.children=n,this.type=_.MERGE}operationForChild(e){if(!eD(this.path))return(0,g.vA)(eE(this.path)===e,"Can't get a merge for a child not on the path of the operation"),new tP(this.source,eP(this.path),this.children);{let t=this.children.subtree(new ek(e));return t.isEmpty()?null:t.value?new tS(this.source,eb(),t.value):new tP(this.source,eb(),t)}}toString(){return"Operation("+this.path+": "+this.source.toString()+" merge: "+this.children.toString()+")"}}class tN{constructor(e,t,n){this.node_=e,this.fullyInitialized_=t,this.filtered_=n}isFullyInitialized(){return this.fullyInitialized_}isFiltered(){return this.filtered_}isCompleteForPath(e){if(eD(e))return this.isFullyInitialized()&&!this.filtered_;let t=eE(e);return this.isCompleteForChild(t)}isCompleteForChild(e){return this.isFullyInitialized()&&!this.filtered_||this.node_.hasChild(e)}getNode(){return this.node_}}class tx{constructor(e){this.query_=e,this.index_=this.query_._queryParams.getIndex()}}function tR(e,t,n,i,r,s){let o=i.filter(e=>e.type===n);o.sort((t,n)=>(function(e,t,n){if(null==t.childName||null==n.childName)throw(0,g.Hk)("Should only compare child_ events.");let i=new ez(t.childName,t.snapshotNode),r=new ez(n.childName,n.snapshotNode);return e.index_.compare(i,r)})(e,t,n)),o.forEach(n=>{var i,o,l;let a=(i=e,o=n,l=s,"value"===o.type||"child_removed"===o.type||(o.prevName=l.getPredecessorChildName(o.childName,o.snapshotNode,i.index_)),o);r.forEach(i=>{i.respondsTo(n.type)&&t.push(i.createEvent(a,e.query_))})})}function tA(e,t){return{eventCache:e,serverCache:t}}function tD(e,t,n,i){return tA(new tN(t,n,i),e.serverCache)}function tM(e,t,n,i){return tA(e.eventCache,new tN(t,n,i))}function tF(e){return e.eventCache.isFullyInitialized()?e.eventCache.getNode():null}function tO(e){return e.serverCache.isFullyInitialized()?e.serverCache.getNode():null}class tL{static fromObject(e){let t=new tL(null);return Q(e,(e,n)=>{t=t.set(new ek(e),n)}),t}constructor(e,t=(!u&&(u=new eG(K)),u)){this.value=e,this.children=t}isEmpty(){return null===this.value&&this.children.isEmpty()}findRootMostMatchingPathAndValue(e,t){if(null!=this.value&&t(this.value))return{path:eb(),value:this.value};{if(eD(e))return null;let n=eE(e),i=this.children.get(n);if(null===i)return null;{let r=i.findRootMostMatchingPathAndValue(eP(e),t);return null!=r?{path:eA(new ek(n),r.path),value:r.value}:null}}}findRootMostValueAndPath(e){return this.findRootMostMatchingPathAndValue(e,()=>!0)}subtree(e){if(eD(e))return this;{let t=eE(e),n=this.children.get(t);return null!==n?n.subtree(eP(e)):new tL(null)}}set(e,t){if(eD(e))return new tL(t,this.children);{let n=eE(e),i=(this.children.get(n)||new tL(null)).set(eP(e),t),r=this.children.insert(n,i);return new tL(this.value,r)}}remove(e){if(eD(e))if(this.children.isEmpty())return new tL(null);else return new tL(null,this.children);{let t=eE(e),n=this.children.get(t);if(!n)return this;{let i,r=n.remove(eP(e));return(i=r.isEmpty()?this.children.remove(t):this.children.insert(t,r),null===this.value&&i.isEmpty())?new tL(null):new tL(this.value,i)}}}get(e){if(eD(e))return this.value;{let t=eE(e),n=this.children.get(t);return n?n.get(eP(e)):null}}setTree(e,t){if(eD(e))return t;{let n,i=eE(e),r=(this.children.get(i)||new tL(null)).setTree(eP(e),t);return n=r.isEmpty()?this.children.remove(i):this.children.insert(i,r),new tL(this.value,n)}}fold(e){return this.fold_(eb(),e)}fold_(e,t){let n={};return this.children.inorderTraversal((i,r)=>{n[i]=r.fold_(eA(e,i),t)}),t(e,this.value,n)}findOnPath(e,t){return this.findOnPath_(e,eb(),t)}findOnPath_(e,t,n){let i=!!this.value&&n(t,this.value);if(i)return i;{if(eD(e))return null;let i=eE(e),r=this.children.get(i);return r?r.findOnPath_(eP(e),eA(t,i),n):null}}foreachOnPath(e,t){return this.foreachOnPath_(e,eb(),t)}foreachOnPath_(e,t,n){if(eD(e))return this;{this.value&&n(t,this.value);let i=eE(e),r=this.children.get(i);return r?r.foreachOnPath_(eP(e),eA(t,i),n):new tL(null)}}foreach(e){this.foreach_(eb(),e)}foreach_(e,t){this.children.inorderTraversal((n,i)=>{i.foreach_(eA(e,n),t)}),this.value&&t(e,this.value)}foreachChild(e){this.children.inorderTraversal((t,n)=>{n.value&&e(t,n.value)})}}class tq{constructor(e){this.writeTree_=e}static empty(){return new tq(new tL(null))}}function tW(e,t,n){if(eD(t))return new tq(new tL(n));{let i=e.writeTree_.findRootMostValueAndPath(t);if(null!=i){let r=i.path,s=i.value,o=eM(r,t);return s=s.updateChild(o,n),new tq(e.writeTree_.set(r,s))}{let i=new tL(n);return new tq(e.writeTree_.setTree(t,i))}}}function tU(e,t,n){let i=e;return Q(n,(e,n)=>{i=tW(i,eA(t,e),n)}),i}function tH(e,t){return eD(t)?tq.empty():new tq(e.writeTree_.setTree(t,new tL(null)))}function tj(e,t){return null!=tz(e,t)}function tz(e,t){let n=e.writeTree_.findRootMostValueAndPath(t);return null!=n?e.writeTree_.get(n.path).getChild(eM(n.path,t)):null}function tY(e){let t=[],n=e.writeTree_.value;return null!=n?n.isLeafNode()||n.forEachChild(e3,(e,n)=>{t.push(new ez(e,n))}):e.writeTree_.children.inorderTraversal((e,n)=>{null!=n.value&&t.push(new ez(e,n.value))}),t}function tK(e,t){if(eD(t))return e;{let n=tz(e,t);return new tq(null!=n?new tL(n):e.writeTree_.subtree(t))}}function tV(e){return e.writeTree_.isEmpty()}function tB(e,t){return function e(t,n,i){if(null!=n.value)return i.updateChild(t,n.value);{let r=null;return n.children.inorderTraversal((n,s)=>{".priority"===n?((0,g.vA)(null!==s.value,"Priority writes must always be leaf nodes"),r=s.value):i=e(eA(t,n),s,i)}),i.getChild(t).isEmpty()||null===r||(i=i.updateChild(eA(t,".priority"),r)),i}}(eb(),e.writeTree_,t)}function t$(e){return e.visible}function tQ(e,t,n){let i=tq.empty();for(let r=0;r<e.length;++r){let s=e[r];if(t(s)){let e,t=s.path;if(s.snap)eL(n,t)?i=tW(i,e=eM(n,t),s.snap):eL(t,n)&&(e=eM(t,n),i=tW(i,eb(),s.snap.getChild(e)));else if(s.children){if(eL(n,t))i=tU(i,e=eM(n,t),s.children);else if(eL(t,n))if(eD(e=eM(t,n)))i=tU(i,eb(),s.children);else{let t=(0,g.yw)(s.children,eE(e));if(t){let n=t.getChild(eP(e));i=tW(i,eb(),n)}}}else throw(0,g.Hk)("WriteRecord should have .snap or .children")}}return i}function tG(e,t,n,i,r){if(i||r){let s=tK(e.visibleWrites,t);return!r&&tV(s)?n:r||null!=n||tj(s,eb())?tB(tQ(e.allWrites,function(e){return(e.visible||r)&&(!i||!~i.indexOf(e.writeId))&&(eL(e.path,t)||eL(t,e.path))},t),n||e9.EMPTY_NODE):null}{let i=tz(e.visibleWrites,t);if(null!=i)return i;{let i=tK(e.visibleWrites,t);return tV(i)?n:null!=n||tj(i,eb())?tB(i,n||e9.EMPTY_NODE):null}}}function tX(e,t,n,i){return tG(e.writeTree,e.treePath,t,n,i)}function tJ(e,t){return function(e,t,n){let i=e9.EMPTY_NODE,r=tz(e.visibleWrites,t);if(r)return r.isLeafNode()||r.forEachChild(e3,(e,t)=>{i=i.updateImmediateChild(e,t)}),i;if(!n)return tY(tK(e.visibleWrites,t)).forEach(e=>{i=i.updateImmediateChild(e.name,e.node)}),i;{let r=tK(e.visibleWrites,t);return n.forEachChild(e3,(e,t)=>{let n=tB(tK(r,new ek(e)),t);i=i.updateImmediateChild(e,n)}),tY(r).forEach(e=>{i=i.updateImmediateChild(e.name,e.node)}),i}}(e.writeTree,e.treePath,t)}function tZ(e,t,n,i){return function(e,t,n,i,r){(0,g.vA)(i||r,"Either existingEventSnap or existingServerSnap must exist");let s=eA(t,n);if(tj(e.visibleWrites,s))return null;{let t=tK(e.visibleWrites,s);return tV(t)?r.getChild(n):tB(t,r.getChild(n))}}(e.writeTree,e.treePath,t,n,i)}function t0(e,t){var n,i;return n=e.writeTree,i=eA(e.treePath,t),tz(n.visibleWrites,i)}function t1(e,t,n){return function(e,t,n,i){let r=eA(t,n),s=tz(e.visibleWrites,r);return null!=s?s:i.isCompleteForChild(n)?tB(tK(e.visibleWrites,r),i.getNode().getImmediateChild(n)):null}(e.writeTree,e.treePath,t,n)}function t2(e,t){return t3(eA(e.treePath,t),e.writeTree)}function t3(e,t){return{treePath:e,writeTree:t}}class t4{constructor(){this.changeMap=new Map}trackChildChange(e){let t=e.type,n=e.childName;(0,g.vA)("child_added"===t||"child_changed"===t||"child_removed"===t,"Only child changes supported for tracking"),(0,g.vA)(".priority"!==n,"Only non-priority child changes can be tracked.");let i=this.changeMap.get(n);if(i){let r=i.type;if("child_added"===t&&"child_removed"===r)this.changeMap.set(n,th(n,e.snapshotNode,i.snapshotNode));else if("child_removed"===t&&"child_added"===r)this.changeMap.delete(n);else if("child_removed"===t&&"child_changed"===r)this.changeMap.set(n,ta(n,i.oldSnap));else if("child_changed"===t&&"child_added"===r)this.changeMap.set(n,tl(n,e.snapshotNode));else if("child_changed"===t&&"child_changed"===r)this.changeMap.set(n,th(n,e.snapshotNode,i.oldSnap));else throw(0,g.Hk)("Illegal combination of changes: "+e+" occurred after "+i)}else this.changeMap.set(n,e)}getChanges(){return Array.from(this.changeMap.values())}}class t5{getCompleteChild(e){return null}getChildAfterChild(e,t,n){return null}}let t8=new t5;class t6{constructor(e,t,n=null){this.writes_=e,this.viewCache_=t,this.optCompleteServerCache_=n}getCompleteChild(e){let t=this.viewCache_.eventCache;if(t.isCompleteForChild(e))return t.getNode().getImmediateChild(e);{let t=null!=this.optCompleteServerCache_?new tN(this.optCompleteServerCache_,!0,!1):this.viewCache_.serverCache;return t1(this.writes_,e,t)}}getChildAfterChild(e,t,n){var i;let r=null!=this.optCompleteServerCache_?this.optCompleteServerCache_:tO(this.viewCache_),s=(i=this.writes_,function(e,t,n,i,r,s,o){let l,a=tK(e.visibleWrites,t),h=tz(a,eb());if(null!=h)l=h;else{if(null==n)return[];l=tB(a,n)}if((l=l.withIndex(o)).isEmpty()||l.isLeafNode())return[];{let e=[],t=o.getCompare(),n=s?l.getReverseIteratorFrom(i,o):l.getIteratorFrom(i,o),r=n.getNext();for(;r&&e.length<1;)0!==t(r,i)&&e.push(r),r=n.getNext();return e}}(i.writeTree,i.treePath,r,t,1,n,e));return 0===s.length?null:s[0]}}function t7(e,t,n,i,r,s){let o=t.eventCache;if(null!=t0(i,n))return t;{let l,a;if(eD(n))if((0,g.vA)(t.serverCache.isFullyInitialized(),"If change path is empty, we must have complete server data"),t.serverCache.isFiltered()){let n=tO(t),r=tJ(i,n instanceof e9?n:e9.EMPTY_NODE);l=e.filter.updateFullNode(t.eventCache.getNode(),r,s)}else{let n=tX(i,tO(t));l=e.filter.updateFullNode(t.eventCache.getNode(),n,s)}else{let h=eE(n);if(".priority"===h){(0,g.vA)(1===eS(n),"Can't have a priority with additional path components");let r=o.getNode(),s=tZ(i,n,r,a=t.serverCache.getNode());l=null!=s?e.filter.updatePriority(r,s):o.getNode()}else{let u,c=eP(n);if(o.isCompleteForChild(h)){a=t.serverCache.getNode();let e=tZ(i,n,o.getNode(),a);u=null!=e?o.getNode().getImmediateChild(h).updateChild(c,e):o.getNode().getImmediateChild(h)}else u=t1(i,h,t.serverCache);l=null!=u?e.filter.updateChild(o.getNode(),h,u,c,r,s):o.getNode()}}return tD(t,l,o.isFullyInitialized()||eD(n),e.filter.filtersNodes())}}function t9(e,t,n,i,r,s,o,l){let a,h=t.serverCache,u=o?e.filter:e.filter.getIndexedFilter();if(eD(n))a=u.updateFullNode(h.getNode(),i,null);else if(u.filtersNodes()&&!h.isFiltered()){let e=h.getNode().updateChild(n,i);a=u.updateFullNode(h.getNode(),e,null)}else{let e=eE(n);if(!h.isCompleteForPath(n)&&eS(n)>1)return t;let r=eP(n),s=h.getNode().getImmediateChild(e).updateChild(r,i);a=".priority"===e?u.updatePriority(h.getNode(),s):u.updateChild(h.getNode(),e,s,r,t8,null)}let c=tM(t,a,h.isFullyInitialized()||eD(n),u.filtersNodes()),d=new t6(r,c,s);return t7(e,c,n,r,d,l)}function ne(e,t,n,i,r,s,o){let l,a,h=t.eventCache,u=new t6(r,t,s);if(eD(n))a=e.filter.updateFullNode(t.eventCache.getNode(),i,o),l=tD(t,a,!0,e.filter.filtersNodes());else{let r=eE(n);if(".priority"===r)a=e.filter.updatePriority(t.eventCache.getNode(),i),l=tD(t,a,h.isFullyInitialized(),h.isFiltered());else{let s,a=eP(n),c=h.getNode().getImmediateChild(r);if(eD(a))s=i;else{let e=u.getCompleteChild(r);s=null!=e?".priority"===eN(a)&&e.getChild(eR(a)).isEmpty()?e:e.updateChild(a,i):e9.EMPTY_NODE}l=c.equals(s)?t:tD(t,e.filter.updateChild(h.getNode(),r,s,a,u,o),h.isFullyInitialized(),e.filter.filtersNodes())}}return l}function nt(e,t){return e.eventCache.isCompleteForChild(t)}function nn(e,t,n){return n.foreach((e,n)=>{t=t.updateChild(e,n)}),t}function ni(e,t,n,i,r,s,o,l){let a;if(t.serverCache.getNode().isEmpty()&&!t.serverCache.isFullyInitialized())return t;let h=t;a=eD(n)?i:new tL(null).setTree(n,i);let u=t.serverCache.getNode();return a.children.inorderTraversal((n,i)=>{if(u.hasChild(n)){let a=nn(e,t.serverCache.getNode().getImmediateChild(n),i);h=t9(e,h,new ek(n),a,r,s,o,l)}}),a.children.inorderTraversal((n,i)=>{let a=!t.serverCache.isCompleteForChild(n)&&null===i.value;if(!u.hasChild(n)&&!a){let a=nn(e,t.serverCache.getNode().getImmediateChild(n),i);h=t9(e,h,new ek(n),a,r,s,o,l)}}),h}class nr{constructor(e,t){this.query_=e,this.eventRegistrations_=[];let n=this.query_._queryParams,i=new tu(n.getIndex()),r=function(e){return e.loadsAllData()?new tu(e.getIndex()):e.hasLimit()?new td(e):new tc(e)}(n);this.processor_={filter:r};let s=t.serverCache,o=t.eventCache,l=i.updateFullNode(e9.EMPTY_NODE,s.getNode(),null),a=r.updateFullNode(e9.EMPTY_NODE,o.getNode(),null),h=new tN(l,s.isFullyInitialized(),i.filtersNodes()),u=new tN(a,o.isFullyInitialized(),r.filtersNodes());this.viewCache_=tA(u,h),this.eventGenerator_=new tx(this.query_)}get query(){return this.query_}}function ns(e){return 0===e.eventRegistrations_.length}function no(e,t,n){let i=[];if(n){(0,g.vA)(null==t,"A cancel should cancel all event registrations.");let r=e.query._path;e.eventRegistrations_.forEach(e=>{let t=e.createCancelEvent(n,r);t&&i.push(t)})}if(t){let n=[];for(let i=0;i<e.eventRegistrations_.length;++i){let r=e.eventRegistrations_[i];if(r.matches(t)){if(t.hasAnyCallback()){n=n.concat(e.eventRegistrations_.slice(i+1));break}}else n.push(r)}e.eventRegistrations_=n}else e.eventRegistrations_=[];return i}function nl(e,t,n,i){var r,s;t.type===_.MERGE&&null!==t.source.queryId&&((0,g.vA)(tO(e.viewCache_),"We should always have a full cache before handling merges"),(0,g.vA)(tF(e.viewCache_),"Missing event cache, even though we have a server cache"));let o=e.viewCache_,l=function(e,t,n,i,r){let s,o,l=new t4;if(n.type===_.OVERWRITE)n.source.fromUser?s=ne(e,t,n.path,n.snap,i,r,l):((0,g.vA)(n.source.fromServer,"Unknown source."),o=n.source.tagged||t.serverCache.isFiltered()&&!eD(n.path),s=t9(e,t,n.path,n.snap,i,r,o,l));else if(n.type===_.MERGE){var a,h,u,c,d,p,f;let _;n.source.fromUser?(a=e,h=t,u=n.path,c=n.children,d=i,p=r,f=l,_=h,c.foreach((e,t)=>{let n=eA(u,e);nt(h,eE(n))&&(_=ne(a,_,n,t,d,p,f))}),c.foreach((e,t)=>{let n=eA(u,e);nt(h,eE(n))||(_=ne(a,_,n,t,d,p,f))}),s=_):((0,g.vA)(n.source.fromServer,"Unknown source."),o=n.source.tagged||t.serverCache.isFiltered(),s=ni(e,t,n.path,n.children,i,r,o,l))}else if(n.type===_.ACK_USER_WRITE)s=n.revert?function(e,t,n,i,r,s){let o;if(null!=t0(i,n))return t;{let l,a=new t6(i,t,r),h=t.eventCache.getNode();if(eD(n)||".priority"===eE(n)){let n;if(t.serverCache.isFullyInitialized())n=tX(i,tO(t));else{let e=t.serverCache.getNode();(0,g.vA)(e instanceof e9,"serverChildren would be complete if leaf node"),n=tJ(i,e)}l=e.filter.updateFullNode(h,n,s)}else{let r=eE(n),u=t1(i,r,t.serverCache);null==u&&t.serverCache.isCompleteForChild(r)&&(u=h.getImmediateChild(r)),(l=null!=u?e.filter.updateChild(h,r,u,eP(n),a,s):t.eventCache.getNode().hasChild(r)?e.filter.updateChild(h,r,e9.EMPTY_NODE,eP(n),a,s):h).isEmpty()&&t.serverCache.isFullyInitialized()&&(o=tX(i,tO(t))).isLeafNode()&&(l=e.filter.updateFullNode(l,o,s))}return o=t.serverCache.isFullyInitialized()||null!=t0(i,eb()),tD(t,l,o,e.filter.filtersNodes())}}(e,t,n.path,i,r,l):function(e,t,n,i,r,s,o){if(null!=t0(r,n))return t;let l=t.serverCache.isFiltered(),a=t.serverCache;if(null!=i.value)if(eD(n)&&a.isFullyInitialized()||a.isCompleteForPath(n))return t9(e,t,n,a.getNode().getChild(n),r,s,l,o);else{if(!eD(n))return t;let i=new tL(null);return a.getNode().forEachChild(eV,(e,t)=>{i=i.set(new ek(e),t)}),ni(e,t,n,i,r,s,l,o)}{let h=new tL(null);return i.foreach((e,t)=>{let i=eA(n,e);a.isCompleteForPath(i)&&(h=h.set(e,a.getNode().getChild(i)))}),ni(e,t,n,h,r,s,l,o)}}(e,t,n.path,n.affectedTree,i,r,l);else if(n.type===_.LISTEN_COMPLETE)s=function(e,t,n,i,r){let s=t.serverCache;return t7(e,tM(t,s.getNode(),s.isFullyInitialized()||eD(n),s.isFiltered()),n,i,t8,r)}(e,t,n.path,i,l);else throw(0,g.Hk)("Unknown operation type: "+n.type);let m=l.getChanges();return function(e,t,n){let i=t.eventCache;if(i.isFullyInitialized()){let r=i.getNode().isLeafNode()||i.getNode().isEmpty(),s=tF(e);!(n.length>0)&&e.eventCache.isFullyInitialized()&&(!r||i.getNode().equals(s))&&i.getNode().getPriority().equals(s.getPriority())||n.push(to(tF(t)))}}(t,s,m),{viewCache:s,changes:m}}(e.processor_,o,t,n,i);return r=e.processor_,s=l.viewCache,(0,g.vA)(s.eventCache.getNode().isIndexed(r.filter.getIndex()),"Event snap not indexed"),(0,g.vA)(s.serverCache.getNode().isIndexed(r.filter.getIndex()),"Server snap not indexed"),(0,g.vA)(l.viewCache.serverCache.isFullyInitialized()||!o.serverCache.isFullyInitialized(),"Once a server snap is complete, it should never go back"),e.viewCache_=l.viewCache,na(e,l.changes,l.viewCache.eventCache.getNode(),null)}function na(e,t,n,i){let r=i?[i]:e.eventRegistrations_;var s=e.eventGenerator_;let o=[],l=[];return t.forEach(e=>{var t;"child_changed"===e.type&&s.index_.indexedValueChanged(e.oldSnap,e.snapshotNode)&&l.push((t=e.childName,{type:"child_moved",snapshotNode:e.snapshotNode,childName:t}))}),tR(s,o,"child_removed",t,r,n),tR(s,o,"child_added",t,r,n),tR(s,o,"child_moved",l,r,n),tR(s,o,"child_changed",t,r,n),tR(s,o,"value",t,r,n),o}class nh{constructor(){this.views=new Map}}function nu(e,t,n,i){let r=t.source.queryId;if(null!==r){let s=e.views.get(r);return(0,g.vA)(null!=s,"SyncTree gave us an op for an invalid query."),nl(s,t,n,i)}{let r=[];for(let s of e.views.values())r=r.concat(nl(s,t,n,i));return r}}function nc(e,t,n,i,r){let s=t._queryIdentifier,o=e.views.get(s);if(!o){let e=tX(n,r?i:null),s=!1;return e?s=!0:(e=i instanceof e9?tJ(n,i):e9.EMPTY_NODE,s=!1),new nr(t,tA(new tN(e,s,!1),new tN(i,r,!1)))}return o}function nd(e){let t=[];for(let n of e.views.values())n.query._queryParams.loadsAllData()||t.push(n);return t}function n_(e,t){let n=null;for(let i of e.views.values())n=n||function(e,t){let n=tO(e.viewCache_);return n&&(e.query._queryParams.loadsAllData()||!eD(t)&&!n.getImmediateChild(eE(t)).isEmpty())?n.getChild(t):null}(i,t);return n}function np(e,t){if(t._queryParams.loadsAllData())return ng(e);{let n=t._queryIdentifier;return e.views.get(n)}}function nf(e){return null!=ng(e)}function ng(e){for(let t of e.views.values())if(t.query._queryParams.loadsAllData())return t;return null}let nm=1;class ny{constructor(e){this.listenProvider_=e,this.syncPointTree_=new tL(null),this.pendingWriteTree_={visibleWrites:tq.empty(),allWrites:[],lastWriteId:-1},this.tagToQueryMap=new Map,this.queryToTagMap=new Map}}function nv(e,t,n,i,r){var s,o;return(s=e.pendingWriteTree_,o=r,(0,g.vA)(i>s.lastWriteId,"Stacking an older write on top of newer ones"),void 0===o&&(o=!0),s.allWrites.push({path:t,snap:n,writeId:i,visible:o}),o&&(s.visibleWrites=tW(s.visibleWrites,t,n)),s.lastWriteId=i,r)?nb(e,new tS(tT(),t,n)):[]}function nC(e,t,n=!1){let i=function(e,t){for(let n=0;n<e.allWrites.length;n++){let i=e.allWrites[n];if(i.writeId===t)return i}return null}(e.pendingWriteTree_,t);if(!function(e,t){var n;let i=e.allWrites.findIndex(e=>e.writeId===t);(0,g.vA)(i>=0,"removeWrite called with nonexistent writeId.");let r=e.allWrites[i];e.allWrites.splice(i,1);let s=r.visible,o=!1,l=e.allWrites.length-1;for(;s&&l>=0;){let t=e.allWrites[l];t.visible&&(l>=i&&function(e,t){if(e.snap)return eL(e.path,t);for(let n in e.children)if(e.children.hasOwnProperty(n)&&eL(eA(e.path,n),t))return!0;return!1}(t,r.path)?s=!1:eL(r.path,t.path)&&(o=!0)),l--}return!!s&&(o?((n=e).visibleWrites=tQ(n.allWrites,t$,eb()),n.allWrites.length>0?n.lastWriteId=n.allWrites[n.allWrites.length-1].writeId:n.lastWriteId=-1):r.snap?e.visibleWrites=tH(e.visibleWrites,r.path):Q(r.children,t=>{e.visibleWrites=tH(e.visibleWrites,eA(r.path,t))}),!0)}(e.pendingWriteTree_,t))return[];{let t=new tL(null);return null!=i.snap?t=t.set(eb(),!0):Q(i.children,e=>{t=t.set(new ek(e),!0)}),nb(e,new tb(i.path,t,n))}}function nw(e,t,n){return nb(e,new tS(tI(),t,n))}function nT(e,t,n,i,r=!1){let s=t._path,o=e.syncPointTree_.get(s),l=[];if(o&&("default"===t._queryIdentifier||null!=np(o,t))){let u=function(e,t,n,i){let r=t._queryIdentifier,s=[],o=[],l=nf(e);if("default"===r)for(let[t,r]of e.views.entries())o=o.concat(no(r,n,i)),ns(r)&&(e.views.delete(t),r.query._queryParams.loadsAllData()||s.push(r.query));else{let t=e.views.get(r);t&&(o=o.concat(no(t,n,i)),ns(t)&&(e.views.delete(r),t.query._queryParams.loadsAllData()||s.push(t.query)))}return l&&!nf(e)&&s.push(new((0,g.vA)(c,"Reference.ts has not been loaded"),c)(t._repo,t._path)),{removed:s,events:o}}(o,t,n,i);0===o.views.size&&(e.syncPointTree_=e.syncPointTree_.remove(s));let d=u.removed;if(l=u.events,!r){let n=-1!==d.findIndex(e=>e._queryParams.loadsAllData()),r=e.syncPointTree_.findOnPath(s,(e,t)=>nf(t));if(n&&!r){let t=e.syncPointTree_.subtree(s);if(!t.isEmpty()){let n=t.fold((e,t,n)=>{if(t&&nf(t))return[ng(t)];{let e=[];return t&&(e=nd(t)),Q(n,(t,n)=>{e=e.concat(n)}),e}});for(let t=0;t<n.length;++t){let i=n[t],r=i.query,s=nE(e,i);e.listenProvider_.startListening(nA(r),nS(e,r),s.hashFn,s.onComplete)}}}r||!(d.length>0)||i||(n?e.listenProvider_.stopListening(nA(t),null):d.forEach(t=>{let n=e.queryToTagMap.get(nP(t));e.listenProvider_.stopListening(nA(t),n)}))}var a=e,h=d;for(let e=0;e<h.length;++e){let t=h[e];if(!t._queryParams.loadsAllData()){let e=nP(t),n=a.queryToTagMap.get(e);a.queryToTagMap.delete(e),a.tagToQueryMap.delete(n)}}}return l}function nI(e,t,n,i){let r=nN(e,i);if(null==r)return[];{let i=nx(r),s=i.path,o=i.queryId,l=eM(s,t);return nR(e,s,new tS(tk(o),l,n))}}function nk(e,t,n){let i=e.pendingWriteTree_,r=e.syncPointTree_.findOnPath(t,(e,n)=>{let i=n_(n,eM(e,t));if(i)return i});return tG(i,t,r,n,!0)}function nb(e,t){var n;return function e(t,n,i,r){if(eD(t.path))return function e(t,n,i,r){let s=n.get(eb());null==i&&null!=s&&(i=n_(s,eb()));let o=[];return n.children.inorderTraversal((n,s)=>{let l=i?i.getImmediateChild(n):null,a=t2(r,n),h=t.operationForChild(n);h&&(o=o.concat(e(h,s,l,a)))}),s&&(o=o.concat(nu(s,t,r,i))),o}(t,n,i,r);{let s=n.get(eb());null==i&&null!=s&&(i=n_(s,eb()));let o=[],l=eE(t.path),a=t.operationForChild(l),h=n.children.get(l);if(h&&a){let t=i?i.getImmediateChild(l):null,n=t2(r,l);o=o.concat(e(a,h,t,n))}return s&&(o=o.concat(nu(s,t,r,i))),o}}(t,e.syncPointTree_,null,(n=e.pendingWriteTree_,t3(eb(),n)))}function nE(e,t){let n=t.query,i=nS(e,n);return{hashFn:()=>(t.viewCache_.serverCache.getNode()||e9.EMPTY_NODE).hash(),onComplete:t=>{if("ok"===t)if(i)return function(e,t,n){let i=nN(e,n);if(!i)return[];{let n=nx(i),r=n.path,s=n.queryId,o=eM(r,t);return nR(e,r,new tE(tk(s),o))}}(e,n._path,i);else{var r;return r=n._path,nb(e,new tE(tI(),r))}{let i=function(e,t){let n="Unknown Error";"too_big"===e?n="The data requested exceeds the maximum size that can be accessed with a single request.":"permission_denied"===e?n="Client doesn't have permission to access the desired data.":"unavailable"===e&&(n="The service is unavailable");let i=Error(e+" at "+t._path.toString()+": "+n);return i.code=e.toUpperCase(),i}(t,n);return nT(e,n,null,i)}}}}function nS(e,t){let n=nP(t);return e.queryToTagMap.get(n)}function nP(e){return e._path.toString()+"$"+e._queryIdentifier}function nN(e,t){return e.tagToQueryMap.get(t)}function nx(e){let t=e.indexOf("$");return(0,g.vA)(-1!==t&&t<e.length-1,"Bad queryKey."),{queryId:e.substr(t+1),path:new ek(e.substr(0,t))}}function nR(e,t,n){let i=e.syncPointTree_.get(t);return(0,g.vA)(i,"Missing sync point for query tag that we're tracking"),nu(i,n,t3(t,e.pendingWriteTree_),null)}function nA(e){return e._queryParams.loadsAllData()&&!e._queryParams.isDefault()?new((0,g.vA)(d,"Reference.ts has not been loaded"),d)(e._repo,e._path):e}class nD{constructor(e){this.node_=e}getImmediateChild(e){return new nD(this.node_.getImmediateChild(e))}node(){return this.node_}}class nM{constructor(e,t){this.syncTree_=e,this.path_=t}getImmediateChild(e){let t=eA(this.path_,e);return new nM(this.syncTree_,t)}node(){return nk(this.syncTree_,this.path_)}}let nF=function(e,t,n){return e&&"object"==typeof e?((0,g.vA)(".sv"in e,"Unexpected leaf node or priority contents"),"string"==typeof e[".sv"])?nO(e[".sv"],t,n):"object"==typeof e[".sv"]?nL(e[".sv"],t):void(0,g.vA)(!1,"Unexpected server value: "+JSON.stringify(e,null,2)):e},nO=function(e,t,n){if("timestamp"===e)return n.timestamp;(0,g.vA)(!1,"Unexpected server value: "+e)},nL=function(e,t,n){e.hasOwnProperty("increment")||(0,g.vA)(!1,"Unexpected server value: "+JSON.stringify(e,null,2));let i=e.increment;"number"!=typeof i&&(0,g.vA)(!1,"Unexpected increment value: "+i);let r=t.node();if((0,g.vA)(null!=r,"Expected ChildrenNode.EMPTY_NODE for nulls"),!r.isLeafNode())return i;let s=r.getValue();return"number"!=typeof s?i:s+i},nq=function(e,t,n,i){return nU(t,new nM(n,e),i)},nW=function(e,t,n){return nU(e,new nD(t),n)};function nU(e,t,n){let i,r=nF(e.getPriority().val(),t.getImmediateChild(".priority"),n);if(!e.isLeafNode())return i=e,r!==e.getPriority().val()&&(i=i.updatePriority(new e1(r))),e.forEachChild(e3,(e,r)=>{let s=nU(r,t.getImmediateChild(e),n);s!==r&&(i=i.updateImmediateChild(e,s))}),i;{let i=nF(e.getValue(),t,n);return i!==e.getValue()||r!==e.getPriority().val()?new e1(i,tn(r)):e}}class nH{constructor(e="",t=null,n={children:{},childCount:0}){this.name=e,this.parent=t,this.node=n}}function nj(e,t){let n=t instanceof ek?t:new ek(t),i=e,r=eE(n);for(;null!==r;){let e=(0,g.yw)(i.node.children,r)||{children:{},childCount:0};i=new nH(r,i,e),r=eE(n=eP(n))}return i}function nz(e){return e.node.value}function nY(e,t){e.node.value=t,function e(t){null!==t.parent&&function(t,n,i){let r=void 0===nz(i)&&!nK(i),s=(0,g.gR)(t.node.children,n);r&&s?(delete t.node.children[n],t.node.childCount--,e(t)):r||s||(t.node.children[n]=i.node,t.node.childCount++,e(t))}(t.parent,t.name,t)}(e)}function nK(e){return e.node.childCount>0}function nV(e,t){Q(e.node.children,(n,i)=>{t(new nH(n,e,i))})}function nB(e){return new ek(null===e.parent?e.name:nB(e.parent)+"/"+e.name)}let n$=/[\[\].#$\/\u0000-\u001F\u007F]/,nQ=/[\[\].#$\u0000-\u001F\u007F]/,nG=function(e){return"string"==typeof e&&0!==e.length&&!n$.test(e)},nX=function(e){return"string"==typeof e&&0!==e.length&&!nQ.test(e)},nJ=function(e,t,n,i){i&&void 0===t||nZ((0,g.dI)(e,"value"),t,n)},nZ=function(e,t,n){let i=n instanceof ek?new eq(n,e):n;if(void 0===t)throw Error(e+"contains undefined "+eU(i));if("function"==typeof t)throw Error(e+"contains a function "+eU(i)+" with contents = "+t.toString());if(U(t))throw Error(e+"contains "+t.toString()+" "+eU(i));if("string"==typeof t&&t.length>3495253.3333333335&&(0,g.OE)(t)>0xa00000)throw Error(e+"contains a string greater than 10485760 utf8 bytes "+eU(i)+" ('"+t.substring(0,50)+"...')");if(t&&"object"==typeof t){let n=!1,r=!1;if(Q(t,(t,s)=>{if(".value"===t)n=!0;else if(".priority"!==t&&".sv"!==t&&(r=!0,!nG(t)))throw Error(e+" contains an invalid key ("+t+") "+eU(i)+'.  Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"');i.parts_.length>0&&(i.byteLength_+=1),i.parts_.push(t),i.byteLength_+=(0,g.OE)(t),eW(i),nZ(e,s,i);let o=i.parts_.pop();i.byteLength_-=(0,g.OE)(o),i.parts_.length>0&&(i.byteLength_-=1)}),n&&r)throw Error(e+' contains ".value" child '+eU(i)+" in addition to actual children.")}},n0=function(e,t){let n,i;for(n=0;n<t.length;n++){let r=ex(i=t[n]);for(let t=0;t<r.length;t++)if(".priority"===r[t]&&t===r.length-1);else if(!nG(r[t]))throw Error(e+"contains an invalid key ("+r[t]+") in path "+i.toString()+'. Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"')}t.sort(eF);let r=null;for(n=0;n<t.length;n++){if(i=t[n],null!==r&&eL(r,i))throw Error(e+"contains a path "+r.toString()+" that is ancestor of another path "+i.toString());r=i}},n1=function(e,t,n,i){if(i&&void 0===t)return;let r=(0,g.dI)(e,"values");if(!(t&&"object"==typeof t)||Array.isArray(t))throw Error(r+" must be an object containing the children to replace.");let s=[];Q(t,(e,t)=>{let i=new ek(e);if(nZ(r,t,eA(n,i)),".priority"===eN(i)&&!(null===t||"string"==typeof t||"number"==typeof t&&!U(t)||t&&"object"==typeof t&&(0,g.gR)(t,".sv")))throw Error(r+"contains an invalid value for '"+i.toString()+"', which must be a valid Firebase priority (a string, finite number, server value, or null).");s.push(i)}),n0(r,s)},n2=function(e,t,n,i){if((!i||void 0!==n)&&!nX(n))throw Error((0,g.dI)(e,t)+'was an invalid path = "'+n+'". Paths must be non-empty strings and can\'t contain ".", "#", "$", "[", or "]"')},n3=function(e,t,n,i){n&&(n=n.replace(/^\/*\.info(\/|$)/,"/")),n2(e,t,n,i)},n4=function(e,t){if(".info"===eE(t))throw Error(e+" failed = Can't modify data under /.info/")},n5=function(e,t){var n;let i=t.path.toString();if("string"!=typeof t.repoInfo.host||0===t.repoInfo.host.length||!nG(t.repoInfo.namespace)&&"localhost"!==t.repoInfo.host.split(":")[0]||0!==i.length&&((n=i)&&(n=n.replace(/^\/*\.info(\/|$)/,"/")),!nX(n)))throw Error((0,g.dI)(e,"url")+'must be a valid firebase URL and the path can\'t contain ".", "#", "$", "[", or "]".')};class n8{constructor(){this.eventLists_=[],this.recursionDepth_=0}}function n6(e,t){let n=null;for(let i=0;i<t.length;i++){let r=t[i],s=r.getPath();null===n||eO(s,n.path)||(e.eventLists_.push(n),n=null),null===n&&(n={events:[],path:s}),n.events.push(r)}n&&e.eventLists_.push(n)}function n7(e,t,n){n6(e,n),function(e,t){e.recursionDepth_++;let n=!0;for(let i=0;i<e.eventLists_.length;i++){let r=e.eventLists_[i];r&&(t(r.path)?(function(e){for(let t=0;t<e.events.length;t++){let n=e.events[t];if(null!==n){e.events[t]=null;let i=n.getEventRunner();R&&M("event: "+n.toString()),Z(i)}}}(e.eventLists_[i]),e.eventLists_[i]=null):n=!1)}n&&(e.eventLists_=[]),e.recursionDepth_--}(e,e=>eL(e,t)||eL(t,e))}class n9{constructor(e,t,n,i){this.repoInfo_=e,this.forceRestClient_=t,this.authTokenProvider_=n,this.appCheckProvider_=i,this.dataUpdateCount=0,this.statsListener_=null,this.eventQueue_=new n8,this.nextWriteId_=1,this.interceptServerDataCallback_=null,this.onDisconnect_=ty(),this.transactionQueueTree_=new nH,this.persistentConnection_=null,this.key=this.repoInfo_.toURLString()}toString(){return(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host}}function ie(e){let t=e.infoData_.getNode(new ek(".info/serverTimeOffset")).val()||0;return new Date().getTime()+t}function it(e){var t;return(t=t={timestamp:ie(e)}).timestamp=t.timestamp||new Date().getTime(),t}function ii(e,t,n,i,r){e.dataUpdateCount++;let s=new ek(t);n=e.interceptServerDataCallback_?e.interceptServerDataCallback_(t,n):n;let o=[];if(r)if(i){let t=(0,g.kH)(n,e=>tn(e));o=function(e,t,n,i){let r=nN(e,i);if(!r)return[];{let i=nx(r),s=i.path,o=i.queryId,l=eM(s,t),a=tL.fromObject(n);return nR(e,s,new tP(tk(o),l,a))}}(e.serverSyncTree_,s,t,r)}else{let t=tn(n);o=nI(e.serverSyncTree_,s,t,r)}else if(i){let t=(0,g.kH)(n,e=>tn(e));o=function(e,t,n){let i=tL.fromObject(n);return nb(e,new tP(tI(),t,i))}(e.serverSyncTree_,s,t)}else{let t=tn(n);o=nw(e.serverSyncTree_,s,t)}let l=s;o.length>0&&(l=iu(e,s)),n7(e.eventQueue_,l,o)}function ir(e,t){is(e,"connected",t),!1===t&&function(e){il(e,"onDisconnectEvents");let t=it(e),n=ty();tv(e.onDisconnect_,eb(),(i,r)=>{let s=nq(i,r,e.serverSyncTree_,t);!function e(t,n,i){if(eD(n))t.value=i,t.children.clear();else if(null!==t.value)t.value=t.value.updateChild(n,i);else{let r=eE(n);t.children.has(r)||t.children.set(r,ty()),e(t.children.get(r),n=eP(n),i)}}(n,i,s)});let i=[];tv(n,eb(),(t,n)=>{i=i.concat(nw(e.serverSyncTree_,t,n));let r=ip(e,t);iu(e,r)}),e.onDisconnect_=ty(),n7(e.eventQueue_,eb(),i)}(e)}function is(e,t,n){let i=new ek("/.info/"+t),r=tn(n);e.infoData_.updateSnapshot(i,r);let s=nw(e.infoSyncTree_,i,r);n7(e.eventQueue_,i,s)}function io(e){return e.nextWriteId_++}function il(e,...t){let n="";e.persistentConnection_&&(n=e.persistentConnection_.id+":"),M(n,...t)}function ia(e,t,n,i){t&&Z(()=>{if("ok"===n)t(null);else{let e=(n||"error").toUpperCase(),r=e;i&&(r+=": "+i);let s=Error(r);s.code=e,t(s)}})}function ih(e,t,n){return nk(e.serverSyncTree_,t,n)||e9.EMPTY_NODE}function iu(e,t){let n=ic(e,t),i=nB(n),r=id(e,n);return function(e,t,n){if(0===t.length)return;let i=[],r=[],s=t.filter(e=>0===e.status).map(e=>e.currentWriteId);for(let o=0;o<t.length;o++){let l=t[o],a=eM(n,l.path),h=!1,u;if((0,g.vA)(null!==a,"rerunTransactionsUnderNode_: relativePath should not be null."),4===l.status)h=!0,u=l.abortReason,r=r.concat(nC(e.serverSyncTree_,l.currentWriteId,!0));else if(0===l.status)if(l.retryCount>=25)h=!0,u="maxretry",r=r.concat(nC(e.serverSyncTree_,l.currentWriteId,!0));else{let n=ih(e,l.path,s);l.currentInputSnapshot=n;let i=t[o].update(n.val());if(void 0!==i){nZ("transaction failed: Data returned ",i,l.path);let t=tn(i);"object"==typeof i&&null!=i&&(0,g.gR)(i,".priority")||(t=t.updatePriority(n.getPriority()));let o=l.currentWriteId,a=nW(t,n,it(e));l.currentOutputSnapshotRaw=t,l.currentOutputSnapshotResolved=a,l.currentWriteId=io(e),s.splice(s.indexOf(o),1),r=(r=r.concat(nv(e.serverSyncTree_,l.path,a,l.currentWriteId,l.applyLocally))).concat(nC(e.serverSyncTree_,o,!0))}else h=!0,u="nodata",r=r.concat(nC(e.serverSyncTree_,l.currentWriteId,!0))}n7(e.eventQueue_,n,r),r=[],h&&(t[o].status=2,setTimeout(t[o].unwatcher,Math.floor(0)),t[o].onComplete&&("nodata"===u?i.push(()=>t[o].onComplete(null,!1,t[o].currentInputSnapshot)):i.push(()=>t[o].onComplete(Error(u),!1,null))))}i_(e,e.transactionQueueTree_);for(let e=0;e<i.length;e++)Z(i[e]);!function e(t,n=t.transactionQueueTree_){if(n||i_(t,n),nz(n)){let i=id(t,n);(0,g.vA)(i.length>0,"Sending zero length transaction queue"),i.every(e=>0===e.status)&&function(t,n,i){let r=ih(t,n,i.map(e=>e.currentWriteId)),s=r,o=r.hash();for(let e=0;e<i.length;e++){let t=i[e];(0,g.vA)(0===t.status,"tryToSendTransactionQueue_: items in queue should all be run."),t.status=1,t.retryCount++;let r=eM(n,t.path);s=s.updateChild(r,t.currentOutputSnapshotRaw)}let l=s.val(!0);t.server_.put(n.toString(),l,r=>{il(t,"transaction put response",{path:n.toString(),status:r});let s=[];if("ok"===r){let r=[];for(let e=0;e<i.length;e++)i[e].status=2,s=s.concat(nC(t.serverSyncTree_,i[e].currentWriteId)),i[e].onComplete&&r.push(()=>i[e].onComplete(null,!0,i[e].currentOutputSnapshotResolved)),i[e].unwatcher();i_(t,nj(t.transactionQueueTree_,n)),e(t,t.transactionQueueTree_),n7(t.eventQueue_,n,s);for(let e=0;e<r.length;e++)Z(r[e])}else{if("datastale"===r)for(let e=0;e<i.length;e++)3===i[e].status?i[e].status=4:i[e].status=0;else{q("transaction at "+n.toString()+" failed: "+r);for(let e=0;e<i.length;e++)i[e].status=4,i[e].abortReason=r}iu(t,n)}},o)}(t,nB(n),i)}else nK(n)&&nV(n,n=>{e(t,n)})}(e,e.transactionQueueTree_)}(e,r,i),i}function ic(e,t){let n,i=e.transactionQueueTree_;for(n=eE(t);null!==n&&void 0===nz(i);)i=nj(i,n),n=eE(t=eP(t));return i}function id(e,t){let n=[];return function e(t,n,i){let r=nz(n);if(r)for(let e=0;e<r.length;e++)i.push(r[e]);nV(n,n=>{e(t,n,i)})}(e,t,n),n.sort((e,t)=>e.order-t.order),n}function i_(e,t){let n=nz(t);if(n){let e=0;for(let t=0;t<n.length;t++)2!==n[t].status&&(n[e]=n[t],e++);n.length=e,nY(t,n.length>0?n:void 0)}nV(t,t=>{i_(e,t)})}function ip(e,t){let n=nB(ic(e,t)),i=nj(e.transactionQueueTree_,t);return!function(e,t,n){let i=e.parent;for(;null!==i;){if(t(i))return!0;i=i.parent}}(i,t=>{ig(e,t)}),ig(e,i),!function e(t,n,i,r){i&&!r&&n(t),nV(t,t=>{e(t,n,!0,r)}),i&&r&&n(t)}(i,t=>{ig(e,t)}),n}function ig(e,t){let n=nz(t);if(n){let i=[],r=[],s=-1;for(let t=0;t<n.length;t++)3===n[t].status||(1===n[t].status?((0,g.vA)(s===t-1,"All SENT items should be at beginning of queue."),s=t,n[t].status=3,n[t].abortReason="set"):((0,g.vA)(0===n[t].status,"Unexpected transaction status in abort"),n[t].unwatcher(),r=r.concat(nC(e.serverSyncTree_,n[t].currentWriteId,!0)),n[t].onComplete&&i.push(n[t].onComplete.bind(null,Error("set"),!1,null))));-1===s?nY(t,void 0):n.length=s+1,n7(e.eventQueue_,nB(t),r);for(let e=0;e<i.length;e++)Z(i[e])}}let im=function(e,t){let n=iy(e),i=n.namespace;"firebase.com"===n.domain&&L(n.host+" is no longer supported. Please use <YOUR FIREBASE>.firebaseio.com instead"),i&&"undefined"!==i||"localhost"===n.domain||L("Cannot parse Firebase url. Please use https://<YOUR FIREBASE>.firebaseio.com"),n.secure||W();let r="ws"===n.scheme||"wss"===n.scheme;return{repoInfo:new el(n.host,n.secure,i,r,t,"",i!==n.subdomain),path:new ek(n.pathString)}},iy=function(e){let t="",n="",i="",r="",s="",o=!0,l="https",a=443;if("string"==typeof e){let h=e.indexOf("//");h>=0&&(l=e.substring(0,h-1),e=e.substring(h+2));let u=e.indexOf("/");-1===u&&(u=e.length);let c=e.indexOf("?");-1===c&&(c=e.length),t=e.substring(0,Math.min(u,c)),u<c&&(r=function(e){let t="",n=e.split("/");for(let e=0;e<n.length;e++)if(n[e].length>0){let i=n[e];try{i=decodeURIComponent(i.replace(/\+/g," "))}catch(e){}t+="/"+i}return t}(e.substring(u,c)));let d=function(e){let t={};for(let n of("?"===e.charAt(0)&&(e=e.substring(1)),e.split("&"))){if(0===n.length)continue;let i=n.split("=");2===i.length?t[decodeURIComponent(i[0])]=decodeURIComponent(i[1]):q(`Invalid query segment '${n}' in query '${e}'`)}return t}(e.substring(Math.min(e.length,c)));(h=t.indexOf(":"))>=0?(o="https"===l||"wss"===l,a=parseInt(t.substring(h+1),10)):h=t.length;let _=t.slice(0,h);if("localhost"===_.toLowerCase())n="localhost";else if(_.split(".").length<=2)n=_;else{let e=t.indexOf(".");i=t.substring(0,e).toLowerCase(),n=t.substring(e+1),s=i}"ns"in d&&(s=d.ns)}return{host:t,port:a,domain:n,subdomain:i,secure:o,scheme:l,pathString:r,namespace:s}},iv="-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz",iC=function(){let e=0,t=[];return function(n){let i,r=n===e;e=n;let s=Array(8);for(i=7;i>=0;i--)s[i]=iv.charAt(n%64),n=Math.floor(n/64);(0,g.vA)(0===n,"Cannot push at time == 0");let o=s.join("");if(r){for(i=11;i>=0&&63===t[i];i--)t[i]=0;t[i]++}else for(i=0;i<12;i++)t[i]=Math.floor(64*Math.random());for(i=0;i<12;i++)o+=iv.charAt(t[i]);return(0,g.vA)(20===o.length,"nextPushId: Length should be 20."),o}}();class iw{constructor(e,t,n,i){this.eventType=e,this.eventRegistration=t,this.snapshot=n,this.prevName=i}getPath(){let e=this.snapshot.ref;return"value"===this.eventType?e._path:e.parent._path}getEventType(){return this.eventType}getEventRunner(){return this.eventRegistration.getEventRunner(this)}toString(){return this.getPath().toString()+":"+this.eventType+":"+(0,g.As)(this.snapshot.exportVal())}}class iT{constructor(e,t,n){this.eventRegistration=e,this.error=t,this.path=n}getPath(){return this.path}getEventType(){return"cancel"}getEventRunner(){return this.eventRegistration.getEventRunner(this)}toString(){return this.path.toString()+":cancel"}}class iI{constructor(e,t){this.snapshotCallback=e,this.cancelCallback=t}onValue(e,t){this.snapshotCallback.call(null,e,t)}onCancel(e){return(0,g.vA)(this.hasCancelCallback,"Raising a cancel event on a listener with no cancel callback"),this.cancelCallback.call(null,e)}get hasCancelCallback(){return!!this.cancelCallback}matches(e){return this.snapshotCallback===e.snapshotCallback||void 0!==this.snapshotCallback.userCallback&&this.snapshotCallback.userCallback===e.snapshotCallback.userCallback&&this.snapshotCallback.context===e.snapshotCallback.context}}class ik{constructor(e,t,n,i){this._repo=e,this._path=t,this._queryParams=n,this._orderByCalled=i}get key(){return eD(this._path)?null:eN(this._path)}get ref(){return new ib(this._repo,this._path)}get _queryIdentifier(){let e=B(tf(this._queryParams));return"{}"===e?"default":e}get _queryObject(){return tf(this._queryParams)}isEqual(e){if(!((e=(0,g.Ku)(e))instanceof ik))return!1;let t=this._repo===e._repo,n=eO(this._path,e._path),i=this._queryIdentifier===e._queryIdentifier;return t&&n&&i}toJSON(){return this.toString()}toString(){return this._repo.toString()+function(e){let t="";for(let n=e.pieceNum_;n<e.pieces_.length;n++)""!==e.pieces_[n]&&(t+="/"+encodeURIComponent(String(e.pieces_[n])));return t||"/"}(this._path)}}class ib extends ik{constructor(e,t){super(e,t,new t_,!1)}get parent(){let e=eR(this._path);return null===e?null:new ib(this._repo,e)}get root(){let e=this;for(;null!==e.parent;)e=e.parent;return e}}class iE{constructor(e,t,n){this._node=e,this.ref=t,this._index=n}get priority(){return this._node.getPriority().val()}get key(){return this.ref.key}get size(){return this._node.numChildren()}child(e){let t=new ek(e),n=iP(this.ref,e);return new iE(this._node.getChild(t),n,e3)}exists(){return!this._node.isEmpty()}exportVal(){return this._node.val(!0)}forEach(e){return!this._node.isLeafNode()&&!!this._node.forEachChild(this._index,(t,n)=>e(new iE(n,iP(this.ref,t),e3)))}hasChild(e){let t=new ek(e);return!this._node.getChild(t).isEmpty()}hasChildren(){return!this._node.isLeafNode()&&!this._node.isEmpty()}toJSON(){return this.exportVal()}val(){return this._node.val()}}function iS(e,t){return(e=(0,g.Ku)(e))._checkNotDeleted("ref"),void 0!==t?iP(e._root,t):e._root}function iP(e,t){return null===eE((e=(0,g.Ku)(e))._path)?n3("child","path",t,!1):n2("child","path",t,!1),new ib(e._repo,eA(e._path,t))}function iN(e,t){let n;n4("push",(e=(0,g.Ku)(e))._path),nJ("push",t,e._path,!0);let i=iC(ie(e._repo)),r=iP(e,i),s=iP(e,i);return r.then=(n=null!=t?iR(s,t).then(()=>s):Promise.resolve(s)).then.bind(n),r.catch=n.then.bind(n,void 0),r}function ix(e){return n4("remove",e._path),iR(e,null)}function iR(e,t){n4("set",(e=(0,g.Ku)(e))._path),nJ("set",t,e._path,!1);let n=new g.cY;return!function(e,t,n,i,r){il(e,"set",{path:t.toString(),value:n,priority:null});let s=it(e),o=tn(n,null),l=nW(o,nk(e.serverSyncTree_,t),s),a=io(e),h=nv(e.serverSyncTree_,t,l,a,!0);n6(e.eventQueue_,h),e.server_.put(t.toString(),o.val(!0),(n,i)=>{let s="ok"===n;s||q("set at "+t+" failed: "+n);let o=nC(e.serverSyncTree_,a,!s);n7(e.eventQueue_,t,o),ia(e,r,n,i)});let u=ip(e,t);iu(e,u),n7(e.eventQueue_,u,[])}(e._repo,e._path,t,null,n.wrapCallback(()=>{})),n.promise}function iA(e,t){n1("update",t,e._path,!1);let n=new g.cY;return!function(e,t,n,i){il(e,"update",{path:t.toString(),value:n});let r=!0,s=it(e),o={};if(Q(n,(n,i)=>{r=!1,o[n]=nq(eA(t,n),tn(i),e.serverSyncTree_,s)}),r)M("update() called with empty data.  Don't do anything."),ia(e,i,"ok",void 0);else{let r=io(e),s=function(e,t,n,i){var r;r=e.pendingWriteTree_,(0,g.vA)(i>r.lastWriteId,"Stacking an older merge on top of newer ones"),r.allWrites.push({path:t,children:n,writeId:i,visible:!0}),r.visibleWrites=tU(r.visibleWrites,t,n),r.lastWriteId=i;let s=tL.fromObject(n);return nb(e,new tP(tT(),t,s))}(e.serverSyncTree_,t,o,r);n6(e.eventQueue_,s),e.server_.merge(t.toString(),n,(n,s)=>{let o="ok"===n;o||q("update at "+t+" failed: "+n);let l=nC(e.serverSyncTree_,r,!o),a=l.length>0?iu(e,t):t;n7(e.eventQueue_,a,l),ia(e,i,n,s)}),Q(n,n=>{let i=ip(e,eA(t,n));iu(e,i)}),n7(e.eventQueue_,t,[])}}(e._repo,e._path,t,n.wrapCallback(()=>{})),n.promise}function iD(e){e=(0,g.Ku)(e);let t=new iM(new iI(()=>{}));return(function(e,t,n){let i=function(e,t){var n;let i=t._path,r=null;e.syncPointTree_.foreachOnPath(i,(e,t)=>{let n=eM(e,i);r=r||n_(t,n)});let s=e.syncPointTree_.get(i);s?r=r||n_(s,eb()):(s=new nh,e.syncPointTree_=e.syncPointTree_.set(i,s));let o=null!=r,l=o?new tN(r,!0,!1):null,a=(n=e.pendingWriteTree_,t3(t._path,n));return tF(nc(s,t,a,o?l.getNode():e9.EMPTY_NODE,o).viewCache_)}(e.serverSyncTree_,t);return null!=i?Promise.resolve(i):e.server_.get(t).then(i=>{let r,s=tn(i).withIndex(t._queryParams.getIndex());if(!function(e,t,n,i=!1){let r,s=t._path,o=null,l=!1;e.syncPointTree_.foreachOnPath(s,(e,t)=>{let n=eM(e,s);o=o||n_(t,n),l=l||nf(t)});let a=e.syncPointTree_.get(s);a?(l=l||nf(a),o=o||n_(a,eb())):(a=new nh,e.syncPointTree_=e.syncPointTree_.set(s,a)),null!=o?r=!0:(r=!1,o=e9.EMPTY_NODE,e.syncPointTree_.subtree(s).foreachChild((e,t)=>{let n=n_(t,eb());n&&(o=o.updateImmediateChild(e,n))}));let h=null!=np(a,t);if(!h&&!t._queryParams.loadsAllData()){let n=nP(t);(0,g.vA)(!e.queryToTagMap.has(n),"View does not exist, but we have a tag");let i=nm++;e.queryToTagMap.set(n,i),e.tagToQueryMap.set(i,n)}let u=function(e,t,n,i,r,s){let o=nc(e,t,i,r,s);e.views.has(t._queryIdentifier)||e.views.set(t._queryIdentifier,o),o.eventRegistrations_.push(n);let l=o.viewCache_.eventCache,a=[];return l.getNode().isLeafNode()||l.getNode().forEachChild(e3,(e,t)=>{a.push(tl(e,t))}),l.isFullyInitialized()&&a.push(to(l.getNode())),na(o,a,l.getNode(),n)}(a,t,n,t3(s,e.pendingWriteTree_),o,r);if(!h&&!l&&!i){let n=np(a,t);u=u.concat(function(e,t,n){let i=t._path,r=nS(e,t),s=nE(e,n),o=e.listenProvider_.startListening(nA(t),r,s.hashFn,s.onComplete),l=e.syncPointTree_.subtree(i);if(r)(0,g.vA)(!nf(l.value),"If we're adding a query, it shouldn't be shadowed");else{let t=l.fold((e,t,n)=>{if(!eD(e)&&t&&nf(t))return[ng(t).query];{let e=[];return t&&(e=e.concat(nd(t).map(e=>e.query))),Q(n,(t,n)=>{e=e.concat(n)}),e}});for(let n=0;n<t.length;++n){let i=t[n];e.listenProvider_.stopListening(nA(i),nS(e,i))}}return o}(e,t,n))}}(e.serverSyncTree_,t,n,!0),t._queryParams.loadsAllData())r=nw(e.serverSyncTree_,t._path,s);else{let n=nS(e.serverSyncTree_,t);r=nI(e.serverSyncTree_,t._path,s,n)}return n7(e.eventQueue_,t._path,r),nT(e.serverSyncTree_,t,n,null,!0),s},n=>(il(e,"get for query "+(0,g.As)(t)+" failed: "+n),Promise.reject(Error(n))))})(e._repo,e,t).then(t=>new iE(t,new ib(e._repo,e._path),e._queryParams.getIndex()))}class iM{constructor(e){this.callbackContext=e}respondsTo(e){return"value"===e}createEvent(e,t){let n=t._queryParams.getIndex();return new iw("value",this,new iE(e.snapshotNode,new ib(t._repo,t._path),n))}getEventRunner(e){return"cancel"===e.getEventType()?()=>this.callbackContext.onCancel(e.error):()=>this.callbackContext.onValue(e.snapshot,null)}createCancelEvent(e,t){return this.callbackContext.hasCancelCallback?new iT(this,e,t):null}matches(e){return e instanceof iM&&(!e.callbackContext||!this.callbackContext||e.callbackContext.matches(this.callbackContext))}hasAnyCallback(){return null!==this.callbackContext}}(0,g.vA)(!c,"__referenceConstructor has already been defined"),c=ib,(0,g.vA)(!d,"__referenceConstructor has already been defined"),d=ib;let iF={};class iO{constructor(e,t){this._repoInternal=e,this.app=t,this.type="database",this._instanceStarted=!1}get _repo(){return this._instanceStarted||(!function(e,t,n){if(e.stats_=ed(e.repoInfo_),e.forceRestClient_||("object"==typeof window&&window.navigator&&window.navigator.userAgent||"").search(/googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i)>=0)e.server_=new tg(e.repoInfo_,(t,n,i,r)=>{ii(e,t,n,i,r)},e.authTokenProvider_,e.appCheckProvider_),setTimeout(()=>ir(e,!0),0);else{if(null!=n){if("object"!=typeof n)throw Error("Only objects are supported for option databaseAuthVariableOverride");try{(0,g.As)(n)}catch(e){throw Error("Invalid authOverride provided: "+e)}}e.persistentConnection_=new ej(e.repoInfo_,t,(t,n,i,r)=>{ii(e,t,n,i,r)},t=>{ir(e,t)},t=>{var n;n=e,Q(t,(e,t)=>{is(n,e,t)})},e.authTokenProvider_,e.appCheckProvider_,n),e.server_=e.persistentConnection_}e.authTokenProvider_.addTokenChangeListener(t=>{e.server_.refreshAuthToken(t)}),e.appCheckProvider_.addTokenChangeListener(t=>{e.server_.refreshAppCheckToken(t.token)}),e.statsReporter_=function(e,t){let n=e.toString();return ec[n]||(ec[n]=t()),ec[n]}(e.repoInfo_,()=>new tw(e.stats_,e.server_)),e.infoData_=new tm,e.infoSyncTree_=new ny({startListening:(t,n,i,r)=>{let s=[],o=e.infoData_.getNode(t._path);return o.isEmpty()||(s=nw(e.infoSyncTree_,t._path,o),setTimeout(()=>{r("ok")},0)),s},stopListening:()=>{}}),is(e,"connected",!1),e.serverSyncTree_=new ny({startListening:(t,n,i,r)=>(e.server_.listen(t,i,n,(n,i)=>{let s=r(n,i);n7(e.eventQueue_,t._path,s)}),[]),stopListening:(t,n)=>{e.server_.unlisten(t,n)}})}(this._repoInternal,this.app.options.appId,this.app.options.databaseAuthVariableOverride),this._instanceStarted=!0),this._repoInternal}get _root(){return this._rootInternal||(this._rootInternal=new ib(this._repo,eb())),this._rootInternal}_delete(){return null!==this._rootInternal&&(!function(e,t){let n=iF[t];n&&n[e.key]===e||L(`Database ${t}(${e.repoInfo_}) has already been deleted.`),e.persistentConnection_&&e.persistentConnection_.interrupt("repo_interrupt"),delete n[e.key]}(this._repo,this.app.name),this._repoInternal=null,this._rootInternal=null),Promise.resolve()}_checkNotDeleted(e){null===this._rootInternal&&L("Cannot call "+e+" on a deleted database.")}}function iL(e=(0,p.Sx)(),t){let n=(0,p.j6)(e,"database").getImmediate({identifier:t});if(!n._instanceStarted){let e=(0,g.yU)("database");e&&function(e,t,n,i={}){let r;(e=(0,g.Ku)(e))._checkNotDeleted("useEmulator");let s=`${t}:${n}`,o=e._repoInternal;if(e._instanceStarted){if(s===e._repoInternal.repoInfo_.host&&(0,g.bD)(i,o.repoInfo_.emulatorOptions))return;L("connectDatabaseEmulator() cannot initialize or alter the emulator configuration after the database instance has started.")}o.repoInfo_.nodeAdmin?(i.mockUserToken&&L('mockUserToken is not supported by the Admin SDK. For client access with mock users, please use the "firebase" package instead of "firebase-admin".'),r=new ei(ei.OWNER)):i.mockUserToken&&(r=new ei("string"==typeof i.mockUserToken?i.mockUserToken:(0,g.Fy)(i.mockUserToken,e.app.options.projectId))),(0,g.zJ)(t)&&((0,g.gE)(t),(0,g.P1)("Database",!0));var l=r;let a=s.lastIndexOf(":"),h=s.substring(0,a);o.repoInfo_=new el(s,(0,g.zJ)(h),o.repoInfo_.namespace,o.repoInfo_.webSocketOnly,o.repoInfo_.nodeAdmin,o.repoInfo_.persistenceKey,o.repoInfo_.includeNamespaceInQueryParams,!0,i),l&&(o.authTokenProvider_=l)}(n,...e)}return n}ej.prototype.simpleListen=function(e,t){this.sendRequest("q",{p:e},t)},ej.prototype.echo=function(e,t){this.sendRequest("echo",{d:e},t)},w=p.MF,(0,p.om)(new f.uA("database",(e,{instanceIdentifier:t})=>{let n=e.getProvider("app").getImmediate();return function(e,t,n,i,r){var s,o,l,a;let h,u,c,d,_=i||e.options.databaseURL;void 0===_&&(e.options.projectId||L("Can't determine Firebase Database URL. Be sure to include  a Project ID when calling firebase.initializeApp()."),M("Using default host for project ",e.options.projectId),_=`${e.options.projectId}-default-rtdb.firebaseio.com`);let p=im(_,void 0),f=p.repoInfo;void 0!==y&&y.env&&(c=y.env.FIREBASE_DATABASE_EMULATOR_HOST),c?(d=!0,f=(p=im(_=`http://${c}?ns=${f.namespace}`,void 0)).repoInfo):d=!p.repoInfo.secure;let g=r&&d?new ei(ei.OWNER):new en(e.name,e.options,t);return n5("Invalid Firebase Database URL",p),eD(p.path)||L("Database URL must point to the root of a Firebase Database (not including a child path)."),new iO((s=f,o=e,l=g,a=new et(e,n),(h=iF[o.name])||(h={},iF[o.name]=h),(u=h[s.toURLString()])&&L("Database initialized multiple times. Please make sure the format of the database URL matches with each database() call."),u=new n9(s,!1,l,a),h[s.toURLString()]=u,u),e)}(n,e.getProvider("auth-internal"),e.getProvider("app-check-internal"),t)},"PUBLIC").setMultipleInstances(!0)),(0,p.KO)(v,C,void 0),(0,p.KO)(v,C,"esm2020")}}]);