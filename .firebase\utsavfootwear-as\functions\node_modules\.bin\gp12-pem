#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../google-p12-pem/build/src/bin/gp12-pem.js" "$@"
else 
  exec node  "$basedir/../google-p12-pem/build/src/bin/gp12-pem.js" "$@"
fi
