(()=>{var a={};a.id=571,a.ids=[571],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8547:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Data\\\\utsavfootwears\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\products\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14973:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687),e=c(30474),f=c(92576),g=c(43210);function h({id:a,imageUrl:b,caption:c,name:h,onEdit:i,onDelete:j,isAdmin:k=!1}){let[l,m]=(0,g.useState)(!1),[n,o]=(0,g.useState)(!0),[p,q]=(0,g.useState)(!1);return(0,d.jsxs)(f.P.div,{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",initial:{opacity:1,y:0},whileInView:{opacity:1,y:0},viewport:{once:!0,amount:.3},transition:{duration:0},whileHover:p?{}:{y:-5,transition:{duration:.2}},children:[(0,d.jsxs)("div",{className:"relative w-full h-0 pb-[100%] overflow-hidden bg-gray-100",children:[n&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}),l?(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 z-10",children:(0,d.jsxs)("div",{className:"text-center text-gray-500",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCF7"}),(0,d.jsx)("div",{className:"text-sm",children:"Image not available"})]})}):(0,d.jsx)(f.P.div,{whileHover:p?{}:{scale:1.05},transition:{duration:.3},className:"absolute inset-0 w-full h-full",children:(0,d.jsx)(e.default,{src:b,alt:c,fill:!0,className:"object-cover",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",priority:!1,quality:p?75:85,placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=",loading:p?"eager":"lazy",onLoad:()=>{o(!1),console.log(`Image loaded successfully: ${b}`)},onError:a=>{console.error(`Image failed to load: ${b}`,a),m(!0),o(!1)}})})]}),(0,d.jsxs)("div",{className:"p-4",children:[h&&(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 text-lg",children:h}),(0,d.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:c}),k&&(i||j)&&(0,d.jsxs)("div",{className:"mt-4 flex gap-2",children:[i&&(0,d.jsx)(f.P.button,{onClick:()=>i(a),className:"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Edit"}),j&&(0,d.jsx)(f.P.button,{onClick:()=>j(a),className:"px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Delete"})]})]})]})}},17310:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(92576);function f({children:a,className:b="",delay:c=0,direction:f="up",duration:g=.6,disableScrollAnimation:h=!1}){let i=()=>{switch(f){case"up":case"down":default:return{opacity:1,y:0};case"left":case"right":return{opacity:1,x:0}}};return(0,d.jsx)(e.P.div,{className:b,initial:(()=>{switch(f){case"up":default:return{opacity:0,y:50};case"down":return{opacity:0,y:-50};case"left":return{opacity:0,x:50};case"right":return{opacity:0,x:-50}}})(),animate:h?i():void 0,whileInView:h?void 0:i(),viewport:h?void 0:{once:!0,amount:.3},transition:{duration:g,delay:c,ease:"easeOut"},children:a})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23084:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(37413);let e={title:"Kolhapuri Chappals Collection - Premium Handmade Footwear",description:"Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear. Handcrafted leather sandals for men, women, and children. Quality guaranteed.",keywords:"kolhapuri chappals collection, handmade footwear, leather sandals, traditional chappals, kolhapur footwear, authentic kolhapuri sandals, premium chappals",openGraph:{title:"Kolhapuri Chappals Collection - Utsav Footwear",description:"Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear. Handcrafted leather sandals for men, women, and children.",url:"https://utsavfootwear.com/products",images:[{url:"/kolhapuri1.jpg",width:1200,height:630,alt:"Kolhapuri Chappals Collection at Utsav Footwear"}]},alternates:{canonical:"https://utsavfootwear.com/products"}};function f({children:a}){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"CollectionPage",name:"Kolhapuri Chappals Collection",description:"Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear",url:"https://utsavfootwear.com/products",mainEntity:{"@type":"ItemList",name:"Kolhapuri Chappals",description:"Premium handcrafted Kolhapuri chappals and traditional footwear"},breadcrumb:{"@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,name:"Home",item:"https://utsavfootwear.com"},{"@type":"ListItem",position:2,name:"Products",item:"https://utsavfootwear.com/products"}]}})}}),a]})}},26703:(a,b,c)=>{Promise.resolve().then(c.bind(c,8547))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33784:(a,b,c)=>{"use strict";c.d(b,{OO:()=>j,j2:()=>i});var d=c(67989),e=c(61448),f=c(90555),g=c(70146);let h=(0,d.Wp)({apiKey:"AIzaSyBsEcUNPz8nDZyGRNU2bh1DpXPxnyV3b88",authDomain:"utsavfootwear-as.firebaseapp.com",databaseURL:"https://utsavfootwear-as-default-rtdb.firebaseio.com",projectId:"utsavfootwear-as",storageBucket:"utsavfootwear-as.firebasestorage.app",messagingSenderId:"74148351385",appId:"1:74148351385:web:ff9c1a3cff4395e3d19708",measurementId:"G-YW880XL7ME"}),i=(0,e.xI)(h),j=(0,f.C3)(h);(0,g.c7)(h)},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},34951:(a,b,c)=>{Promise.resolve().then(c.bind(c,70965))},35296:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(92576);let f={initial:{opacity:0,y:20},in:{opacity:1,y:0},out:{opacity:0,y:-20}},g={type:"tween",ease:"easeInOut",duration:.5};function h({children:a,className:b=""}){return(0,d.jsx)(e.P.div,{className:b,initial:"initial",animate:"in",exit:"out",variants:f,transition:g,children:a})}},35523:(a,b,c)=>{"use strict";c.d(b,{j:()=>i});var d=c(33784),e=c(90555);async function f(a){try{let b=new FormData;b.append("file",a);let c=await fetch("/api/uploadToCloudinary",{method:"POST",body:b});if(!c.ok)throw Error("Failed to upload image to Cloudinary");let d=await c.json();return{url:d.url,publicId:d.public_id}}catch(a){throw console.error("Error uploading to Cloudinary:",a),Error("Failed to upload image")}}async function g(a){try{if(!(await fetch("/api/deleteFromCloudinary",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({publicId:a})})).ok)throw Error("Failed to delete image from Cloudinary")}catch(a){throw console.error("Error deleting from Cloudinary:",a),Error("Failed to delete image")}}class h{async getAllProducts(){try{let a=await (0,e.Jt)(this.productsRef);if(a.exists()){let b=a.val(),c=[];return Object.keys(b).forEach(a=>{c.push({id:a,...b[a]})}),c}return[]}catch(a){throw console.error("Error fetching products:",a),Error("Failed to fetch products")}}async getPaginatedProducts(a,b){try{let c=await (0,e.Jt)(this.productsRef);if(c.exists()){let d=c.val(),e=[];Object.keys(d).forEach(a=>{e.push({id:a,...d[a]})});let f=(e=e.sort((a,b)=>{let c=a.updatedAt||a.createdAt||0;return(b.updatedAt||b.createdAt||0)-c})).length,g=(a-1)*b;return{products:e.slice(g,g+b),total:f}}return{products:[],total:0}}catch(a){throw console.error("Error fetching products:",a),Error("Failed to fetch products")}}async getProductById(a){try{let b=(0,e.KR)(d.OO,`products/${a}`),c=await (0,e.Jt)(b);if(c.exists())return{id:a,...c.val()};return null}catch(a){throw console.error("Error fetching product:",a),Error("Failed to fetch product")}}async addProduct(a,b){try{let{url:c,publicId:d}=await f(b),g=Date.now(),h={...a,imageUrl:c,publicId:d,createdAt:g,updatedAt:g},i=(0,e.VC)(this.productsRef);return await (0,e.hZ)(i,h),i.key}catch(a){throw console.error("Error adding product:",a),Error("Failed to add product")}}async updateProduct(a,b,c){try{let h=(0,e.KR)(d.OO,`products/${a}`),i=await this.getProductById(a);if(!i)throw Error("Product not found");let j=i.imageUrl,k=i.publicId;if(c){let{url:a,publicId:b}=await f(c);if(j=a,i.publicId)try{await g(i.publicId)}catch(a){console.error("Error deleting old image:",a)}k=b}let l={...i,...b,imageUrl:j,publicId:k,updatedAt:Date.now()};await (0,e.yo)(h,l)}catch(a){throw console.error("Error updating product:",a),Error("Failed to update product")}}async deleteProduct(a){try{let b=await this.getProductById(a);if(!b)throw Error("Product not found");if(b.publicId)try{await g(b.publicId)}catch(a){console.error("Error deleting image from Cloudinary:",a)}let c=(0,e.KR)(d.OO,`products/${a}`);await (0,e.TF)(c)}catch(a){throw console.error("Error deleting product:",a),Error("Failed to delete product")}}async searchProducts(a){try{let b=await this.getAllProducts(),c=a.toLowerCase();return b.filter(a=>a.name.toLowerCase().includes(c)||a.caption.toLowerCase().includes(c))}catch(a){throw console.error("Error searching products:",a),Error("Failed to search products")}}constructor(){this.productsRef=(0,e.KR)(d.OO,"products")}}let i=new h},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45521:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,8547)),"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\products\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,23084)),"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\products\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,98042)),"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\Data\\utsavfootwears\\src\\app\\products\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/products/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70965:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(60687),e=c(43210),f=c.n(e),g=c(35523),h=c(14973),i=c(35296),j=c(17310),k=c(92576);function l({size:a="md",color:b="text-blue-600",text:c="Loading..."}){return(0,d.jsxs)("div",{className:"flex flex-col items-center justify-center p-8",children:[(0,d.jsx)(k.P.div,{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[a]} ${b} mb-4`,animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,d.jsxs)("svg",{className:"w-full h-full",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),(0,d.jsx)(k.P.p,{className:`${{sm:"text-sm",md:"text-base",lg:"text-lg"}[a]} ${b} font-medium`,initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:c})]})}let m=[12,24,48,96];function n(){let[a,b]=(0,e.useState)([]),[c,k]=(0,e.useState)(!0),[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)(1),[r,s]=(0,e.useState)(12),[t,u]=(0,e.useState)(1),v=(0,e.useCallback)(async()=>{try{k(!0),o(null);let{products:a,total:c}=await g.j.getPaginatedProducts(p,r);b(a),u(Math.ceil(c/r))}catch(a){console.error("Error fetching products:",a),o("Failed to load products. Please try again later.")}finally{k(!1)}},[p,r]);return c?(0,d.jsx)(i.default,{className:"min-h-screen py-12",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)(l,{size:"lg",text:"Loading products..."})})}):n?(0,d.jsx)("div",{className:"min-h-screen py-12",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:n}),(0,d.jsx)("button",{onClick:v,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors",children:"Try Again"})]})})}):(0,d.jsxs)(i.default,{className:"min-h-screen py-12 relative bg-[url('/KCbg1.png')] bg-cover bg-center bg-no-repeat py-10",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-black/40 z-0"}),(0,d.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ",children:[(0,d.jsxs)(j.default,{className:"text-center mb-12",disableScrollAnimation:!0,children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Our Products"}),(0,d.jsxs)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:["Discover our kolahpuri chappal collection and quality footwear's.",(0,d.jsx)("br",{}),"Each pair is selected for comfort, style, and durability."]})]}),0===a.length?(0,d.jsxs)(j.default,{className:"text-center py-16",disableScrollAnimation:!0,children:[(0,d.jsx)("div",{className:"bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 transform transition-transform duration-300 hover:scale-110",children:(0,d.jsx)("span",{className:"text-4xl",children:"\uD83D\uDC5F"})}),(0,d.jsx)("h3",{className:"text-2xl font-semibold text-white mb-4",children:"No Products Yet"}),(0,d.jsx)("p",{className:"text-gray-600 max-w-md mx-auto",children:"We're currently updating our product gallery. Please check back soon to see our latest collection of quality footwear."})]}):(0,d.jsxs)(j.default,{disableScrollAnimation:!0,children:[(0,d.jsx)("div",{className:"flex justify-end mb-4",children:(0,d.jsx)("select",{value:r,onChange:a=>{s(Number(a.target.value)),q(1)},className:"bg-white text-gray-700 px-4 py-2 rounded-lg shadow border border-gray-200",children:m.map(a=>(0,d.jsxs)("option",{value:a,children:["Show ",a," per page"]},a))})}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:a.map(a=>(0,d.jsx)(h.A,{id:a.id,imageUrl:a.imageUrl,caption:a.caption,name:a.name,isAdmin:!1},a.id))}),t>1&&(0,d.jsxs)("div",{className:"mt-8 flex justify-center items-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>q(a=>Math.max(a-1,1)),disabled:1===p,className:"px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors","aria-label":"Previous page",children:"←"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:Array.from({length:t},(a,b)=>b+1).filter(a=>1===a||a===t||1>=Math.abs(a-p)).map((a,b,c)=>(0,d.jsxs)(f().Fragment,{children:[b>0&&c[b-1]!==a-1&&(0,d.jsx)("span",{className:"text-white",children:"..."}),(0,d.jsx)("button",{onClick:()=>q(a),className:`w-10 h-10 rounded-lg flex items-center justify-center ${p===a?"bg-blue-500 text-white":"bg-white text-gray-700 hover:bg-gray-50"} border border-gray-200 shadow transition-colors`,children:a})]},a))}),(0,d.jsx)("button",{onClick:()=>q(a=>Math.min(a+1,t)),disabled:p===t,className:"px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors","aria-label":"Next page",children:"→"})]}),t>1&&(0,d.jsx)("div",{className:"mt-4 text-center text-white text-sm",children:"Use ← and → arrow keys to navigate between pages"})]}),(0,d.jsx)(j.default,{className:"mt-16 text-center",delay:.3,disableScrollAnimation:!0,children:(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-8 transform transition-transform duration-300 hover:scale-105",children:[(0,d.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Interested in Our Products?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Visit our store to see our full collection, try on different styles, and get personalized fitting assistance from our knowledgeable staff."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("a",{href:"/about",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:"Store Locations"}),(0,d.jsx)("a",{href:"/contact",className:"border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg",children:"Contact Us"})]})]})})]})]})}},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,358,391,573],()=>b(b.s=45521));module.exports=c})();