"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateBlockListParams = void 0;
const validateBlockListParams = (list, watchVariable) => {
    if (!Array.isArray(list)) {
        throw 'The BlockList list has to be an array';
    }
    if (typeof watchVariable !== 'string') {
        throw 'The BlockList watchVariable has to be a string';
    }
};
exports.validateBlockListParams = validateBlockListParams;
